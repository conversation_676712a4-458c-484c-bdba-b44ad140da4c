{"name": "testApp", "version": "0.0.1", "private": true, "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "type-check": "tsc --project tsconfig.strict.json --noEmit", "type-check-all": "tsc --noEmit", "dev": "echo 'Dev build script - update as needed'", "prod": "echo 'Prod build script - update as needed'"}, "dependencies": {"@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@reduxjs/toolkit": "^2.5.0", "aes-js": "^3.1.2", "expo": "~51.0.0", "expo-camera": "~15.0.16", "expo-constants": "~16.0.2", "expo-file-system": "~17.0.1", "expo-image-picker": "~15.1.0", "expo-localization": "~15.0.3", "expo-secure-store": "~13.0.2", "expo-sqlite": "~14.0.6", "expo-status-bar": "~1.12.1", "moment": "^2.30.1", "object-hash": "^3.0.0", "react": "18.2.0", "react-native": "0.74.5", "react-native-actionsheet": "^2.4.2", "react-native-animatable": "^1.4.0", "react-native-gesture-handler": "~2.16.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "^13.0.1", "react-native-modal-dropdown": "^1.0.2", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-text": "file:cust_modules/react-native-text", "react-native-version-number": "file:cust_modules/react-native-version-number", "react-redux": "^9.2.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "string-natural-compare": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.79", "@types/react-native": "^0.72.8", "@types/react-redux": "^7.1.34", "@types/redux": "^3.6.31", "@types/redux-logger": "^3.0.13", "babel-jest": "^29.2.1", "babel-preset-expo": "~11.0.0", "jest": "^29.2.1", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "jest": {"preset": "react-native"}}