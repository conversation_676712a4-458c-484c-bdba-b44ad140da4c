{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "emmet.includeLanguages": {"typescript": "typescriptreact", "javascript": "javascriptreact"}, "files.associations": {"*.js": "javascriptreact", "*.jsx": "javascriptreact", "*.ts": "typescriptreact", "*.tsx": "typescriptreact"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.expo": true, "**/ios": true, "**/android": true}, "typescript.preferences.quoteStyle": "single", "javascript.preferences.quoteStyle": "single"}