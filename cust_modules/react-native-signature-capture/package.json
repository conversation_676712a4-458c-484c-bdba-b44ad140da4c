{"_args": [[{"raw": "react-native-signature-capture", "scope": null, "escapedName": "react-native-signature-capture", "name": "react-native-signature-capture", "rawSpec": "", "spec": "latest", "type": "tag"}, "/Users/<USER>/Documents/test-app"]], "_from": "react-native-signature-capture@latest", "_id": "react-native-signature-capture@0.4.6", "_inCache": true, "_location": "/react-native-signature-capture", "_nodeVersion": "6.10.0", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-native-signature-capture-0.4.6.tgz_1496312960237_0.6402933998033404"}, "_npmUser": {"name": "jedt", "email": "<EMAIL>"}, "_npmVersion": "3.10.10", "_phantomChildren": {}, "_requested": {"raw": "react-native-signature-capture", "scope": null, "escapedName": "react-native-signature-capture", "name": "react-native-signature-capture", "rawSpec": "", "spec": "latest", "type": "tag"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/react-native-signature-capture/-/react-native-signature-capture-0.4.6.tgz", "_shasum": "65f942446a8b315c89cb121045e411f0acf248bc", "_shrinkwrap": null, "_spec": "react-native-signature-capture", "_where": "/Users/<USER>/Documents/test-app", "author": {"name": "RepairShopr"}, "bugs": {"url": "https://github.com/RepairShopr/react-native-signature-capture/issues"}, "dependencies": {}, "description": "Lets users sign their signatures", "devDependencies": {}, "directories": {}, "dist": {"shasum": "65f942446a8b315c89cb121045e411f0acf248bc", "tarball": "https://registry.npmjs.org/react-native-signature-capture/-/react-native-signature-capture-0.4.6.tgz"}, "gitHead": "2e1bc1674a45dde17711209dde7eaefc9211f671", "homepage": "https://github.com/RepairShopr/react-native-signature-capture#readme", "keywords": ["react-component", "react-native", "ios", "signature"], "license": "ISC", "main": "SignatureCapture.js", "maintainers": [{"name": "jedt", "email": "<EMAIL>"}], "name": "react-native-signature-capture", "optionalDependencies": {}, "peerDependencies": {"react-native": ">=0.40"}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/RepairShopr/react-native-signature-capture.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "0.4.6"}