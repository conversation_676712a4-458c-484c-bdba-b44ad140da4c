# Example

The Example is a sample app of SignatureCapture.

## Running this app

Before running the app, make sure you run:

```sh
git clone https://github.com/RepairShopr/react-native-signature-capture.git
cd Example
npm install
```

### Running on iOS

Mac OS and Xcode are required.

+ Open `Example/ios/SignatureCaptureExample.xcodeproj` in Xcode
+ Hit the Run button

or

```sh
cd ./Example
react-native run-ios
```

### Running on Android

You'll need to have all the [prerequisites](https://github.com/facebook/react-native/tree/master/ReactAndroid#prerequisites) (SDK, NDK) for Building React Native installed.

Start an Android emulator ([Genymotion](https://www.genymotion.com/) is recommended).

```sh
cd ./Example
react-native run-android
```
Note: Building for the first time can take a while.

Open the Example app in your emulator.
