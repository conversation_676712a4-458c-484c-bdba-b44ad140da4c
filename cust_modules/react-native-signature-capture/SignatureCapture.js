/**
 * @providesModule cust/react-native-signature-capture
 */

'use strict';

var ReactNative = require('react-native');
var React = require('react');
var { PropTypes } = React;
var {
  requireNativeComponent,
	View,
	UIManager,
	DeviceEventEmitter,
	Platform
} = ReactNative;

class SignatureCapture extends React.Component {

	constructor() {
		super();
		this.onChange = this.onChange.bind(this);
		this.subscriptions = [];
	}

	onChange(event) {
		if (event.nativeEvent.pathName) {
			if (!this.props.onSaveEvent) {
				return;
			}
			this.props.onSaveEvent({
				pathName: event.nativeEvent.pathName,
				uri: event.nativeEvent.uri,
				// encoded: event.nativeEvent.encoded,
			});
		}

		if (event.nativeEvent.dragged) {
			if (!this.props.onDragEvent) {
				return;
			}
			this.props.onDragEvent({
				dragged: event.nativeEvent.dragged
			});
		}

		if (event.nativeEvent.paths) {
			if (!this.props.onSaveEvent) {
				return;
			}
			this.props.onSaveEvent({
				paths: event.nativeEvent.paths,
			});
		}
	}

	componentDidMount() {
		/*
			The event will be called directly from iOS,
			while for android it is handled in onChange
		 */
		if (this.props.onSaveEvent) {
			let sub = DeviceEventEmitter.addListener(
				'onSaveEvent',
				this.props.onSaveEvent
			);
			this.subscriptions.push(sub);
		}

		if (this.props.onDragEvent) {
			let sub = DeviceEventEmitter.addListener(
				'onDragEvent',
				this.props.onDragEvent
			);
			this.subscriptions.push(sub);
		}
	}

	componentWillUnmount() {
		this.subscriptions.forEach(sub => sub.remove());
		this.subscriptions = [];
		
		this.removeImage();
	}

	render() {
		return <RSSignatureView {...this.props} onChange={this.onChange} />;
	}

	saveAnnotations() {
		UIManager.dispatchViewManagerCommand(
			ReactNative.findNodeHandle(this),
			UIManager.RSSignatureView.Commands.saveAnnotations,
			[],
		);
	}

	saveImage() {
		UIManager.dispatchViewManagerCommand(
			ReactNative.findNodeHandle(this),
			UIManager.RSSignatureView.Commands.saveImage,
			[],
		);
	}

	resetImage() {
		UIManager.dispatchViewManagerCommand(
			ReactNative.findNodeHandle(this),
			UIManager.RSSignatureView.Commands.resetImage,
			[],
		);
	}

	removeImage() {
		UIManager.dispatchViewManagerCommand(
			ReactNative.findNodeHandle(this),
			UIManager.RSSignatureView.Commands.removeImage,
			[],
		);
	}

	clearImage() {
		UIManager.dispatchViewManagerCommand(
			ReactNative.findNodeHandle(this),
			UIManager.RSSignatureView.Commands.clearImage,
			[],
		);
	}

	undo() {
		UIManager.dispatchViewManagerCommand(
			ReactNative.findNodeHandle(this),
			UIManager.RSSignatureView.Commands.undo,
			[],
		);
	}
}

SignatureCapture.propTypes = {
	...View.propTypes,
	rotateClockwise: PropTypes.bool,
	square: PropTypes.bool,
	saveImageFileInExtStorage: PropTypes.bool,
	viewMode: PropTypes.string,
	maxSize: PropTypes.number,
	imageFilePath: PropTypes.string,
	strokeColor: PropTypes.string,
	editMode: PropTypes.number,
	isDrawing: PropTypes.bool,
	isErase: PropTypes.bool,
	floorPlanMode: PropTypes.bool,
	annotations: PropTypes.array
};

var RSSignatureView = requireNativeComponent('RSSignatureView', SignatureCapture, { nativeOnly: { onChange: true } });

module.exports = SignatureCapture;
