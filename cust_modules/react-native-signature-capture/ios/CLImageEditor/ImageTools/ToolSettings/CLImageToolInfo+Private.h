//
//  CLImageToolInfo+Private.h
//
//  Created by s<PERSON> <PERSON><PERSON><PERSON><PERSON> on 2013/12/07.
//  Copyright (c) 2013年 CALACULU. All rights reserved.
//

#import "../../CLImageToolInfo.h"

@protocol CLImageToolProtocol;

@interface CLImageToolInfo (Private)

+ (CLImageToolInfo*)toolInfoForToolClass:(Class<CLImageToolProtocol>)toolClass;
+ (NSArray*)toolsWithToolClass:(Class<CLImageToolProtocol>)toolClass;

@end
