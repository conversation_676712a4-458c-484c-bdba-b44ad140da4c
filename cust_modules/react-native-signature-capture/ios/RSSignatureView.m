#import "RSSignatureView.h"
#import <React/RCTConvert.h>
#import <UIKit/UIKit.h>
#import <QuartzCore/QuartzCore.h>
#import "PPSSignatureView.h"
#import "RSSignatureViewManager.h"

#define DEGREES_TO_RADIANS(x) (M_PI * (x) / 180.0)

@implementation RSSignatureView {
	CAShapeLayer *_border;
	BOOL _loaded;
	EAGLContext *_context;
	UIButton *saveButton;
	UIButton *clearButton;
	UILabel *titleLabel;
	BOOL _rotateClockwise;
	BOOL _square;
	BOOL _showNativeButtons;
    BOOL _showTitleLabel;
    
    NSString *imageFilePath;
    UIColor *strokeColor;
    BOOL isDrawing;
    BOOL isErase;
    BOOL floorPlanMode;
    BOOL editMode;
    NSArray *annotations;
    
    CLImageEditor *editor;
}

@synthesize sign;
@synthesize manager;

- (instancetype)init
{
	_showNativeButtons = NO;
    _showTitleLabel = NO;
	if ((self = [super init])) {
		_border = [CAShapeLayer layer];
		_border.strokeColor = [UIColor blackColor].CGColor;
		_border.fillColor = nil;
		_border.lineDashPattern = @[@4, @2];

		[self.layer addSublayer:_border];
	}

	return self;
}

- (void) didRotate:(NSNotification *)notification {
	int ori=1;
	UIDeviceOrientation currOri = [[UIDevice currentDevice] orientation];
	if ((currOri == UIDeviceOrientationLandscapeLeft) || (currOri == UIDeviceOrientationLandscapeRight)) {
		ori=0;
	}
}
    
    
- (void)imageEditorDidCancel:(CLImageEditor *)editor
{
    
}
    
- (void)imageEditor:(CLImageEditor *)editor didFinishEditingWithImage:(UIImage *)image
{

}

- (void)layoutSubviews
{
	[super layoutSubviews];
	if (!_loaded) {

		[[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didRotate:)
																								 name:UIDeviceOrientationDidChangeNotification object:nil];

		_context = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES2];

		CGSize screen = self.bounds.size;
        
        UIImage *image = [UIImage imageWithContentsOfFile:[imageFilePath stringByReplacingOccurrencesOfString:@"file://" withString:@""]];
        editor = [[CLImageEditor alloc] initWithImage:image];
        editor.isFloorPlan = floorPlanMode;
        editor.defaultAnnotations = annotations;
        
        [editor.view setFrame:CGRectMake(0, 0, screen.width, screen.height)];
        
        if (editMode) {
            [editor displayMode:editMode];
        }
        
        [self addSubview:editor.view];
	}
	_loaded = true;
	_border.path = [UIBezierPath bezierPathWithRect:self.bounds].CGPath;
	_border.frame = self.bounds;
}

- (void)setRotateClockwise:(BOOL)rotateClockwise {
	_rotateClockwise = rotateClockwise;
}

- (void)setSquare:(BOOL)square {
	_square = square;
}

- (void)setShowNativeButtons:(BOOL)showNativeButtons {
	_showNativeButtons = showNativeButtons;
}

- (void)setShowTitleLabel:(BOOL)showTitleLabel {
    _showTitleLabel = showTitleLabel;
}
    
- (void)setStrokeColor:(NSString *)sColor {
    strokeColor = [UIColor redColor];
}
    
- (void)setFloorPlanMode:(BOOL)mode {
    floorPlanMode = mode;
}

- (void)setAnnotations: (NSArray*)_annotations {
    annotations = _annotations;
}
    
- (void)setIsDrawing:(BOOL)drawing {
    isDrawing = drawing;
    [editor changeMode:drawing];
}
    
- (void)setIsErase:(BOOL)erase {
    isErase = erase;
}

- (void)setEditMode:(BOOL)display {
    editMode = !display;
}
    
- (void)setImageFilePath:(NSString *)path {
    imageFilePath = path;
}

-(void) onSaveButtonPressed {
	[self saveImage];
}

-(void) saveImage {
	saveButton.hidden = YES;
	clearButton.hidden = YES;
	UIImage *signImage = [self.sign signatureImage: _rotateClockwise withSquare:_square];

	saveButton.hidden = NO;
	clearButton.hidden = NO;

    NSError *error;

	NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
	NSString *documentsDirectory = [paths firstObject];
	NSString *tempPath = [documentsDirectory stringByAppendingFormat:@"/%f_editPhoto.png", [[NSDate date] timeIntervalSince1970]];

	//remove if file already exists
	if ([[NSFileManager defaultManager] fileExistsAtPath:tempPath]) {
        [[NSFileManager defaultManager] removeItemAtPath:tempPath error:&error];
        if (error) {
            NSLog(@"Error: %@", error.debugDescription);
        }
	}

	// Convert UIImage object into NSData (a wrapper for a stream of bytes) formatted according to PNG spec
	NSData *imageData = UIImagePNGRepresentation(signImage);
	BOOL isSuccess = [imageData writeToFile:tempPath atomically:YES];
	if (isSuccess) {
        NSFileManager *man = [NSFileManager defaultManager];
        NSDictionary *attrs = [man attributesOfItemAtPath:tempPath error: NULL];
        UInt32 result = [attrs fileSize];

		NSString *base64Encoded = [imageData base64EncodedStringWithOptions:0];
		[self.manager publishSaveImageEvent: tempPath withEncoded:base64Encoded];
	}
}

-(NSArray*) getAnnotations {
    return [editor getAnnotations];
}

-(NSString*) saveAndGetImagePath {
    
    UIImage *signImage = [editor saveImage];
    
    if (signImage != nil) {
        NSError *error;
        
        NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
        NSString *documentsDirectory = [paths firstObject];
        NSString *tempPath = [documentsDirectory stringByAppendingFormat:@"/%f_editPhoto.jpg", [[NSDate date] timeIntervalSince1970]];
        
        // try another name when the file is already exist
        while ([[NSFileManager defaultManager] fileExistsAtPath:tempPath]) {
            tempPath = [documentsDirectory stringByAppendingFormat:@"/%f_editPhoto.jpg", [[NSDate date] timeIntervalSince1970]];
        }
        
        // Convert UIImage object into NSData (a wrapper for a stream of bytes) formatted according to PNG spec
//        NSData *imageData = UIImagePNGRepresentation(signImage);
        NSData *imageData = UIImageJPEGRepresentation(signImage, 0.7f);
        BOOL isSuccess = [imageData writeToFile:tempPath atomically:YES];
        if (isSuccess) {
            return tempPath;
        }
    }
    
    return @"";
}

-(void) onClearButtonPressed {
    //	[self erase];
    [editor resetImage];
}

-(void) erase {
    //	[self.sign erase];
    if (floorPlanMode) {
        annotations = [NSMutableArray array];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"CLEAR_ANNOTATIONS" object:nil];
    }
    [editor resetImage];
}


@end
