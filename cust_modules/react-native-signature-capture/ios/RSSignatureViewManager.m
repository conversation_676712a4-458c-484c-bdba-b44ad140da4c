#import "RSSignatureViewManager.h"
#import <React/RCTBridgeModule.h>
#import <React/RCTBridge.h>
#import <React/RCTEventDispatcher.h>

@implementation RSSignatureViewManager {
    NSMutableArray *viewArr;
}

@synthesize bridge = _bridge;
@synthesize signView;

RCT_EXPORT_MODULE()

RCT_EXPORT_VIEW_PROPERTY(rotateClockwise, BOOL)
RCT_EXPORT_VIEW_PROPERTY(square, BOOL)
//RCT_EXPORT_VIEW_PROPERTY(showTitleLabel, BOOL)
    
RCT_EXPORT_VIEW_PROPERTY(imageFilePath, NSString)
RCT_EXPORT_VIEW_PROPERTY(strokeColor, NSString)
RCT_EXPORT_VIEW_PROPERTY(isDrawing, BOOL)
RCT_EXPORT_VIEW_PROPERTY(isErase, BOOL)
RCT_EXPORT_VIEW_PROPERTY(floorPlanMode, BOOL)
RCT_EXPORT_VIEW_PROPERTY(editMode, BOOL)
RCT_EXPORT_VIEW_PROPERTY(annotations, NSArray)

-(dispatch_queue_t) methodQueue
{
	return dispatch_get_main_queue();
}

-(UIView *) view
{
	self.signView = [[RSSignatureView alloc] init];
	self.signView.manager = self;
    
    if (viewArr == nil) {
        viewArr = [[NSMutableArray alloc] init];
    }
    [viewArr addObject:self.signView];
    
	return signView;
}

// Both of these methods needs to be called from the main thread so the
// UI can clear out the signature.
RCT_EXPORT_METHOD(saveImage:(nonnull NSNumber *)reactTag) {
	dispatch_async(dispatch_get_main_queue(), ^{
        NSMutableArray *pathArr = [NSMutableArray array];
        for (RSSignatureView *view in viewArr) {
            NSString *path = [view saveAndGetImagePath];
            [pathArr addObject:path];
        }
        [self.bridge.eventDispatcher
         sendDeviceEventWithName:@"onSaveEvent"
         body:@{
                @"paths":pathArr
            }];
	});
}

RCT_EXPORT_METHOD(saveAnnotations:(nonnull NSNumber *)reactTag) {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSMutableArray *annotationsArr = [NSMutableArray array];
        for (RSSignatureView *view in viewArr) {
            NSArray *annotations = [view getAnnotations];
            [annotationsArr addObject:annotations];
        }
        [self.bridge.eventDispatcher
         sendDeviceEventWithName:@"onSaveEvent"
         body:@{
                @"paths":annotationsArr
                }];
    });
}

RCT_EXPORT_METHOD(resetImage:(nonnull NSNumber *)reactTag) {
	dispatch_async(dispatch_get_main_queue(), ^{
        for (RSSignatureView *view in viewArr) {
            if ([view.reactTag isEqualToNumber:reactTag]) {
                [view erase];
            }
        }
	});
}

RCT_EXPORT_METHOD(removeImage:(nonnull NSNumber *)reactTag) {
    dispatch_async(dispatch_get_main_queue(), ^{
        RSSignatureView *targetView = nil;
        for (RSSignatureView *view in viewArr) {
            if ([view.reactTag isEqualToNumber:reactTag]) {
                targetView = view;
            }
        }
        if (targetView != nil) {
            [viewArr removeObject:targetView];
        }
    });
}

RCT_EXPORT_METHOD(clearImage:(nonnull NSNumber *)reactTag) {
    dispatch_async(dispatch_get_main_queue(), ^{
        viewArr = [[NSMutableArray alloc] init];
    });
}

-(void) publishSaveImageEvent:(NSString *) aTempPath withEncoded: (NSString *) aEncoded {
	[self.bridge.eventDispatcher
	 sendDeviceEventWithName:@"onSaveEvent"
	 body:@{
					@"pathName": aTempPath,
					@"encoded": aEncoded
					}];
}

-(void) publishDraggedEvent {
	[self.bridge.eventDispatcher
	 sendDeviceEventWithName:@"onDragEvent"
	 body:@{@"dragged": @YES}];
}

- (void)dealloc {
    NSLog(@"dealloc");
}

@end
