// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0C53A9EE1B56134F004F2642 /* RSSignatureViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0C53A9ED1B56134F004F2642 /* RSSignatureViewManager.m */; };
		0C53A9F31B5613AF004F2642 /* PPSSignatureView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0C53A9F01B5613AF004F2642 /* PPSSignatureView.m */; };
		0C53A9F41B5613AF004F2642 /* RSSignatureView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0C53A9F21B5613AF004F2642 /* RSSignatureView.m */; };
		8D399F161F567C9900120537 /* CLImageEditor.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EC61F567C9900120537 /* CLImageEditor.m */; };
		8D399F171F567C9900120537 /* CLImageEditorTheme.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EC81F567C9900120537 /* CLImageEditorTheme.m */; };
		8D399F181F567C9900120537 /* CLImageToolInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399ECA1F567C9900120537 /* CLImageToolInfo.m */; };
		8D399F191F567C9900120537 /* CLAdjustmentTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399ECE1F567C9900120537 /* CLAdjustmentTool.m */; };
		8D399F1A1F567C9900120537 /* CLBlurTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399ED11F567C9900120537 /* CLBlurTool.m */; };
		8D399F1B1F567C9900120537 /* CLClippingTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399ED41F567C9900120537 /* CLClippingTool.m */; };
		8D399F1C1F567C9900120537 /* CLDrawTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399ED71F567C9900120537 /* CLDrawTool.m */; };
		8D399F1D1F567C9900120537 /* CLBloomEffect.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EDB1F567C9900120537 /* CLBloomEffect.m */; };
		8D399F1E1F567C9900120537 /* CLGloomEffect.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EDD1F567C9900120537 /* CLGloomEffect.m */; };
		8D399F1F1F567C9900120537 /* CLHighlightShadowEffect.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EDF1F567C9900120537 /* CLHighlightShadowEffect.m */; };
		8D399F201F567C9900120537 /* CLHueEffect.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EE11F567C9900120537 /* CLHueEffect.m */; };
		8D399F211F567C9900120537 /* CLPixellateEffect.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EE31F567C9900120537 /* CLPixellateEffect.m */; };
		8D399F221F567C9900120537 /* CLPosterizeEffect.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EE51F567C9900120537 /* CLPosterizeEffect.m */; };
		8D399F231F567C9900120537 /* CLSpotEffect.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EE71F567C9900120537 /* CLSpotEffect.m */; };
		8D399F241F567C9900120537 /* CLEffectBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EE91F567C9900120537 /* CLEffectBase.m */; };
		8D399F251F567C9900120537 /* CLEffectTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EEB1F567C9900120537 /* CLEffectTool.m */; };
		8D399F261F567C9900120537 /* CLFilterBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EEE1F567C9900120537 /* CLFilterBase.m */; };
		8D399F271F567C9900120537 /* CLFilterTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EF01F567C9900120537 /* CLFilterTool.m */; };
		8D399F281F567C9900120537 /* CLImageToolBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EF21F567C9900120537 /* CLImageToolBase.m */; };
		8D399F291F567C9900120537 /* CLRotateTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EF51F567C9900120537 /* CLRotateTool.m */; };
		8D399F2A1F567C9900120537 /* CLToneCurveTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EF81F567C9900120537 /* CLToneCurveTool.m */; };
		8D399F2B1F567C9900120537 /* CLCircleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EFB1F567C9900120537 /* CLCircleView.m */; };
		8D399F2C1F567C9900120537 /* CLColorPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EFD1F567C9900120537 /* CLColorPickerView.m */; };
		8D399F2D1F567C9900120537 /* CLImageEditorTheme+Private.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399EFF1F567C9900120537 /* CLImageEditorTheme+Private.m */; };
		8D399F2E1F567C9900120537 /* CLImageToolInfo+Private.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F011F567C9900120537 /* CLImageToolInfo+Private.m */; };
		8D399F2F1F567C9900120537 /* CLToolbarMenuItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F051F567C9900120537 /* CLToolbarMenuItem.m */; };
		8D399F301F567C9900120537 /* UIView+CLImageToolInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F071F567C9900120537 /* UIView+CLImageToolInfo.m */; };
		8D399F311F567C9900120537 /* CLClassList.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F0A1F567C9900120537 /* CLClassList.m */; };
		8D399F321F567C9900120537 /* CLSplineInterpolator.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F0C1F567C9900120537 /* CLSplineInterpolator.m */; };
		8D399F331F567C9900120537 /* UIDevice+SystemVersion.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F0E1F567C9900120537 /* UIDevice+SystemVersion.m */; };
		8D399F341F567C9900120537 /* UIImage+Utility.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F101F567C9900120537 /* UIImage+Utility.m */; };
		8D399F351F567C9900120537 /* UIView+Frame.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F121F567C9900120537 /* UIView+Frame.m */; };
		8D399F361F567C9900120537 /* _CLImageEditorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D399F151F567C9900120537 /* _CLImageEditorViewController.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		0C53A99C1B56113D004F2642 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0C53A99E1B56113D004F2642 /* libRSSignatureCapture.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRSSignatureCapture.a; sourceTree = BUILT_PRODUCTS_DIR; };
		0C53A9EC1B56134F004F2642 /* RSSignatureViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RSSignatureViewManager.h; sourceTree = "<group>"; };
		0C53A9ED1B56134F004F2642 /* RSSignatureViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RSSignatureViewManager.m; sourceTree = "<group>"; };
		0C53A9EF1B5613AF004F2642 /* PPSSignatureView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPSSignatureView.h; sourceTree = "<group>"; };
		0C53A9F01B5613AF004F2642 /* PPSSignatureView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPSSignatureView.m; sourceTree = "<group>"; };
		0C53A9F11B5613AF004F2642 /* RSSignatureView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RSSignatureView.h; sourceTree = "<group>"; };
		0C53A9F21B5613AF004F2642 /* RSSignatureView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RSSignatureView.m; sourceTree = "<group>"; };
		8D399EC41F567C9900120537 /* CLImageEditor.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = CLImageEditor.bundle; sourceTree = "<group>"; };
		8D399EC51F567C9900120537 /* CLImageEditor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLImageEditor.h; sourceTree = "<group>"; };
		8D399EC61F567C9900120537 /* CLImageEditor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLImageEditor.m; sourceTree = "<group>"; };
		8D399EC71F567C9900120537 /* CLImageEditorTheme.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLImageEditorTheme.h; sourceTree = "<group>"; };
		8D399EC81F567C9900120537 /* CLImageEditorTheme.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLImageEditorTheme.m; sourceTree = "<group>"; };
		8D399EC91F567C9900120537 /* CLImageToolInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLImageToolInfo.h; sourceTree = "<group>"; };
		8D399ECA1F567C9900120537 /* CLImageToolInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLImageToolInfo.m; sourceTree = "<group>"; };
		8D399ECD1F567C9900120537 /* CLAdjustmentTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLAdjustmentTool.h; sourceTree = "<group>"; };
		8D399ECE1F567C9900120537 /* CLAdjustmentTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLAdjustmentTool.m; sourceTree = "<group>"; };
		8D399ED01F567C9900120537 /* CLBlurTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLBlurTool.h; sourceTree = "<group>"; };
		8D399ED11F567C9900120537 /* CLBlurTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLBlurTool.m; sourceTree = "<group>"; };
		8D399ED31F567C9900120537 /* CLClippingTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLClippingTool.h; sourceTree = "<group>"; };
		8D399ED41F567C9900120537 /* CLClippingTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLClippingTool.m; sourceTree = "<group>"; };
		8D399ED61F567C9900120537 /* CLDrawTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLDrawTool.h; sourceTree = "<group>"; };
		8D399ED71F567C9900120537 /* CLDrawTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLDrawTool.m; sourceTree = "<group>"; };
		8D399EDA1F567C9900120537 /* CLBloomEffect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLBloomEffect.h; sourceTree = "<group>"; };
		8D399EDB1F567C9900120537 /* CLBloomEffect.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLBloomEffect.m; sourceTree = "<group>"; };
		8D399EDC1F567C9900120537 /* CLGloomEffect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLGloomEffect.h; sourceTree = "<group>"; };
		8D399EDD1F567C9900120537 /* CLGloomEffect.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLGloomEffect.m; sourceTree = "<group>"; };
		8D399EDE1F567C9900120537 /* CLHighlightShadowEffect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLHighlightShadowEffect.h; sourceTree = "<group>"; };
		8D399EDF1F567C9900120537 /* CLHighlightShadowEffect.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLHighlightShadowEffect.m; sourceTree = "<group>"; };
		8D399EE01F567C9900120537 /* CLHueEffect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLHueEffect.h; sourceTree = "<group>"; };
		8D399EE11F567C9900120537 /* CLHueEffect.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLHueEffect.m; sourceTree = "<group>"; };
		8D399EE21F567C9900120537 /* CLPixellateEffect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLPixellateEffect.h; sourceTree = "<group>"; };
		8D399EE31F567C9900120537 /* CLPixellateEffect.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLPixellateEffect.m; sourceTree = "<group>"; };
		8D399EE41F567C9900120537 /* CLPosterizeEffect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLPosterizeEffect.h; sourceTree = "<group>"; };
		8D399EE51F567C9900120537 /* CLPosterizeEffect.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLPosterizeEffect.m; sourceTree = "<group>"; };
		8D399EE61F567C9900120537 /* CLSpotEffect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLSpotEffect.h; sourceTree = "<group>"; };
		8D399EE71F567C9900120537 /* CLSpotEffect.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLSpotEffect.m; sourceTree = "<group>"; };
		8D399EE81F567C9900120537 /* CLEffectBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLEffectBase.h; sourceTree = "<group>"; };
		8D399EE91F567C9900120537 /* CLEffectBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLEffectBase.m; sourceTree = "<group>"; };
		8D399EEA1F567C9900120537 /* CLEffectTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLEffectTool.h; sourceTree = "<group>"; };
		8D399EEB1F567C9900120537 /* CLEffectTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLEffectTool.m; sourceTree = "<group>"; };
		8D399EED1F567C9900120537 /* CLFilterBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLFilterBase.h; sourceTree = "<group>"; };
		8D399EEE1F567C9900120537 /* CLFilterBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLFilterBase.m; sourceTree = "<group>"; };
		8D399EEF1F567C9900120537 /* CLFilterTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLFilterTool.h; sourceTree = "<group>"; };
		8D399EF01F567C9900120537 /* CLFilterTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLFilterTool.m; sourceTree = "<group>"; };
		8D399EF11F567C9900120537 /* CLImageToolBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLImageToolBase.h; sourceTree = "<group>"; };
		8D399EF21F567C9900120537 /* CLImageToolBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLImageToolBase.m; sourceTree = "<group>"; };
		8D399EF41F567C9900120537 /* CLRotateTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLRotateTool.h; sourceTree = "<group>"; };
		8D399EF51F567C9900120537 /* CLRotateTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLRotateTool.m; sourceTree = "<group>"; };
		8D399EF71F567C9900120537 /* CLToneCurveTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLToneCurveTool.h; sourceTree = "<group>"; };
		8D399EF81F567C9900120537 /* CLToneCurveTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLToneCurveTool.m; sourceTree = "<group>"; };
		8D399EFA1F567C9900120537 /* CLCircleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLCircleView.h; sourceTree = "<group>"; };
		8D399EFB1F567C9900120537 /* CLCircleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLCircleView.m; sourceTree = "<group>"; };
		8D399EFC1F567C9900120537 /* CLColorPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLColorPickerView.h; sourceTree = "<group>"; };
		8D399EFD1F567C9900120537 /* CLColorPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLColorPickerView.m; sourceTree = "<group>"; };
		8D399EFE1F567C9900120537 /* CLImageEditorTheme+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "CLImageEditorTheme+Private.h"; sourceTree = "<group>"; };
		8D399EFF1F567C9900120537 /* CLImageEditorTheme+Private.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "CLImageEditorTheme+Private.m"; sourceTree = "<group>"; };
		8D399F001F567C9900120537 /* CLImageToolInfo+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "CLImageToolInfo+Private.h"; sourceTree = "<group>"; };
		8D399F011F567C9900120537 /* CLImageToolInfo+Private.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "CLImageToolInfo+Private.m"; sourceTree = "<group>"; };
		8D399F021F567C9900120537 /* CLImageToolProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLImageToolProtocol.h; sourceTree = "<group>"; };
		8D399F031F567C9900120537 /* CLImageToolSettings.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLImageToolSettings.h; sourceTree = "<group>"; };
		8D399F041F567C9900120537 /* CLToolbarMenuItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLToolbarMenuItem.h; sourceTree = "<group>"; };
		8D399F051F567C9900120537 /* CLToolbarMenuItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLToolbarMenuItem.m; sourceTree = "<group>"; };
		8D399F061F567C9900120537 /* UIView+CLImageToolInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+CLImageToolInfo.h"; sourceTree = "<group>"; };
		8D399F071F567C9900120537 /* UIView+CLImageToolInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+CLImageToolInfo.m"; sourceTree = "<group>"; };
		8D399F091F567C9900120537 /* CLClassList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLClassList.h; sourceTree = "<group>"; };
		8D399F0A1F567C9900120537 /* CLClassList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLClassList.m; sourceTree = "<group>"; };
		8D399F0B1F567C9900120537 /* CLSplineInterpolator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CLSplineInterpolator.h; sourceTree = "<group>"; };
		8D399F0C1F567C9900120537 /* CLSplineInterpolator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CLSplineInterpolator.m; sourceTree = "<group>"; };
		8D399F0D1F567C9900120537 /* UIDevice+SystemVersion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIDevice+SystemVersion.h"; sourceTree = "<group>"; };
		8D399F0E1F567C9900120537 /* UIDevice+SystemVersion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+SystemVersion.m"; sourceTree = "<group>"; };
		8D399F0F1F567C9900120537 /* UIImage+Utility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Utility.h"; sourceTree = "<group>"; };
		8D399F101F567C9900120537 /* UIImage+Utility.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Utility.m"; sourceTree = "<group>"; };
		8D399F111F567C9900120537 /* UIView+Frame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Frame.h"; sourceTree = "<group>"; };
		8D399F121F567C9900120537 /* UIView+Frame.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Frame.m"; sourceTree = "<group>"; };
		8D399F141F567C9900120537 /* _CLImageEditorViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _CLImageEditorViewController.h; sourceTree = "<group>"; };
		8D399F151F567C9900120537 /* _CLImageEditorViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = _CLImageEditorViewController.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0C53A99B1B56113D004F2642 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0C53A9951B56113D004F2642 = {
			isa = PBXGroup;
			children = (
				8D399EC31F567C9900120537 /* CLImageEditor */,
				0C53A9EF1B5613AF004F2642 /* PPSSignatureView.h */,
				0C53A9F01B5613AF004F2642 /* PPSSignatureView.m */,
				0C53A9F11B5613AF004F2642 /* RSSignatureView.h */,
				0C53A9F21B5613AF004F2642 /* RSSignatureView.m */,
				0C53A9EC1B56134F004F2642 /* RSSignatureViewManager.h */,
				0C53A9ED1B56134F004F2642 /* RSSignatureViewManager.m */,
				0C53A99F1B56113D004F2642 /* Products */,
			);
			sourceTree = "<group>";
		};
		0C53A99F1B56113D004F2642 /* Products */ = {
			isa = PBXGroup;
			children = (
				0C53A99E1B56113D004F2642 /* libRSSignatureCapture.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8D399EC31F567C9900120537 /* CLImageEditor */ = {
			isa = PBXGroup;
			children = (
				8D399EC41F567C9900120537 /* CLImageEditor.bundle */,
				8D399EC51F567C9900120537 /* CLImageEditor.h */,
				8D399EC61F567C9900120537 /* CLImageEditor.m */,
				8D399EC71F567C9900120537 /* CLImageEditorTheme.h */,
				8D399EC81F567C9900120537 /* CLImageEditorTheme.m */,
				8D399EC91F567C9900120537 /* CLImageToolInfo.h */,
				8D399ECA1F567C9900120537 /* CLImageToolInfo.m */,
				8D399ECB1F567C9900120537 /* ImageTools */,
				8D399F081F567C9900120537 /* Utils */,
				8D399F131F567C9900120537 /* ViewController */,
			);
			path = CLImageEditor;
			sourceTree = "<group>";
		};
		8D399ECB1F567C9900120537 /* ImageTools */ = {
			isa = PBXGroup;
			children = (
				8D399ECC1F567C9900120537 /* CLAdjustmentTool */,
				8D399ECF1F567C9900120537 /* CLBlurTool */,
				8D399ED21F567C9900120537 /* CLClippingTool */,
				8D399ED51F567C9900120537 /* CLDrawTool */,
				8D399ED81F567C9900120537 /* CLEffectTool */,
				8D399EEC1F567C9900120537 /* CLFilterTool */,
				8D399EF11F567C9900120537 /* CLImageToolBase.h */,
				8D399EF21F567C9900120537 /* CLImageToolBase.m */,
				8D399EF31F567C9900120537 /* CLRotateTool */,
				8D399EF61F567C9900120537 /* CLToneCurveTool */,
				8D399EF91F567C9900120537 /* ToolSettings */,
			);
			path = ImageTools;
			sourceTree = "<group>";
		};
		8D399ECC1F567C9900120537 /* CLAdjustmentTool */ = {
			isa = PBXGroup;
			children = (
				8D399ECD1F567C9900120537 /* CLAdjustmentTool.h */,
				8D399ECE1F567C9900120537 /* CLAdjustmentTool.m */,
			);
			path = CLAdjustmentTool;
			sourceTree = "<group>";
		};
		8D399ECF1F567C9900120537 /* CLBlurTool */ = {
			isa = PBXGroup;
			children = (
				8D399ED01F567C9900120537 /* CLBlurTool.h */,
				8D399ED11F567C9900120537 /* CLBlurTool.m */,
			);
			path = CLBlurTool;
			sourceTree = "<group>";
		};
		8D399ED21F567C9900120537 /* CLClippingTool */ = {
			isa = PBXGroup;
			children = (
				8D399ED31F567C9900120537 /* CLClippingTool.h */,
				8D399ED41F567C9900120537 /* CLClippingTool.m */,
			);
			path = CLClippingTool;
			sourceTree = "<group>";
		};
		8D399ED51F567C9900120537 /* CLDrawTool */ = {
			isa = PBXGroup;
			children = (
				8D399ED61F567C9900120537 /* CLDrawTool.h */,
				8D399ED71F567C9900120537 /* CLDrawTool.m */,
			);
			path = CLDrawTool;
			sourceTree = "<group>";
		};
		8D399ED81F567C9900120537 /* CLEffectTool */ = {
			isa = PBXGroup;
			children = (
				8D399ED91F567C9900120537 /* CLEffect */,
				8D399EE81F567C9900120537 /* CLEffectBase.h */,
				8D399EE91F567C9900120537 /* CLEffectBase.m */,
				8D399EEA1F567C9900120537 /* CLEffectTool.h */,
				8D399EEB1F567C9900120537 /* CLEffectTool.m */,
			);
			path = CLEffectTool;
			sourceTree = "<group>";
		};
		8D399ED91F567C9900120537 /* CLEffect */ = {
			isa = PBXGroup;
			children = (
				8D399EDA1F567C9900120537 /* CLBloomEffect.h */,
				8D399EDB1F567C9900120537 /* CLBloomEffect.m */,
				8D399EDC1F567C9900120537 /* CLGloomEffect.h */,
				8D399EDD1F567C9900120537 /* CLGloomEffect.m */,
				8D399EDE1F567C9900120537 /* CLHighlightShadowEffect.h */,
				8D399EDF1F567C9900120537 /* CLHighlightShadowEffect.m */,
				8D399EE01F567C9900120537 /* CLHueEffect.h */,
				8D399EE11F567C9900120537 /* CLHueEffect.m */,
				8D399EE21F567C9900120537 /* CLPixellateEffect.h */,
				8D399EE31F567C9900120537 /* CLPixellateEffect.m */,
				8D399EE41F567C9900120537 /* CLPosterizeEffect.h */,
				8D399EE51F567C9900120537 /* CLPosterizeEffect.m */,
				8D399EE61F567C9900120537 /* CLSpotEffect.h */,
				8D399EE71F567C9900120537 /* CLSpotEffect.m */,
			);
			path = CLEffect;
			sourceTree = "<group>";
		};
		8D399EEC1F567C9900120537 /* CLFilterTool */ = {
			isa = PBXGroup;
			children = (
				8D399EED1F567C9900120537 /* CLFilterBase.h */,
				8D399EEE1F567C9900120537 /* CLFilterBase.m */,
				8D399EEF1F567C9900120537 /* CLFilterTool.h */,
				8D399EF01F567C9900120537 /* CLFilterTool.m */,
			);
			path = CLFilterTool;
			sourceTree = "<group>";
		};
		8D399EF31F567C9900120537 /* CLRotateTool */ = {
			isa = PBXGroup;
			children = (
				8D399EF41F567C9900120537 /* CLRotateTool.h */,
				8D399EF51F567C9900120537 /* CLRotateTool.m */,
			);
			path = CLRotateTool;
			sourceTree = "<group>";
		};
		8D399EF61F567C9900120537 /* CLToneCurveTool */ = {
			isa = PBXGroup;
			children = (
				8D399EF71F567C9900120537 /* CLToneCurveTool.h */,
				8D399EF81F567C9900120537 /* CLToneCurveTool.m */,
			);
			path = CLToneCurveTool;
			sourceTree = "<group>";
		};
		8D399EF91F567C9900120537 /* ToolSettings */ = {
			isa = PBXGroup;
			children = (
				8D399EFA1F567C9900120537 /* CLCircleView.h */,
				8D399EFB1F567C9900120537 /* CLCircleView.m */,
				8D399EFC1F567C9900120537 /* CLColorPickerView.h */,
				8D399EFD1F567C9900120537 /* CLColorPickerView.m */,
				8D399EFE1F567C9900120537 /* CLImageEditorTheme+Private.h */,
				8D399EFF1F567C9900120537 /* CLImageEditorTheme+Private.m */,
				8D399F001F567C9900120537 /* CLImageToolInfo+Private.h */,
				8D399F011F567C9900120537 /* CLImageToolInfo+Private.m */,
				8D399F021F567C9900120537 /* CLImageToolProtocol.h */,
				8D399F031F567C9900120537 /* CLImageToolSettings.h */,
				8D399F041F567C9900120537 /* CLToolbarMenuItem.h */,
				8D399F051F567C9900120537 /* CLToolbarMenuItem.m */,
				8D399F061F567C9900120537 /* UIView+CLImageToolInfo.h */,
				8D399F071F567C9900120537 /* UIView+CLImageToolInfo.m */,
			);
			path = ToolSettings;
			sourceTree = "<group>";
		};
		8D399F081F567C9900120537 /* Utils */ = {
			isa = PBXGroup;
			children = (
				8D399F091F567C9900120537 /* CLClassList.h */,
				8D399F0A1F567C9900120537 /* CLClassList.m */,
				8D399F0B1F567C9900120537 /* CLSplineInterpolator.h */,
				8D399F0C1F567C9900120537 /* CLSplineInterpolator.m */,
				8D399F0D1F567C9900120537 /* UIDevice+SystemVersion.h */,
				8D399F0E1F567C9900120537 /* UIDevice+SystemVersion.m */,
				8D399F0F1F567C9900120537 /* UIImage+Utility.h */,
				8D399F101F567C9900120537 /* UIImage+Utility.m */,
				8D399F111F567C9900120537 /* UIView+Frame.h */,
				8D399F121F567C9900120537 /* UIView+Frame.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		8D399F131F567C9900120537 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				8D399F141F567C9900120537 /* _CLImageEditorViewController.h */,
				8D399F151F567C9900120537 /* _CLImageEditorViewController.m */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0C53A99D1B56113D004F2642 /* RSSignatureCapture */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0C53A9B21B56113D004F2642 /* Build configuration list for PBXNativeTarget "RSSignatureCapture" */;
			buildPhases = (
				0C53A99A1B56113D004F2642 /* Sources */,
				0C53A99B1B56113D004F2642 /* Frameworks */,
				0C53A99C1B56113D004F2642 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RSSignatureCapture;
			productName = RSSignatureCapture;
			productReference = 0C53A99E1B56113D004F2642 /* libRSSignatureCapture.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0C53A9961B56113D004F2642 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0640;
				ORGANIZATIONNAME = RepairShopr;
				TargetAttributes = {
					0C53A99D1B56113D004F2642 = {
						CreatedOnToolsVersion = 6.4;
					};
				};
			};
			buildConfigurationList = 0C53A9991B56113D004F2642 /* Build configuration list for PBXProject "RSSignatureCapture" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 0C53A9951B56113D004F2642;
			productRefGroup = 0C53A99F1B56113D004F2642 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0C53A99D1B56113D004F2642 /* RSSignatureCapture */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		0C53A99A1B56113D004F2642 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D399F1D1F567C9900120537 /* CLBloomEffect.m in Sources */,
				8D399F281F567C9900120537 /* CLImageToolBase.m in Sources */,
				8D399F231F567C9900120537 /* CLSpotEffect.m in Sources */,
				8D399F351F567C9900120537 /* UIView+Frame.m in Sources */,
				8D399F361F567C9900120537 /* _CLImageEditorViewController.m in Sources */,
				8D399F241F567C9900120537 /* CLEffectBase.m in Sources */,
				8D399F341F567C9900120537 /* UIImage+Utility.m in Sources */,
				8D399F251F567C9900120537 /* CLEffectTool.m in Sources */,
				8D399F1C1F567C9900120537 /* CLDrawTool.m in Sources */,
				8D399F311F567C9900120537 /* CLClassList.m in Sources */,
				8D399F181F567C9900120537 /* CLImageToolInfo.m in Sources */,
				8D399F1A1F567C9900120537 /* CLBlurTool.m in Sources */,
				8D399F2F1F567C9900120537 /* CLToolbarMenuItem.m in Sources */,
				8D399F1E1F567C9900120537 /* CLGloomEffect.m in Sources */,
				0C53A9EE1B56134F004F2642 /* RSSignatureViewManager.m in Sources */,
				8D399F211F567C9900120537 /* CLPixellateEffect.m in Sources */,
				8D399F191F567C9900120537 /* CLAdjustmentTool.m in Sources */,
				8D399F321F567C9900120537 /* CLSplineInterpolator.m in Sources */,
				0C53A9F41B5613AF004F2642 /* RSSignatureView.m in Sources */,
				8D399F271F567C9900120537 /* CLFilterTool.m in Sources */,
				8D399F2D1F567C9900120537 /* CLImageEditorTheme+Private.m in Sources */,
				8D399F1B1F567C9900120537 /* CLClippingTool.m in Sources */,
				8D399F291F567C9900120537 /* CLRotateTool.m in Sources */,
				8D399F1F1F567C9900120537 /* CLHighlightShadowEffect.m in Sources */,
				8D399F171F567C9900120537 /* CLImageEditorTheme.m in Sources */,
				0C53A9F31B5613AF004F2642 /* PPSSignatureView.m in Sources */,
				8D399F201F567C9900120537 /* CLHueEffect.m in Sources */,
				8D399F2B1F567C9900120537 /* CLCircleView.m in Sources */,
				8D399F301F567C9900120537 /* UIView+CLImageToolInfo.m in Sources */,
				8D399F2C1F567C9900120537 /* CLColorPickerView.m in Sources */,
				8D399F2A1F567C9900120537 /* CLToneCurveTool.m in Sources */,
				8D399F261F567C9900120537 /* CLFilterBase.m in Sources */,
				8D399F161F567C9900120537 /* CLImageEditor.m in Sources */,
				8D399F2E1F567C9900120537 /* CLImageToolInfo+Private.m in Sources */,
				8D399F331F567C9900120537 /* UIDevice+SystemVersion.m in Sources */,
				8D399F221F567C9900120537 /* CLPosterizeEffect.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		0C53A9B01B56113D004F2642 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		0C53A9B11B56113D004F2642 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		0C53A9B31B56113D004F2642 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../../React/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		0C53A9B41B56113D004F2642 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../../React/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0C53A9991B56113D004F2642 /* Build configuration list for PBXProject "RSSignatureCapture" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0C53A9B01B56113D004F2642 /* Debug */,
				0C53A9B11B56113D004F2642 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0C53A9B21B56113D004F2642 /* Build configuration list for PBXNativeTarget "RSSignatureCapture" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0C53A9B31B56113D004F2642 /* Debug */,
				0C53A9B41B56113D004F2642 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0C53A9961B56113D004F2642 /* Project object */;
}
