#import "PPSSignatureView.h"
#import <UIKit/UIKit.h>
#import <React/RCTView.h>
#import <React/RCTBridge.h>

#import "CLImageEditor.h"

@class RSSignatureViewManager;

@interface RSSignatureView : RCTView <CLImageEditorDelegate>
@property (nonatomic, strong) PPSSignatureView *sign;
@property (nonatomic, strong) RSSignatureViewManager *manager;
-(void) onSaveButtonPressed;
-(void) onClearButtonPressed;
-(void) saveImage;
-(NSString*) saveAndGetImagePath;
-(void) erase;
-(NSArray*) getAnnotations;
@end
