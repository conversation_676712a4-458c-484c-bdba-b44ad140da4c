package com.rssignaturecapture;

import android.content.Context;

import android.graphics.Point;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.drawable.Drawable;
import android.util.Log;
import android.view.MotionEvent;

import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Canvas;
import android.graphics.Bitmap;
import android.graphics.Color;

import android.util.DisplayMetrics;

import android.support.v7.widget.AppCompatImageView;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;

import java.util.ArrayList;

public class RSSignatureCaptureView extends AppCompatImageView {
    private float mLastTouchX;
    private float mLastTouchY;
    private Paint mPaint;
    private Path mPath;
    private Bitmap mSignatureBitmap = null;
    private ArrayList<Path> paths = new ArrayList<Path>();
    private ArrayList<Point> points = new ArrayList<>();
    private ReadableArray defaultPoints;
    private ArrayList<Paint> paints = new ArrayList<Paint>();
    int strokeColor = Color.RED;
    Canvas mCanvas;
    Bitmap mBitmap;

    private Boolean isErase = false;
    private Boolean isFloorPlan = false;

    public void setIsErase(Boolean isErase) {
        this.isErase = isErase;
    }

    public boolean getIsErase() {
        return this.isErase;
    }

    public void setIsFloorPlanMode(Boolean isFloorPlan) {
        Log.d("setIsFloorPlanMode", "setIsFloorPlanMode: " + isFloorPlan);
        this.isFloorPlan = isFloorPlan;
    }

    public Drawable image;

    public RSSignatureCaptureView(Context context) {

        super(context);
        mPaint = new Paint();
        //Fixed parameters
        mPaint.setStrokeWidth(20);
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPaint.setStrokeJoin(Paint.Join.ROUND);

        clear();
    }

    /**
     * Get signature
     *
     * @return
     */
    public Bitmap getSignature() {
        Bitmap signatureBitmap = null;
        // set the signature bitmap
        if (signatureBitmap == null) {
            signatureBitmap = Bitmap.createBitmap(getWidth(), getHeight(), Bitmap.Config.ARGB_8888);
        }
        // important for saving signature
        final Canvas canvas = new Canvas(signatureBitmap);
        this.draw(canvas);

        return signatureBitmap;
    }

    /**
     * clear signature canvas
     */
    public void clearSignature() {
        clear();
    }

    //    re-draw the draw area's bitmap
    private void ensureSignatureBitmap() {

        mSignatureBitmap = Bitmap.createBitmap(getWidth(), getHeight(),
                Bitmap.Config.ARGB_8888);
        mCanvas = new Canvas(mSignatureBitmap);
        mCanvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.OVERLAY);

    }

    // touch event for drawing
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // handle when isn't drawing and not using eraser
        if (!isFloorPlan) {
            if (!isEnabled() && !isErase) {
                return false;
            }
        } else {
            if (!isEnabled()) {
                return false;
            }
        }

        float eventX = event.getX();
        float eventY = event.getY();
        Log.d("isFloorPlan", "isFloorPlan: " + isFloorPlan);
        if (isFloorPlan) {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    Log.d("position", "X: " + (event.getX()) + "  Y: " + (event.getY()));

                    // refresh the drawing bitmap
                    ensureSignatureBitmap();

                    mPaint = new Paint();
                    paints.add(mPaint);
                    mPaint.setAntiAlias(true);
                    mPaint.setStyle(Paint.Style.FILL);
                    mPaint.setColor(Color.RED);

                    mPath = new Path();
                    paths.add(mPath);
                    mPath.moveTo(eventX, eventY);
                    Point pt = new Point((int) eventX, (int) eventY);
                    points.add(pt);

                    mLastTouchX = eventX;
                    mLastTouchY = eventY;

                case MotionEvent.ACTION_UP:
                    mPath.lineTo(mLastTouchX, mLastTouchY);
                    ensureSignatureBitmap();
                    break;

                default:
                    return false;
            }
        } else {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    Log.d("position", "X: " + (event.getX()) + "  Y: " + (event.getY()));

                    // refresh the drawing bitmap
                    ensureSignatureBitmap();

                    mPaint = new Paint();
                    paints.add(mPaint);
                    mPaint.setStrokeWidth(20);
                    mPaint.setAntiAlias(true);
                    mPaint.setStyle(Paint.Style.STROKE);
                    mPaint.setStrokeCap(Paint.Cap.ROUND);
                    mPaint.setStrokeJoin(Paint.Join.ROUND);
                    mPaint.setColor(strokeColor);

                    if (isErase) {
                        mPaint.setStrokeWidth(80);
                        PorterDuffXfermode porterDuffXfermode = new PorterDuffXfermode(PorterDuff.Mode.CLEAR);
                        mPaint.setXfermode(porterDuffXfermode);
                    }

                    mPath = new Path();
                    paths.add(mPath);
                    mPath.moveTo(eventX, eventY);
                    mLastTouchX = eventX;
                    mLastTouchY = eventY;

                case MotionEvent.ACTION_MOVE:
                    float dx = Math.abs(eventX - mLastTouchX);
                    float dy = Math.abs(eventY - mLastTouchY);
                    if (dx >= 4 || dy >= 4) {
                        mPath.quadTo(mLastTouchX, mLastTouchY, (eventX + mLastTouchX) / 2, (eventY + mLastTouchY) / 2);
                        mLastTouchX = eventX;
                        mLastTouchY = eventY;
                    }
                    super.invalidate();
                    break;

                case MotionEvent.ACTION_UP:
                    mPath.lineTo(mLastTouchX, mLastTouchY);
                    ensureSignatureBitmap();
                    break;

                default:
                    return false;
            }
        }

        invalidate();
        return true;
    }

    public void setImage(Drawable img) {
        this.image = img;
        this.setImageDrawable(img);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (isFloorPlan) {
            // check whether the previous points are added
            if (defaultPoints != null && points.size() < defaultPoints.size()) {
                ensureSignatureBitmap();
                for (int i = 0; i < defaultPoints.size(); i++) {
                    ReadableMap map = defaultPoints.getMap(i);
                    double coorX = map.getDouble("coorX");
                    double coorY = map.getDouble("coorY");

                    float eventX = (float) (coorX * canvas.getWidth());
                    float eventY = (float) (coorY * canvas.getHeight());

                    mPath = new Path();
                    paths.add(mPath);
                    mPath.moveTo(eventX, eventY);
                    Point pt = new Point((int) eventX, (int) eventY);
                    points.add(pt);

                    mPaint = new Paint();
                    paints.add(mPaint);
                    mPaint.setAntiAlias(true);
                    mPaint.setStyle(Paint.Style.FILL);
                    mPaint.setColor(Color.RED);
                }
            }
        }
        // draw paths
        for (int i = 0; i < paths.size(); i++) {
            Path p = paths.get(i);
            int idx = paths.indexOf(p);
            if (isFloorPlan) {
                Point pt = points.get(i);
                mCanvas.drawCircle(pt.x, pt.y, 10, paints.get(idx));
            } else
                mCanvas.drawPath(p, paints.get(idx));
        }
        if (mSignatureBitmap != null) {
            canvas.drawBitmap(mSignatureBitmap, 0, 0, null);
        }
    }

    public void clear() {
        paths.clear();
        points.clear();
        paints.clear();
        if (defaultPoints != null)
            defaultPoints = null;
        if (getDrawable() != null)
            ensureSignatureBitmap();
        invalidate();
    }

    private int convertDpToPx(float dp) {
        return Math.round(dp * (getResources().getDisplayMetrics().xdpi / DisplayMetrics.DENSITY_DEFAULT));
    }

    public interface OnSignedListener {
        public void onSigned();

        public void onClear();
    }

    public void setStrokeColor(int color) {
        Log.d("setStrokeColor", "setStrokeColor");
        this.strokeColor = color;
        invalidate();
    }

    public void undo() {
        ensureSignatureBitmap();
        if (paths.size() >= 1 && paints.size() >= 1) {
            paths.remove(paths.size() - 1);
            paints.remove(paints.size() - 1);
        }
        if (points.size() >= 1) {
            points.remove(points.size() - 1);
        }
        invalidate();
    }

    public ArrayList getAnnotations() {
        return points;
    }

    public void setAnnotations(ReadableArray annotations) {
        defaultPoints = annotations;
    }
}
