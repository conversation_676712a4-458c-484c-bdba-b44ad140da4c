package com.rssignaturecapture;

import android.graphics.Color;
import android.graphics.Point;
import android.util.Log;

import com.facebook.infer.annotation.Assertions;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.ViewGroupManager;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.uimanager.events.RCTEventEmitter;
import com.rssignaturecapture.RSSignatureCaptureContextModule;

import java.util.ArrayList;
import java.util.Map;

import javax.annotation.Nullable;

public class RSSignatureCaptureViewManager extends ViewGroupManager<RSSignatureCaptureMainView> {

    public static final String PROPS_SAVE_IMAGE_FILE = "saveImageFileInExtStorage";
    public static final String PROPS_VIEW_MODE = "viewMode";
    public static final String PROPS_MAX_SIZE = "maxSize";
    public static final String PROPS_IMAGE_FILE_PATH = "imageFilePath";
    public static final String PROPS_STROKE_COLOR = "strokeColor";
    public static final String PROPS_IS_DRAWING = "isDrawing";
    public static final String PROPS_IS_ERASE = "isErase";
    public static final String PROPS_FLOOR_PLAN = "floorPlanMode";
    public static final String PROPS_ANNOTATIONS = "annotations";

    public static final int COMMAND_SAVE_IMAGE = 1;
    public static final int COMMAND_RESET_IMAGE = 2;
    public static final int COMMAND_UNDO = 3;
    public static final int COMMAND_REMOVE_IMAGE = 4;
    public static final int COMMAND_CLEAR_IMAGE = 5;
    public static final int COMMAND_SAVE_ANNOTATIONS = 6;

    private ArrayList<RSSignatureCaptureMainView> viewArr;

    private RSSignatureCaptureContextModule mContextModule;
    private ReactApplicationContext mReactContext;

    Boolean isDrawing = false;

    public RSSignatureCaptureViewManager(ReactApplicationContext reactContext) {
        mContextModule = new RSSignatureCaptureContextModule(reactContext);
        mReactContext = reactContext;

        viewArr = new ArrayList<>();
    }

    @Override
    public String getName() {
        return "RSSignatureView";
    }

    @ReactProp(name = PROPS_SAVE_IMAGE_FILE)
    public void setSaveImageFileInExtStorage(RSSignatureCaptureMainView view, @Nullable Boolean saveFile) {
        Log.d("setFileInExtStorage:", "" + saveFile);
        if (view != null) {
            view.setSaveFileInExtStorage(saveFile);
        }
    }

    @ReactProp(name = PROPS_VIEW_MODE)
    public void setViewMode(RSSignatureCaptureMainView view, @Nullable String viewMode) {
        Log.d("setViewMode:", "" + viewMode);
        if (view != null) {
            view.setViewMode(viewMode);
        }
    }


    @ReactProp(name = PROPS_IMAGE_FILE_PATH)
    public void setImageFilePath(RSSignatureCaptureMainView view, @Nullable String imageFilePath) {
        Log.d("imageFilePath:", "" + imageFilePath);
        if (view != null) {
            view.setImageFilePath(imageFilePath);
        }
    }

    @ReactProp(name = PROPS_MAX_SIZE)
    public void setPropsWidth(RSSignatureCaptureMainView view, @Nullable Integer maxSize) {
        Log.d("maxSize:", "" + maxSize);
        if (view != null) {
            view.setMaxSize(maxSize);
        }
    }

    @ReactProp(name = PROPS_STROKE_COLOR)
    public void setStrokeColor(RSSignatureCaptureMainView view, @Nullable String strokeColor) {
        Log.d("isDrawing:", "" + strokeColor);
        int color;
        switch (strokeColor) {
            case "red":
                color = Color.RED;
                break;
            default:
                color = Color.RED;
                break;
        }
        if (view != null)
            view.setStrokeColor(color);
    }

    @ReactProp(name = PROPS_IS_DRAWING)
    public void setIsDrawing(RSSignatureCaptureMainView view, @Nullable Boolean isDrawing) {
        Log.d("isDrawing:", "" + isDrawing);
        if(view!=null) {
            view.isDrawing(isDrawing);
        }
    }

    @ReactProp(name = PROPS_IS_ERASE)
    public void setIsErase(RSSignatureCaptureMainView view, @Nullable Boolean isErase) {
        Log.d("isErase:", "" + isErase);
        if(view!=null) {
            view.isErase(isErase);
        }
    }

    @ReactProp(name = PROPS_FLOOR_PLAN)
    public void setFloorPlanMode(RSSignatureCaptureMainView view, @Nullable Boolean floorPlanMode) {
        Log.d("isFloorPlan:", "" + floorPlanMode + "View: " +(view == null));
        if(view!=null) {
            view.setIsFloorPlanMode(floorPlanMode);
        }
    }

    @ReactProp(name = PROPS_ANNOTATIONS)
    public void setAnnotations(RSSignatureCaptureMainView view, @Nullable ReadableArray annotations) {
        if(view!=null) {
            view.setAnnotations(annotations);
        }
    }


    @Override
    public RSSignatureCaptureMainView createViewInstance(ThemedReactContext context) {
        Log.d("React", " View manager createViewInstance:");
        RSSignatureCaptureMainView view = new RSSignatureCaptureMainView(context, mContextModule.getActivity());
        viewArr.add(view);
        return view;
    }

    @Override
    public Map<String, Integer> getCommandsMap() {
        Log.d("React", " View manager getCommandsMap:");
        return MapBuilder.of(
                "saveImage",
                COMMAND_SAVE_IMAGE,
                "resetImage",
                COMMAND_RESET_IMAGE,
                "undo",
                COMMAND_UNDO,
                "removeImage",
                COMMAND_REMOVE_IMAGE,
                "clearImage",
                COMMAND_CLEAR_IMAGE,
                "saveAnnotations",
                COMMAND_SAVE_ANNOTATIONS);
    }

    @Override
    public void receiveCommand(
            RSSignatureCaptureMainView view,
            int commandType,
            @Nullable ReadableArray args) {
        Assertions.assertNotNull(view);
        Assertions.assertNotNull(args);
        switch (commandType) {
            case COMMAND_CLEAR_IMAGE: {
                viewArr = new ArrayList<>();
                return;
            }
            case COMMAND_REMOVE_IMAGE: {
                viewArr.remove(view);
                return;
            }
            case COMMAND_SAVE_IMAGE: {
                WritableArray pathArr = Arguments.createArray();
                for (RSSignatureCaptureMainView captureView : viewArr) {
                    String imgPath = captureView.saveAndGetImagePath();
                    Log.d("COMMAND_SAVE_IMAGE", imgPath);
                    pathArr.pushString(imgPath);
                }

                WritableMap event = Arguments.createMap();
                event.putArray("paths", pathArr);
                mReactContext.getJSModule(RCTEventEmitter.class).receiveEvent(view.getId(), "topChange", event);
                return;
            }
            case COMMAND_RESET_IMAGE: {
                view.reset();
                return;
            }
            case COMMAND_UNDO: {
                view.undoDrawing();
                return;
            }

            case COMMAND_SAVE_ANNOTATIONS: {
                WritableArray pointsArr = Arguments.createArray();
                for (RSSignatureCaptureMainView captureView : viewArr) {
                    WritableArray pointArr = Arguments.createArray();;
                    ArrayList<Point> pts = captureView.getAnnotations();
                    double imgHeight = captureView.getImageHeight();
                    double imgWidth = captureView.getImageWidth();
                    for (Point pt : pts) {
                        WritableMap pointMap = Arguments.createMap();
                        pointMap.putDouble("coorX", pt.x/imgWidth);
                        pointMap.putDouble("coorY", pt.y/imgHeight);
                        pointArr.pushMap(pointMap);
                    }
                    pointsArr.pushArray(pointArr);
                }

                WritableMap event = Arguments.createMap();
                event.putArray("paths", pointsArr);
                mReactContext.getJSModule(RCTEventEmitter.class).receiveEvent(view.getId(), "topChange", event);
                return;
            }

            default:
                throw new IllegalArgumentException(String.format(
                        "Unsupported command %d received by %s.",
                        commandType,
                        getClass().getSimpleName()));
        }
    }


}
