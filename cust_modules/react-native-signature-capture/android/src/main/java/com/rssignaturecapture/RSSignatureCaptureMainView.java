package com.rssignaturecapture;

import android.content.ContentValues;
import android.database.Cursor;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.provider.MediaStore;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.ViewGroup;



import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.events.RCTEventEmitter;

import java.io.File;
import java.io.FileOutputStream;
import java.io.ByteArrayOutputStream;

import android.util.Base64;

import android.content.Context;
import android.graphics.Bitmap;

import android.os.Environment;
import android.view.View;

import android.widget.LinearLayout;

import android.app.Activity;
import android.content.pm.ActivityInfo;


import java.lang.Boolean;
import java.util.ArrayList;


public class RSSignatureCaptureMainView extends LinearLayout  {
    RSSignatureCaptureView signatureView;

    Activity mActivity;
    int mOriginalOrientation;
    Boolean saveFileInExtStorage = false;
    String viewMode = "portrait";

    int maxSize = 500;
    String imageFilePath;
    String strokeColor;

    private GestureDetector mGestureDetector;
    private ScaleGestureDetector scaleGestureDetector;

    View mView;

    public float scaleFactor = 1.0f;
    private Boolean scaling = false;

    public RSSignatureCaptureMainView(Context context, Activity activity) {
        super(context);
        Log.d("React:", "RSSignatureCaptureMainView(Contructtor)");
        mOriginalOrientation = activity.getRequestedOrientation();
        mActivity = activity;
        this.setGravity(Gravity.CENTER);
        mView = this;
        scaleGestureDetector = new ScaleGestureDetector(context, new ScaleListener());
        mGestureDetector = new GestureDetector(getContext(), mGestureListener);
        setLayoutParams(new android.view.ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));

        this.signatureView = new RSSignatureCaptureView(getContext());
    }

    public void setSaveFileInExtStorage(Boolean saveFileInExtStorage) {
        this.saveFileInExtStorage = saveFileInExtStorage;
    }

    public void setViewMode(String viewMode) {
        this.viewMode = viewMode;

        if (viewMode.equalsIgnoreCase("portrait")) {
            mActivity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        } else if (viewMode.equalsIgnoreCase("landscape")) {
            mActivity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }
    }


    public void setMaxSize(int size) {
        this.maxSize = size;
    }

    public String getImgUrl() {
        return this.imageFilePath;
    }

    public void setImageFilePath(String imageFilePath) {
        this.imageFilePath = imageFilePath;

        if (this.imageFilePath != null && this.imageFilePath != "") {
            String[] strArr = this.imageFilePath.split("saved_signature/");
            File imgFile;
            if (strArr.length > 1) {
                String root = Environment.getExternalStorageDirectory().toString();
                imgFile = new File(root + "/saved_signature/" + strArr[1] );
            } else {
                imgFile = new File(this.imageFilePath.replace("file://", ""));
            }

            if (imgFile.exists()) {
                DisplayMetrics dm = mActivity.getResources().getDisplayMetrics();

                BitmapFactory.Options options=new BitmapFactory.Options();
                options.inDensity=dm.densityDpi;
                options.inScreenDensity=dm.densityDpi;
                options.inTargetDensity=dm.densityDpi;

                BitmapDrawable ob = new BitmapDrawable(getResources(), BitmapFactory.decodeFile(imgFile.getAbsolutePath(), options));
                this.addView(signatureView);
                this.signatureView.setLayoutParams(new LayoutParams(ob.getIntrinsicWidth(), ob.getIntrinsicHeight()));
                this.signatureView.setImage(ob);
            }
        }
    }

    private void initImageSize() {

        float scaleX = (float) getWidth() / signatureView.getWidth();
        float scaleY = (float) getHeight() / signatureView.getHeight();
        Log.d("position: ", "width" + getWidth() + "   height " + getHeight());
        scaleFactor = Math.min(1, Math.min(scaleX, scaleY));
        //
        // Stretch and center image to fit view
        //
        this.signatureView.setScaleX(scaleFactor);
        this.signatureView.setScaleY(scaleFactor);
        invalidate();
    }

    /**
     * save the signature to an sd card directory
     */
//    final void saveImage() {
//
//        String root = Environment.getExternalStorageDirectory().toString();
//
//        // the directory where the signature will be saved
//        File myDir = new File(root + "/saved_signature");
//
//        // make the directory if it does not exist yet
//        if (!myDir.exists()) {
//            myDir.mkdirs();
//        }
//
//        // set the file name of your choice
//        String fname = System.currentTimeMillis() + "_editPhoto.png";
//
//        // in our case, we delete the previous file, you can remove this
//        File file = new File(myDir, fname);
//        while (file.exists()) {
////            file.delete();
//            fname = System.currentTimeMillis() + "_editPhoto.png";
//            file = new File(myDir, fname);
//        }
//
//        try {
//
//            Log.d("React Signature", "Save file-======:" + saveFileInExtStorage);
//            // save the signature
//            if (saveFileInExtStorage) {
//                FileOutputStream out = new FileOutputStream(file);
//                this.signatureView.getSignature().compress(Bitmap.CompressFormat.JPEG, 90, out);
//                out.flush();
//                out.close();
//            }
//
//
//            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//            Bitmap bp = this.signatureView.getSignature();
//            bp.compress(Bitmap.CompressFormat.JPEG, 90, byteArrayOutputStream);
//
//
//            byte[] byteArray = byteArrayOutputStream.toByteArray();
//            String encoded = Base64.encodeToString(byteArray, Base64.DEFAULT);
//            WritableMap event = Arguments.createMap();
//            event.putString("uri", ""+this.getImageContentUri(getContext(), file));
//            event.putString("pathName", file.getAbsolutePath());
////      event.putString("encoded", encoded);
//            ReactContext reactContext = (ReactContext) getContext();
//            reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(getId(), "topChange", event);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    final ArrayList getAnnotations() {
        return signatureView.getAnnotations();
    }

    final double getImageWidth() {
        return signatureView.getWidth();
    }

    final double getImageHeight() {
        return signatureView.getHeight();
    }

    final String saveAndGetImagePath() {
        String root = Environment.getExternalStorageDirectory().toString();

        // the directory where the signature will be saved
        File myDir = new File(root + "/saved_signature");

        // make the directory if it does not exist yet
        if (!myDir.exists()) {
            myDir.mkdirs();
        }

        // set the file name of your choice
        String fname = System.currentTimeMillis() + "_editPhoto.jpg";

        // in our case, we delete the previous file, you can remove this
        File file = new File(myDir, fname);
        while (file.exists()) {
//            file.delete();
            fname = System.currentTimeMillis() + "_editPhoto.jpg";
            file = new File(myDir, fname);
        }

        try {

            Log.d("React Signature", "saveAndGetImagePath-======:" + saveFileInExtStorage);
            // save the signature
            FileOutputStream out = new FileOutputStream(file);
            this.signatureView.getSignature().compress(Bitmap.CompressFormat.JPEG, 70, out);
            out.flush();
            out.close();

            return file.getAbsolutePath();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    private static Uri getImageContentUri(Context context, File imageFile) {
        String filePath = imageFile.getAbsolutePath();
        Cursor cursor = context.getContentResolver().query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                new String[]{MediaStore.Images.Media._ID},
                MediaStore.Images.Media.DATA + "=? ",
                new String[]{filePath}, null);
        if (cursor != null && cursor.moveToFirst()) {
            int id = cursor.getInt(cursor.getColumnIndex(MediaStore.MediaColumns._ID));
            cursor.close();
            return Uri.withAppendedPath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "" + id);
        } else {
            if (imageFile.exists()) {
                ContentValues values = new ContentValues();
                values.put(MediaStore.Images.Media.DATA, filePath);
                return context.getContentResolver().insert(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
            } else {
                return null;
            }
        }
    }

    public void reset() {
        if (this.signatureView != null) {
            this.signatureView.clearSignature();
        }
    }

    public void isDrawing(Boolean isDrawing){
        if (this.signatureView != null) {
            this.signatureView.setEnabled(isDrawing);
        }
    }

    public void isErase(Boolean isErase){
        if (this.signatureView != null) {
            this.signatureView.setIsErase(isErase);
        }
    }

    public void undoDrawing(){
        if (this.signatureView != null) {
            this.signatureView.undo();
        }
    }

    public void setStrokeColor(int color){
        if (this.signatureView != null) {
            this.signatureView.setStrokeColor(color);
        }
    }

    public void setIsFloorPlanMode(Boolean isFloorPlanMode){
        Log.d("setIsFloorPlanMode:", ""  + "View: " +(signatureView == null));
        if (this.signatureView != null) {
            this.signatureView.setIsFloorPlanMode(isFloorPlanMode);
        }
    }

    public boolean getDrawViewIsEnable(){
        if (this.signatureView != null) {
           return this.signatureView.isEnabled();
        }
        return false;
    }


    @Override
    protected void onDraw(Canvas canvas) {

    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_POINTER_DOWN:
                scaling = true;
                break;
            case MotionEvent.ACTION_UP:
                scaling = false;
                break;
        }
        if (!signatureView.isEnabled() && !signatureView.getIsErase()) {
            if (scaling)
                scaleGestureDetector.onTouchEvent(event);
            else
                mGestureDetector.onTouchEvent(event);
            return true;
        }
        return false;
    }

    public void setAnnotations(ReadableArray annotations) {
        signatureView.setAnnotations(annotations);
    }

    private class ScaleListener extends
            ScaleGestureDetector.SimpleOnScaleGestureListener {
        @Override
        public boolean onScale(ScaleGestureDetector detector) {
            scaleFactor *= detector.getScaleFactor();

            // don't let the object get too small or too large.
            scaleFactor = Math.max(0.8f, Math.min(scaleFactor, 3.0f));
            signatureView.setScaleX(scaleFactor);
            signatureView.setScaleY(scaleFactor);
            invalidate();
            return true;
        }
    }


    private GestureDetector.OnGestureListener mGestureListener = new GestureDetector.SimpleOnGestureListener() {

        private float mMotionDownX, mMotionDownY;

        @Override
        public boolean onDown(MotionEvent e) {
            mMotionDownX = e.getRawX() - signatureView.getTranslationX();
            mMotionDownY = e.getRawY() - signatureView.getTranslationY();
//            Log.d("mMotionDown", "X: " + (mMotionDownX) + "  Y: " + (mMotionDownY));

            return true;
        }

        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            Log.d("=============", "=============");
//            Log.d("MotionEvent 1", "X: " + (e1.getRawX()) + "  Y: " + (e1.getRawY()));
//            Log.d("MotionEvent 2", "X: " + (e2.getRawX()) + "  Y: " + (e2.getRawY()));
//            Log.d("Distance", "X: " + (distanceX) + "  Y: " + (distanceY));
//            Log.d("view size", "width: " + (signatureView.getWidth()) + "  height: " + (signatureView.getHeight()));
            Log.d("before", "X: " + (signatureView.getTranslationX()) + "  Y: " + (signatureView.getTranslationY()));

            signatureView.setTranslationX(e2.getRawX() - mMotionDownX);
            signatureView.setTranslationY(e2.getRawY() - mMotionDownY);

            Log.d("after", "X: " + (signatureView.getTranslationX()) + "  Y: " + (signatureView.getTranslationY()));

            return true;
        }
    };


}
