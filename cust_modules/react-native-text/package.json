{"_args": [["react-native-text@0.0.4", "/Users/<USER>/projects/test-app"]], "_from": "react-native-text@0.0.4", "_id": "react-native-text@0.0.4", "_inBundle": false, "_integrity": "sha1-mHUhVQm1O9eBikG5qhfMv4hmaLo=", "_location": "/react-native-text", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "react-native-text@0.0.4", "name": "react-native-text", "escapedName": "react-native-text", "rawSpec": "0.0.4", "saveSpec": null, "fetchSpec": "0.0.4"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react-native-text/-/react-native-text-0.0.4.tgz", "_spec": "0.0.4", "_where": "/Users/<USER>/projects/test-app", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "mateus<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/knowbody"}, "bugs": {"url": "https://github.com/knowbody/react-native-text/issues"}, "description": "Scalable font size in Text component for React Native", "homepage": "https://github.com/knowbody/react-native-text#readme", "keywords": ["react-native", "scaled", "ios", "android", "text", "react"], "license": "MIT", "main": "./index.js", "name": "react-native-text", "peerDependencies": {"react": "*", "react-native": "*"}, "repository": {"type": "git", "url": "git+https://github.com/knowbody/react-native-text.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "0.0.4"}