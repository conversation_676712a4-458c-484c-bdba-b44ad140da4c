{"_from": "react-native-version-number@^0.3.0", "_id": "react-native-version-number@0.3.0", "_inBundle": false, "_integrity": "sha512-WSSFJUn+Ztgz/KdMGrsJZqYtnn5z4AvpPPB9G6cp/Mx6xDukreoG16zKkeeKBi0baryvmOofe7X+DTac6vefRQ==", "_location": "/react-native-version-number", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "react-native-version-number@^0.3.0", "name": "react-native-version-number", "escapedName": "react-native-version-number", "rawSpec": "^0.3.0", "saveSpec": null, "fetchSpec": "^0.3.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react-native-version-number/-/react-native-version-number-0.3.0.tgz", "_shasum": "49ee70140420946ca8a88c4ed50f31412ee0e227", "_spec": "react-native-version-number@^0.3.0", "_where": "/Users/<USER>/Documents/Frenco/PlayMore/test/projects/test-app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/APSL/react-native-version-number/issues"}, "bundleDependencies": false, "deprecated": false, "description": "<img src=\"https://travis-ci.org/APSL/react-native-version-number.svg?branch=master\" />", "devDependencies": {"babel-eslint": "^6.1.2", "eslint": "^3.3.1", "eslint-plugin-react": "^6.1.2"}, "gitHead": "d4cd14e286bee2374e20eaf799faef73692ed2ee", "homepage": "https://github.com/APSL/react-native-version-number#readme", "keywords": ["react", "react-native", "ios", "react-component"], "license": "MIT", "main": "index.js", "name": "react-native-version-number", "repository": {"type": "git", "url": "git+https://github.com/APSL/react-native-version-number.git"}, "scripts": {"lint": "eslint index.js", "test": "npm run lint"}, "tags": ["react", "react-native", "react-component", "ios"], "version": "0.3.0"}