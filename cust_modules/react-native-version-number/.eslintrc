{"parser": "babel-es<PERSON>", "parserOptions": {"ecmaFeatures": {"jsx": true, "modules": true, "experimentalObjectRestSpread": true}}, "env": {"es6": true, "jasmine": true, "node": 1}, "plugins": ["react"], "globals": {"__DEV__": true, "__dirname": false, "__fbBatchedBridgeConfig": false, "cancelAnimationFrame": false, "clearImmediate": true, "clearInterval": false, "clearTimeout": false, "console": false, "document": false, "escape": false, "exports": false, "fetch": false, "global": false, "jest": false, "Map": true, "module": false, "navigator": false, "process": false, "Promise": true, "requestAnimationFrame": true, "require": false, "Set": true, "setImmediate": true, "setInterval": false, "setTimeout": false, "window": false, "XMLHttpRequest": false, "pit": false, "FormData": true}, "rules": {"comma-dangle": 0, "no-cond-assign": 1, "no-console": 0, "no-constant-condition": 0, "no-control-regex": 1, "no-debugger": 1, "no-dupe-keys": 1, "no-empty": 0, "no-empty-character-class": 1, "no-ex-assign": 1, "no-extra-boolean-cast": 1, "no-extra-parens": 0, "no-extra-semi": 1, "no-func-assign": 1, "no-inner-declarations": 0, "no-invalid-regexp": 1, "no-negated-in-lhs": 1, "no-obj-calls": 1, "no-regex-spaces": 1, "no-reserved-keys": 0, "no-sparse-arrays": 1, "no-unreachable": 1, "use-isnan": 1, "valid-jsdoc": 0, "valid-typeof": 1, "block-scoped-var": 0, "complexity": 0, "consistent-return": 0, "curly": 1, "default-case": 0, "dot-notation": 1, "eqeqeq": 1, "guard-for-in": 0, "no-alert": 1, "no-caller": 1, "no-div-regex": 1, "no-else-return": 0, "no-labels": 1, "no-eq-null": 0, "no-eval": 1, "no-extend-native": 1, "no-extra-bind": 1, "no-fallthrough": 1, "no-floating-decimal": 1, "no-implied-eval": 1, "no-iterator": 1, "no-lone-blocks": 1, "no-loop-func": 0, "no-multi-str": 0, "no-native-reassign": 0, "no-new": 1, "no-new-func": 1, "no-new-wrappers": 1, "no-octal": 1, "no-octal-escape": 1, "no-proto": 1, "no-redeclare": 0, "no-return-assign": 1, "no-script-url": 1, "no-self-compare": 1, "no-sequences": 1, "no-unused-expressions": 0, "no-void": 1, "no-warning-comments": 0, "no-with": 1, "radix": 1, "vars-on-top": 0, "wrap-iife": 0, "yoda": 1, "strict": 0, "no-catch-shadow": 1, "no-delete-var": 1, "no-label-var": 1, "no-shadow": 1, "no-shadow-restricted-names": 1, "no-undef": 2, "no-undefined": 0, "no-undef-init": 1, "no-unused-vars": [1, {"vars": "all", "args": "none"}], "no-use-before-define": 0, "handle-callback-err": 1, "no-mixed-requires": 1, "no-new-require": 1, "no-path-concat": 1, "no-process-exit": 0, "no-restricted-modules": 1, "no-sync": 0, "key-spacing": 0, "comma-spacing": 0, "no-multi-spaces": 0, "brace-style": 0, "camelcase": 0, "consistent-this": [1, "self"], "eol-last": 1, "func-names": 0, "func-style": 0, "new-cap": 0, "new-parens": 1, "no-nested-ternary": 0, "no-array-constructor": 1, "no-lonely-if": 0, "no-new-object": 1, "no-spaced-func": 1, "semi-spacing": 1, "no-ternary": 0, "no-trailing-spaces": 1, "no-underscore-dangle": 0, "no-mixed-spaces-and-tabs": 1, "quotes": [1, "single", "avoid-escape"], "quote-props": 0, "semi": 0, "sort-vars": 0, "keyword-spacing": [0, {"before": true}], "space-in-brackets": 0, "space-in-parens": 0, "space-infix-ops": 1, "space-unary-ops": [1, {"words": true, "nonwords": false}], "max-nested-callbacks": 0, "one-var": 0, "wrap-regex": 0, "max-depth": 0, "max-len": 0, "max-params": 0, "max-statements": 0, "no-bitwise": 1, "no-plusplus": 0, "react/display-name": 0, "react/jsx-boolean-value": 0, "react/jsx-no-undef": 1, "react/jsx-sort-props": 0, "react/jsx-uses-react": 0, "react/jsx-uses-vars": 1, "react/no-did-mount-set-state": 1, "react/no-did-update-set-state": 1, "react/no-multi-comp": 0, "react/no-unknown-property": 0, "react/prop-types": 0, "react/react-in-jsx-scope": 0, "react/self-closing-comp": 1, "react/wrap-multilines": 0}}