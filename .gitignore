# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace
Pods/

# Android/IntelliJ
#
.idea
.gradle
local.properties
*.iml

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore
!defecttrackingapp.keystore
# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://github.com/fastlane/fastlane/blob/master/fastlane/docs/Gitignore.md
com_crashlytics_export_strings.xml
crashlytics-build.properties
*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots
android/app/src/main/assets/index.android.bundle
android/app/src/main/assets/index.android.bundle.meta
android/app/src/main/res/drawable-hdpi/
android/app/src/main/res/drawable-mdpi/
android/app/src/main/res/drawable-xhdpi/
android/app/src/main/res/drawable-xxhdpi/
android/app/src/main/res/drawable-xxxhdpi/
ios/testApp.app.dSYM.zip
ios/assets/src/
ios/main.jsbundle
ios/main.jsbundle.meta
main.jsbundle.map
index.android.bundle.map