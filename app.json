{"expo": {"name": "TEST UAT", "slug": "test-app", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "icon": "./assets/icon.png", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.test.dta"}, "android": {"package": "com.test.dta", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "favicon": "./assets/favicon.png"}, "plugins": ["expo-camera", "expo-image-picker"]}}