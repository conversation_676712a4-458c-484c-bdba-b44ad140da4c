const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add module alias resolver
config.resolver.alias = {
  'Utilities': path.resolve(__dirname, 'src/util/utilities.ts'),
  'Key': path.resolve(__dirname, 'src/service/key/key.js'),
  'i18': path.resolve(__dirname, 'src/service/i18/index.js'),
  'Redux': path.resolve(__dirname, 'src/redux/index.js'),
  'Page': path.resolve(__dirname, 'src/component/page/index.ts'),
  'Item': path.resolve(__dirname, 'src/component/item/index.js'),
  'Config': path.resolve(__dirname, 'src/config/config.json.ts'),
  'LocalDB': path.resolve(__dirname, 'src/service/localDB/index.js'),
  'Server': path.resolve(__dirname, 'src/service/server/index.js'),
  'Navigator': path.resolve(__dirname, 'src/navigation/index.ts'),
};

// Add file extensions
config.resolver.sourceExts = [...config.resolver.sourceExts, 'ts', 'tsx'];

module.exports = config;
