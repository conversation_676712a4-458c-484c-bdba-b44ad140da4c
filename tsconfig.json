{"extends": "expo/tsconfig.base", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "checkJs": false, "baseUrl": "./src", "paths": {"Component": ["./component"], "Page": ["./component/page"], "Item": ["./component/item"], "Config": ["./config/config.json.js"], "Theme": ["./config/theme.js"], "Redux": ["./redux"], "Navigator": ["./service/navigator.js"], "Key": ["./util/key.js"], "i18": ["./util/localizedString.js"], "Utilities": ["./util/utilities.js"], "LocalDB": ["./service/localDB.js"], "Server": ["./service/server.js"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "App.js", "TestApp.tsx"], "exclude": ["node_modules", "dist", "cust_modules", "src/**/*.js", "src/**/*.jsx"]}