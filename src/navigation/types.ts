// Navigation types and route parameters
export type RootStackParamList = {
  Empty: undefined;
  Login: undefined;
  Main: undefined;
  CheckList: undefined;
  CheckListItem: undefined;
  FloorPlan: undefined;
  EditPhoto: undefined;
  PhotoView: undefined;
  TaskDownload: undefined;
  UnitDownload: undefined;
  ItemsDownload: undefined;
  ItemDetail: undefined;
  UnitDetail: undefined;
  Items: undefined;
  AddHocForm: undefined;
  AdhocList: undefined;
  FormSubmission: undefined;
  RefreshLoginSession: undefined;
  ReadQRcode: undefined;
};

// Navigation prop types for screens
export type ScreenNavigationProp<T extends keyof RootStackParamList> = {
  navigation: any; // Will be properly typed with React Navigation
  route?: any;
};

// Screen component type
export type ScreenComponent<T extends keyof RootStackParamList> = React.ComponentType<
  ScreenNavigationProp<T>
>;
