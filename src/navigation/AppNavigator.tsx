import { NavigationContainer, NavigationState } from '@react-navigation/native';
import {
  createStackNavigator,
  StackNavigationProp,
} from '@react-navigation/stack';
import React, { useEffect } from 'react';
import { View } from 'react-native';
import { connect } from 'react-redux';

import { RootState, User } from '../types';
import { routes } from './routes';
import { RootStackParamList } from './types';
const { DEBUG } = require('../config/config.json');

const Stack = createStackNavigator<RootStackParamList>();

// Navigation logging utility
const logNavigation = (state: NavigationState | undefined) => {
  if (DEBUG && state) {
    const route = state.routes[state.index];
    console.log('Navigation changed to:', route.name);
  }
};

// Empty component for initial route
interface EmptyProps {
  navigation: StackNavigationProp<RootStackParamList, 'Empty'>;
  user: User | null;
  setNavigation: (navigation: any) => void;
}

const Empty: React.FC<EmptyProps> = ({ navigation, user, setNavigation }) => {
  useEffect(() => {
    setNavigation(navigation);

    // Navigate based on user authentication status
    if (user && user.username) {
      navigation.replace('Main');
    } else {
      navigation.replace('Login');
    }
  }, [navigation, user, setNavigation]);

  return <View />;
};

// Connected Empty component
const ConnectedEmpty = connect(
  (state: RootState) => ({
    user: state.auth.user,
  }),
  (dispatch: any) => {
    const { Action } = require('../redux');
    return {
      setNavigation: (navigation: any) => {
        dispatch(Action.navigation.setMainNavigation(navigation));
      },
    };
  }
)(Empty);

// Stack Navigator component
const StackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="Empty"
      screenOptions={{
        headerStyle: {
          backgroundColor: '#f4511e',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      {/* Add Empty route first */}
      <Stack.Screen
        name="Empty"
        component={ConnectedEmpty}
        options={{ headerShown: false }}
      />

      {/* Add all other routes */}
      {routes.map(route => (
        <Stack.Screen
          key={route.name}
          name={route.name}
          component={route.component}
          options={route.options}
        />
      ))}
    </Stack.Navigator>
  );
};

// Main App Navigator component
interface AppNavigatorProps {
  user: User | null;
  loading: boolean;
  setNavigation: (navigation: any) => void;
}

const AppNavigator: React.FC<AppNavigatorProps> = ({ setNavigation }) => {
  const onStateChange = (state: NavigationState | undefined) => {
    logNavigation(state);
    // Additional navigation state handling can be added here
  };

  return (
    <NavigationContainer onStateChange={onStateChange}>
      <StackNavigator />
    </NavigationContainer>
  );
};

// Redux connection
const mapStateToProps = (state: RootState) => ({
  user: state.auth.user,
  loading: state.loading.api.length > 0 || state.loading.storage.length > 0,
});

const mapDispatchToProps = (dispatch: any) => {
  const { Action } = require('../redux');
  return {
    setNavigation: (navigation: any) => {
      dispatch(Action.navigation.setMainNavigation(navigation));
    },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(AppNavigator);
