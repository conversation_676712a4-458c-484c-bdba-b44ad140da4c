import { RootStackParamList } from './types';

// Import all page components
import {
  LoginPage,
  MainPage,
  CheckList,
  CheckListItem,
  EditPhoto,
  PhotoView,
  FloorPlan,
  TaskDownload,
  ItemsDownload,
  ItemDetail,
  UnitDownload,
  UnitDetail,
  Items,
  AddHocForm,
  AdhocList,
  FormSubmission,
  RefreshLoginSession,
  ReadQRcode,
  Loading,
} from '../component/page';

// Route configuration interface
export interface RouteConfig {
  name: keyof RootStackParamList;
  component: React.ComponentType<any>;
  options?: {
    title?: string;
    headerShown?: boolean;
    gestureEnabled?: boolean;
  };
}

// Main route configurations
export const routes: RouteConfig[] = [
  {
    name: 'Empty',
    component: () => null, // Empty component for initial route
    options: { headerShown: false },
  },
  {
    name: 'Login',
    component: LoginPage,
    options: { 
      title: 'Login',
      headerShown: false,
      gestureEnabled: false,
    },
  },
  {
    name: 'Main',
    component: MainPage,
    options: { 
      title: 'Main',
      headerShown: false,
      gestureEnabled: false,
    },
  },
  {
    name: 'CheckList',
    component: CheckList,
    options: { title: 'Check List' },
  },
  {
    name: 'CheckListItem',
    component: CheckListItem,
    options: { title: 'Check List Item' },
  },
  {
    name: 'FloorPlan',
    component: FloorPlan,
    options: { title: 'Floor Plan' },
  },
  {
    name: 'EditPhoto',
    component: EditPhoto,
    options: { title: 'Edit Photo' },
  },
  {
    name: 'PhotoView',
    component: PhotoView,
    options: { title: 'Photo View' },
  },
  {
    name: 'TaskDownload',
    component: TaskDownload,
    options: { title: 'Task Download' },
  },
  {
    name: 'UnitDownload',
    component: UnitDownload,
    options: { title: 'Unit Download' },
  },
  {
    name: 'ItemsDownload',
    component: ItemsDownload,
    options: { title: 'Items Download' },
  },
  {
    name: 'ItemDetail',
    component: ItemDetail,
    options: { title: 'Item Detail' },
  },
  {
    name: 'UnitDetail',
    component: UnitDetail,
    options: { title: 'Unit Detail' },
  },
  {
    name: 'Items',
    component: Items,
    options: { title: 'Items' },
  },
  {
    name: 'AddHocForm',
    component: AddHocForm,
    options: { title: 'Add Hoc Form' },
  },
  {
    name: 'AdhocList',
    component: AdhocList,
    options: { title: 'Adhoc List' },
  },
  {
    name: 'FormSubmission',
    component: FormSubmission,
    options: { title: 'Form Submission' },
  },
  {
    name: 'RefreshLoginSession',
    component: RefreshLoginSession,
    options: { title: 'Refresh Login Session' },
  },
  {
    name: 'ReadQRcode',
    component: ReadQRcode,
    options: { title: 'Read QR Code' },
  },
];
