const { DEBUG } = require('../config/config.json');

// Type definitions
export type ValidType =
  | 'undefined'
  | 'null'
  | 'any'
  | 'boolean'
  | 'string'
  | 'emptyString'
  | 'object'
  | 'emptyObject'
  | 'array'
  | 'emptyArray'
  | 'date'
  | 'integer'
  | 'float'
  | 'number'
  | 'http'
  | 'https'
  | 'ftp'
  | 'email'
  | 'uri'
  | 'base64';

/**
 * checkParam type matching and compulsory
 *
 * @param tag - warning display tag
 * @param param - any value
 * @param types - required types
 * @return boolean
 *
 * @example checkParam("function.js > myFunction",shouldBeStringOrUndefined,['string','undefined])  ->  true | false
 * @example checkParam("function.js > myFunction",shouldBeBooleanAndCompulsory,'boolean')  ->  true | false
 * @example checkParam("function.js > myFunction",compulsoryOnly,'any')  ->  true | false
 */
function checkParam(
  tag: string | any,
  param?: any,
  types?: ValidType | ValidType[]
): boolean {
  if (
    types === undefined &&
    (typeof param === 'string' || param instanceof Array)
  ) {
    types = param as ValidType | ValidType[];
    param = tag;
    tag = undefined;
  }

  let typesArray: ValidType[];
  if (types instanceof Array) {
    typesArray = types;
  } else if (typeof types === 'string') {
    typesArray = [types];
  } else {
    DEBUG &&
      console.warn(
        'checkParam(): types must be a string or an array of string'
      );
    return false;
  }

  let paramType: ValidType = 'any';
  if (param === null) {
    paramType = 'null';
  } else if (param === undefined) {
    paramType = 'undefined';
  } else if (param instanceof Array) {
    paramType = param.length === 0 ? 'emptyArray' : 'array';
  } else if (param instanceof Date) {
    paramType = 'date';
  } else if (typeof param === 'number') {
    if (Number.isInteger(param)) paramType = 'integer';
    else paramType = 'float';
  } else if (
    typeof param === 'string' &&
    !!param.match(/^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$/)
  ) {
    paramType = 'email';
  } else if (
    typeof param === 'string' &&
    !!param.match(
      /^http:\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/
    )
  ) {
    paramType = 'http';
  } else if (
    typeof param === 'string' &&
    !!param.match(
      /^https:\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/
    )
  ) {
    paramType = 'https';
  } else if (
    typeof param === 'string' &&
    !!param.match(
      /^ftp:\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/
    )
  ) {
    paramType = 'ftp';
  } else if (
    typeof param === 'string' &&
    !!param.match(
      /^\s*data:([a-z]+\/[a-z]+(;[a-z\-]+\=[a-z\-]+)?)?(;base64)?,[a-z0-9\!\$\&\'\,\(\)\*\+\,\;\=\-\.\_\~\:\@\/\?\%\s]*\s*$/
    )
  ) {
    paramType = 'base64';
  } else if (typeof param === 'string' && param.length === 0) {
    paramType = 'emptyString';
  } else if (typeof param === 'object' && Object.keys(param).length === 0) {
    paramType = 'emptyObject';
  } else {
    paramType = typeof param as ValidType;
  }

  if (
    typesArray.some(t => t === 'any') &&
    paramType !== 'null' &&
    paramType !== 'undefined'
  ) {
    return true;
  } else if (
    typesArray.some(t => t === 'number') &&
    (paramType === 'integer' || paramType === 'float')
  ) {
    return true;
  } else if (
    typesArray.some(t => t === 'uri') &&
    (paramType === 'http' ||
      paramType === 'https' ||
      paramType === 'ftp' ||
      paramType === 'base64')
  ) {
    return true;
  } else if (
    typesArray.some(t => t === 'string') &&
    (paramType === 'email' ||
      paramType === 'http' ||
      paramType === 'https' ||
      paramType === 'ftp' ||
      paramType === 'base64')
  ) {
    return true;
  } else {
    const result = typesArray.some(t => t === paramType);
    if (!result) {
      DEBUG &&
        tag &&
        console.warn(
          tag +
            '(): Err: Invalid param, vaild params include: [' +
            typesArray.join() +
            "], but got '" +
            paramType +
            "'"
        );
      return false;
    }
  }
  return true;
}

/**
 * checkParam value matching the data
 *
 * @param tag - warning display tag
 * @param param - any value
 * @param values - required values
 * @return boolean
 *
 * @example checkParamValue("function.js > myFunction",myStringValue,['myStringValue','yourStringValue])  ->  true
 * @example checkParamValue("function.js > myFunction",thisIsTrue,[true,false])  ->  true
 * @example checkParamValue("function.js > myFunction",aRandomNumber,[0,1,2,3,4,5])  ->  true
 */
function checkParamValue(
  tag: string | any,
  param?: any,
  values?: any | any[]
): boolean {
  if (
    values === undefined &&
    (typeof param === 'string' || param instanceof Array)
  ) {
    values = param;
    param = tag;
    tag = undefined;
  }

  let valuesArray: any[];
  if (!(values instanceof Array)) {
    valuesArray = [values];
  } else {
    valuesArray = values;
  }

  if (valuesArray.some(v => v === param)) {
    return true;
  } else {
    DEBUG &&
      tag &&
      console.warn(
        tag +
          '(): Err: Invalid value, vaild values include: [' +
          valuesArray.join() +
          "], but got '" +
          param +
          "'"
      );
    return false;
  }
}

const CHAR_SET =
  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

/**
 * random string generator
 *
 * @param length - the length of the random string
 * @return string
 *
 * @example randomString(20)  ->  'b5j85najbrigh1nc00an4'
 */
function randomString(length: number = 30): string {
  let str = '';
  for (let i = 0; i < length; i++)
    str += CHAR_SET[Math.floor(Math.random() * 62)];
  return str;
}

/**
 * leftpad add the char if the length is not long enough
 *
 * @param text - the original string
 * @param length - target length
 * @param char - the char for insert
 * @return string
 *
 * @example leftpad("3",3,'0')  ->  "003"
 */
function leftpad(
  text: string | number,
  length: number,
  char: string = ' '
): string {
  let textStr = text + '';
  while (textStr.length < length) textStr = char + textStr;
  return textStr;
}

/**
 * amount add comma to display number
 *
 * @param number - the original number
 * @return string
 *
 * @example amount(120.5) ->  "120.5"
 * @example amount(1200)  ->  "1,200"
 */
function amount(number: number | null | undefined): string {
  if (number === null || number === undefined) return '';

  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * delay milliseconds
 *
 * @param time - delay time in milliseconds
 *
 * @example await delay(1000)
 */
async function delay(time: number = 0): Promise<void> {
  return new Promise(resolver => {
    setTimeout(resolver, time);
  });
}

export { amount, checkParam, checkParamValue, delay, leftpad, randomString };

export default {
  checkParam,
  checkParamValue,
  randomString,
  leftpad,
  amount,
  delay,
};
