// Theme types
export interface NavTheme {
	bg: string;
	title: string;
}

export interface TabTheme {
	active: string;
	text: string;
	bg: string;
}

export interface TaskListItemTheme {
	bg: string;
	text: string;
	date: string;
	state: string;
}

export interface TaskListBubbleTheme {
	draft: string;
	extra: string;
	text: string;
}

export interface TaskListTheme {
	bg: string;
	item: TaskListItemTheme;
	bubble: TaskListBubbleTheme;
}

export interface CheckListFooterTheme {
	bg: string;
	border: string;
	button: string;
}

export interface CheckListHeaderTheme {
	bg: string;
}

export interface CheckListPopUpTheme {
	bg: string;
	button: string;
}

export interface CheckListListTheme {
	header: CheckListHeaderTheme;
	popUp: CheckListPopUpTheme;
}

export interface CheckListStatusTheme {
	text: string;
	unfill: string;
	satisfy: string;
	followUp: string;
}

export interface CheckListItemTheme {
	bg: string;
	border: string;
	level: string;
	status: CheckListStatusTheme;
}

export interface CheckListHeaderStatusTheme {
	unfill: string;
	satisfy: string;
	followUp: string;
}

export interface CheckListSectionTheme {
	bg: string;
	border: string;
}

export interface CheckListTheme {
	bg: string;
	footer: CheckListFooterTheme;
	list: CheckListListTheme;
	item: CheckListItemTheme;
	header: CheckListHeaderStatusTheme;
	section: CheckListSectionTheme;
}

export interface AppTheme {
	theme: string;
	nav: NavTheme;
	tab: TabTheme;
	taskList: TaskListTheme;
	checkList: CheckListTheme;
}

const theme: AppTheme = {
	theme: '#faeddb',
	nav:{
		bg:'#362e29',
		title:'#faeddb',
	},
	tab:{
		active: 'rgba(104.55, 51, 0, 1.0)',
		text: 'rgba(104.55, 51, 0, 0.3)',
		bg: '#fff',
	},
	taskList:{
		bg: '#f2f2f2',
		item:{
			bg: '#fff',
			text: '#333',
			date: '#aaa',
			state: '#333',
		},
		bubble:{
			draft: '#fd8040',
			extra: '#3ea725',
			text: '#fff',
		},
	},
	checkList:{
		bg: '#f2f2f2',
		footer:{
			bg:'#F7F7F8',
			border:'#000',
			button:'rgba(255, 127.5, 51, 1)',
		},
		list:{
			header:{
				bg: '#fff',
			},
			popUp:{
				bg: 'rgba(255,255,255,0.85)',
				button: '#333',
			},
		},
		item:{
			bg: '#fff',
			border: '#ccc',
			level: '#555',
			status:{
				text: '#fff',
				unfill: 'gray',
				satisfy: 'rgba(96.6, 175.95, 153, 1)',
				followUp: 'rgba(204, 114.75, 114.75, 1)',
			},
		},
		header: {
			unfill: '#393939',
			satisfy: '#69B39E',
			followUp: '#D28686',
		},
		section:{
			bg: '#f2f2f2',
			border: '#000',
		}
	},
};

export default theme;