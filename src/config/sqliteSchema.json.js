module.exports = {
	Project: {
		_id: { type: 'id', primaryKey: true },
		_updated_by: { type: 'string' },
		_updated_at: { type: 'date' },
		_created_by: { type: 'string'},
		_created_at: { type: 'date' },
		_acl: { type: 'json'  },
		code: { type: 'string', index: true },
		name_en: { type: 'string'},
		name_zh: { type: 'string'},
		name_cn: { type: 'string'},
		logo: { type: 'string'},
		pic: { type: 'string'},
		description_en: { type: 'string'},
		description_zh: { type: 'string'},
		description_cn: { type: 'string'},
		address_en: { type: 'string'},
		address_zh: { type: 'string'},
		address_cn: { type: 'string'},
		contact_email: { type: 'string'},
		contact_hotline: { type: 'string'},
		unit_address_en: { type: 'json' },
		unit_address_zh: { type: 'json' },
		unit_address_cn: { type: 'json' },
		is_active: {type: 'bool'},
	},

	Unit: {
		_id: { type: 'id', primaryKey: true },
		_updated_by: { type: 'string'},
		_updated_at: { type: 'date' },
		_created_by: { type: 'string'},
		_created_at: { type: 'date' },
		_acl: { type: 'json'  },			
		type: { type: 'string'},
		tower: { type: 'string'},
		floor: { type: 'string'},
		flat: { type: 'string'},
		stage: { type: 'string'},
		status_int: { type: 'string'},
		checkboxes: { type: 'json' },
		floor_plans: { type: 'json' },
		customers_purchaser: { type: 'json' },
		customers_booking: { type: 'json' },
		checklists: { type: 'json' },
		responses: { type: 'json' },
		timeslots: { type: 'json' },
		key: { type: 'json' },
		stage_history: { type: 'string' },
		status_int_history: { type: 'string' },
		handover_form: { type: 'string' },
		sold_date: { type: 'string' },
		assignment_date: { type: 'string' },
		handover_date: { type: 'string' },
		dlp_date: { type: 'string' },
		lawyer : { type: 'string' },	
		agent : { type: 'string' },	
		car_plate : { type: 'string' },	
		car_park : { type: 'string' },	
		remark : { type: 'string' },	
		vip : { type: 'string' },
		handover_info : { type: 'string' },	
		display_name_zh : { type: 'string' },	
		display_name_en : { type: 'string' },	
		display_name_cn : { type: 'string' },	
		is_club : { type: 'bool' },	
		project: { type: 'string' },				
		selected: { type: 'bool' }
			// project: { type: 'Project', pointer: true },
	},
	
	Checklist: {
		_id: { type: 'id', primaryKey: true },
		_updated_by: { type: 'string'},
		_updated_at: { type: 'date' },
		_created_by: { type: 'string'},
		_created_at: { type: 'date' },
		_acl: { type: 'json'  },			
		name : { type: 'string'},	
		project : { type: 'string'},	
			// project : { type: 'Project', pointer: true },
		unit : { type: 'string'},				
			// unit : { type: 'Unit', pointer: true },
		type : { type: 'string'},		
		tower : { type: 'string'},
		floor : { type: 'string'},
		flat : { type: 'string'},
		metadata : { type: 'json' },
		active : { type: 'bool'},
		questions: {type: 'json', size: 524286},
		template: { type: 'string' },
		questions_count: { type: 'int' },
		has_option_statisfy: { type: 'bool' },
		has_option_other: { type: 'bool' },
		has_option_missing: { type: 'bool' },
		can_batch_failed: { type: 'bool' },
		require_photo_for_statisfy: { type: 'bool' },
		require_floor_plan_for_statisfy: { type: 'bool' },
		require_photo_for_failed: { type: 'bool' },
		require_floor_plan_for_failed: { type: 'bool' },
		assignee: { type: 'string' },
	},

	PendingCheckList: {
		_id: { type: 'string', primaryKey: true },
		_updated_by: { type: 'string'},
		_updated_at: { type: 'date' },
		_created_by: { type: 'string'},
		_created_at: { type: 'date' },
		_acl: { type: 'json'  },			
		name : { type: 'string'},	
		project : { type: 'string' },	
			// project : { type: 'Project', pointer: true },
		unit : { type: 'string' },				
			// unit : { type: 'Unit', pointer: true },
		type : { type: 'string' },		
		tower : { type: 'string' },
		floor : { type: 'string' },
		flat : { type: 'string' },
		metadata : { type: 'json' },
		active : { type: 'bool' },
		questions: {type: 'json' },
		template: { type: 'string' },
		questions_count: { type: 'int' },
		has_option_statisfy: { type: 'bool' },
		has_option_other: { type: 'bool' },
		has_option_missing: { type: 'bool' },
		can_batch_failed: { type: 'bool' },
		require_photo_for_statisfy: { type: 'bool' },
		require_floor_plan_for_statisfy: { type: 'bool' },
		require_photo_for_failed: { type: 'bool' },
		require_floor_plan_for_failed: { type: 'bool' },
		assignee: { type: 'string' },
	},

	Item: {
		_id: { type: 'string', primary: true },
		_updated_by: { type: 'string'},
		_updated_at: { type: 'date' },
		_created_by: { type: 'string'},
		_created_at: { type: 'date' },
		_acl: { type: 'json'  },			
		project : { type: 'string' },	
			// project : { type: 'Project', pointer: true },
		unit : { type: 'string' },				
			// unit : { type: 'Unit', pointer: true },
		type : { type: 'string' },		
		tower : { type: 'string' },
		floor : { type: 'string' },
		flat : { type: 'string' },
		description : { type: 'string' },
		location : { type: 'string' },	
		option : { type: 'string' },	
		level1 : { type: 'string' },
		level2 : { type: 'string' },
		name : { type: 'string' },
		checklist : { type: 'string' },
		checklist_resp : { type: 'string' },
		checklist_item : { type: 'string' },
		status : { type: 'string' },
		status_history: {type: 'json'},
		pictures: {type: 'json'},
		messages: {type: 'json'},
		files: {type: 'json'},
		assignee : { type: 'string' },
		goodwill : { type: 'bool' },
		critical : { type: 'bool' },
		defect : { type: 'bool' },
		isOthers : { type: 'bool' },
		remark : { type: 'string' },
		finish_date : { type: 'string' },
		selected : { type: 'bool' },
		comments: { type: 'string' },
		isDefect: { type: 'bool' },
		isFinish: { type: 'bool' },
		real_finish_date: { type: 'string' },
	},

	PendingItem: {
		_id: { type: 'string', primart: true },
		_updated_by: { type: 'string'},
		_updated_at: { type: 'date' },
		_created_by: { type: 'string'},
		_created_at: { type: 'date' },
		_acl: { type: 'json'  },		
			// project : { type: 'Project', pointer: true },
		unit : { type: 'string' },				
			// unit : { type: 'Unit', pointer: true },
		type : { type: 'string' },		
		tower : { type: 'string' },
		floor : { type: 'string' },
		flat : { type: 'string' },
		description : { type: 'string' },
		location : { type: 'string' },	
		option : { type: 'string' },	
		level1 : { type: 'string' },
		level2 : { type: 'string' },
		name : { type: 'string' },
		checklist : { type: 'string' },
		checklist_resp : { type: 'string' },
		checklist_item : { type: 'string' },
		status : { type: 'string' },
		status_history: {type: 'json'},
		pictures: {type: 'json'},
		messages: {type: 'json'},
		files: {type: 'json'},
		assignee : { type: 'string' },
		goodwill : { type: 'bool' },
		critical : { type: 'bool' },
		defect : { type: 'bool' },
		isOthers : { type: 'bool' },
		remark : { type: 'string' },
		finish_date : { type: 'string' },
		selected : { type: 'bool' },
	},
}