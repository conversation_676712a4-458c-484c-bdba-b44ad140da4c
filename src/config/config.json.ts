// Configuration types
export interface RealmConfig {
	schema: any[];
	schemaVersion: number;
	migration: (oldRealm: any, newRealm: any) => void;
}

export interface SQLiteConfig {
	name: string;
	size: number;
	schema: any[];
}

export interface AppConfig {
	SERVER_URL: string;
	API_KEY: string;
	DEBUG: boolean;
	OFFLINE_MODE: boolean;
	APP_ID: string;
	REALM: RealmConfig;
	SQLITE: SQLiteConfig;
}

const config: AppConfig = {
	SERVER_URL: "https://test.com/api",
	API_KEY: "test_playmore_apikey",
	DEBUG: true,
	OFFLINE_MODE: false,
	APP_ID: 'com.test.dtatest',

	REALM: {
		schema: require('./realmSchema.json.js'),
		schemaVersion: 27,
		migration: (_oldRealm: any, _newRealm: any) => {
			/* example code from realm in case extra action is needed for migrating data*/
			// if (oldRealm.schemaVersion < 1) {
			// 	const oldObjects = oldRealm.objects('Person');
			// 	const newObjects = newRealm.objects('Person');

			// 	// loop through all objects and set the name property in the new schema
			// 	for (let i = 0; i < oldObjects.length; i++) {
			// 		newObjects[i].name = oldObjects[i].firstName + ' ' + oldObjects[i].lastName;
			// 	}
			// }
		}
	},

	SQLITE: {
		name: "local.db",
		size: 3000000, // 3MB
		schema: require('./sqliteSchema.json.js'),
	}
};

export default config;
