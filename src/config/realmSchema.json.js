module.exports = [
	{
		name: 'Project',
		primaryKey: '_id',
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },
			code: { type: 'string', indexed: true },
			name_en: { type: 'string', optional: true },
			name_zh: { type: 'string', optional: true },
			name_cn: { type: 'string', optional: true },
			logo: { type: 'string', optional: true },
			pic: { type: 'string', optional: true },
			description_en: { type: 'string', optional: true },
			description_zh: { type: 'string', optional: true },
			description_cn: { type: 'string', optional: true },
			address_en: { type: 'string', optional: true },
			address_zh: { type: 'string', optional: true },
			address_cn: { type: 'string', optional: true },
			contact_email: { type: 'string', optional: true },
			contact_hotline: { type: 'string', optional: true },
			unit_address_en: { type: 'string', optional: true, json: true },
			unit_address_zh: { type: 'string', optional: true, json: true },
			unit_address_cn: { type: 'string', optional: true, json: true },
		}
	},

	{
		name: 'Unit',
		primaryKey: '_id',		
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },			
			type: { type: 'string', optional: true },
			tower: { type: 'string', optional: true },
			floor: { type: 'string', optional: true },
			flat: { type: 'string', optional: true },
			stage: { type: 'string', optional: true },
			status_int: { type: 'string', optional: true },
			checkboxes: { type: 'string', optional: true, json: true },
			floor_plans: { type: 'string', optional: true, json: true },
			customers_purchaser: { type: 'string', optional: true, json: true },
			customers_booking: { type: 'string', optional: true, json: true },
			checklists: { type: 'string', optional: true, json: true },
			responses: { type: 'string', optional: true, json: true },
			timeslots: { type: 'string', optional: true, json: true },
			unit_key: { type: 'string', optional: true, json: true },
			stage_history: { type: 'string', optional: true, json: true },
			status_int_history: { type: 'string', optional: true, json: true },
			handover_form: { type: 'string', optional: true, json: true },
			sold_date: { type: 'string', optional: true },
			assignment_date: { type: 'string', optional: true },
			handover_date: { type: 'string', optional: true },
			dlp_date: { type: 'string', optional: true },
			lawyer : { type: 'string', optional: true },	
			agent : { type: 'string', optional: true },	
			car_plate : { type: 'string', optional: true },	
			car_park : { type: 'string', optional: true },	
			remark : { type: 'string', optional: true },	
			vip : { type: 'bool', optional: true },
			vip_trans : { type: 'string', optional: true },
			handover_info : { type: 'string', optional: true, json: true },	
			display_name_zh : { type: 'string', optional: true },	
			display_name_en : { type: 'string', optional: true },	
			display_name_cn : { type: 'string', optional: true },	
			is_club : { type: 'bool', optional: true },	
			project : { type: 'string', optional: true },	
			selected: { type: 'bool', optional: true },			
				// project: { type: 'Project', pointer: true },
		}
	},

	{
		name: 'PendingUnit',
		primaryKey: '_id',		
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },			
			type: { type: 'string', optional: true },
			tower: { type: 'string', optional: true },
			floor: { type: 'string', optional: true },
			flat: { type: 'string', optional: true },
			stage: { type: 'string', optional: true },
			status_int: { type: 'string', optional: true },
			checkboxes: { type: 'string', optional: true, json: true },
			floor_plans: { type: 'string', optional: true, json: true },
			customers_purchaser: { type: 'string', optional: true, json: true },
			customers_booking: { type: 'string', optional: true, json: true },
			checklists: { type: 'string', optional: true, json: true },
			responses: { type: 'string', optional: true, json: true },
			timeslots: { type: 'string', optional: true, json: true },
			unit_key: { type: 'string', optional: true, json: true },
			stage_history: { type: 'string', optional: true, json: true },
			status_int_history: { type: 'string', optional: true, json: true },
			handover_form: { type: 'string', optional: true, json: true },
			sold_date: { type: 'string', optional: true },
			assignment_date: { type: 'string', optional: true },
			handover_date: { type: 'string', optional: true },
			dlp_date: { type: 'string', optional: true },
			lawyer : { type: 'string', optional: true },	
			agent : { type: 'string', optional: true },	
			car_plate : { type: 'string', optional: true },	
			car_park : { type: 'string', optional: true },	
			remark : { type: 'string', optional: true },	
			vip : { type: 'bool', optional: true },
			vip_trans : { type: 'string', optional: true },
			handover_info : { type: 'string', optional: true, json: true },	
			display_name_zh : { type: 'string', optional: true },	
			display_name_en : { type: 'string', optional: true },	
			display_name_cn : { type: 'string', optional: true },	
			is_club : { type: 'bool', optional: true },	
			project : { type: 'string', optional: true },	
			selected: { type: 'bool', optional: true },			
			saved_at: { type: 'int', optional: true, date:true },
		}
	},

	{
		name: 'UnitItem',
		primaryKey: '_id',		
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },			
			project : { type: 'string', optional: true },
			unit : { type: 'string', optional: true },
			type: { type: 'string', optional: true },
			tower: { type: 'string', optional: true },
			floor: { type: 'string', optional: true },
			flat: { type: 'string', optional: true },
			source: { type: 'string', optional: true },
			checklist: { type: 'string', optional: true },
			questions_count: { type: 'int', optional: true },
			pass: { type: 'int', optional: true },
			failed: { type: 'int', optional: true },
			name : { type: 'string', optional: true },	
			auto_generate_defect_item : { type: 'bool', optional: true },	
			purchaser_report: { type: 'string', optional: true },
			letter: { type: 'string', optional: true },
			resp_items: { type: 'string', optional: true, json: true },	
			template: { type: 'string', optional: true },
			assignee: { type: 'string', optional: true },
			assigned_at: { type: 'int', optional: true, date:true },
			assigned_by: { type: 'string', optional: true },
			submitted_at: { type: 'int', optional: true, date:true }
		}
	},
    {
        name: 'ItemSummary',
        primaryKey: '_id',
        properties: {
            _id: { type: 'string', indexed: true },
            unit: { type: 'string', optional: true},
            name: { type: 'string', optional: true},
			total: { type: 'int', optional: true},
            statuses: { type: 'string', optional: true, json: true  },
        }
    },
	{
		name: 'Checklist',
		primaryKey: '_id',		
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },			
			name : { type: 'string', optional: true },	
			project : { type: 'string', optional: true },	
				// project : { type: 'Project', pointer: true },
			unit : { type: 'string', optional: true },				
				// unit : { type: 'Unit', pointer: true },
			type : { type: 'string', optional: true },		
			tower : { type: 'string', optional: true },
			floor : { type: 'string', optional: true },
			flat : { type: 'string', optional: true },
			metadata : { type: 'string', optional: true, json: true },
			active : { type: 'bool', optional: true },
			questions: {type: 'string', optional: true, json: true},
			template: { type: 'string', optional: true },
			questions_count: { type: 'int', optional: true },
			has_option_statisfy: { type: 'bool', optional: true },
			has_option_other: { type: 'bool', optional: true },
			has_option_missing: { type: 'bool', optional: true },
			can_batch_failed: { type: 'bool', optional: true },
			require_photo_for_statisfy: { type: 'bool', optional: true },
			require_floor_plan_for_statisfy: { type: 'bool', optional: true },
			require_photo_for_failed: { type: 'bool', optional: true },
			require_floor_plan_for_failed: { type: 'bool', optional: true },
			assignee: { type: 'string', optional: true, json: true },
			source: { type: 'string', optional: true },
			label: { type: 'string', optional: true },
			require_skip_function: { type: 'bool', optional: true },
			checklist_resp : { type: 'string', optional: true },
			vip : { type: 'bool', optional: true },
			vip_trans : { type: 'string', optional: true },
			downloadFromChecklist: { type: 'bool', optional: true },
		}
	},
    {
        name: 'ChecklistSummary',
        primaryKey: '_id',
        properties: {
            _id: { type: 'string', indexed: true },
            unit: { type: 'string', optional: true},
            name: { type: 'string', optional: true},
            satisfy: { type: 'int', optional: true},
            followup: { type: 'int', optional: true},
            checklist_resp: { type: 'string', optional: true, json: true  },
        }
    },
	{
		name: 'PendingCheckList',
		primaryKey: '_id',		
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },			
			name : { type: 'string', optional: true },	
			project : { type: 'string', optional: true },	
				// project : { type: 'Project', pointer: true },
			unit : { type: 'string', optional: true },				
				// unit : { type: 'Unit', pointer: true },
			type : { type: 'string', optional: true },		
			tower : { type: 'string', optional: true },
			floor : { type: 'string', optional: true },
			flat : { type: 'string', optional: true },
			metadata : { type: 'string', optional: true, json: true },
			active : { type: 'bool', optional: true },
			questions: {type: 'string', optional: true, json: true},
			template: { type: 'string', optional: true },
			questions_count: { type: 'int', optional: true },
			has_option_statisfy: { type: 'bool', optional: true },
			has_option_other: { type: 'bool', optional: true },
			has_option_missing: { type: 'bool', optional: true },
			can_batch_failed: { type: 'bool', optional: true },
			require_photo_for_statisfy: { type: 'bool', optional: true },
			require_floor_plan_for_statisfy: { type: 'bool', optional: true },
			require_photo_for_failed: { type: 'bool', optional: true },
			require_floor_plan_for_failed: { type: 'bool', optional: true },
			assignee: { type: 'string', optional: true, json: true },
			source: { type: 'string', optional: true },
			label: { type: 'string', optional: true },
			require_skip_function: { type: 'bool', optional: true },
			checklist_resp : { type: 'string', optional: true },
			labelData: { type: 'string', optional: true, json: true  },	
			downloadFromChecklist: { type: 'bool', optional: true },
			saved_at: { type: 'int', optional: true, date:true },
		}
	},
	{
		name: 'Item',
		primaryKey: '_id',		
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },			
			project : { type: 'string', optional: true },	
				// project : { type: 'Project', pointer: true },
			unit : { type: 'string', optional: true },				
				// unit : { type: 'Unit', pointer: true },
			type : { type: 'string', optional: true },		
			tower : { type: 'string', optional: true },
			floor : { type: 'string', optional: true },
			flat : { type: 'string', optional: true },
			description : { type: 'string', optional: true },
			location : { type: 'string', optional: true },	
			option : { type: 'string', optional: true },	
			level1 : { type: 'string', optional: true },
			level2 : { type: 'string', optional: true },
			name : { type: 'string', optional: true },
			checklist : { type: 'string', optional: true },
			checklist_resp : { type: 'string', optional: true },
			checklist_item : { type: 'string', optional: true },
			status : { type: 'string', optional: true },
			status_history: {type: 'string', optional: true, json: true},
			annotations: {type: 'string', optional: true, json: true},
			pictures: {type: 'string', optional: true, json: true},	// the field for original images
			images: {type: 'string', optional: true, json: true},	// the filed for images going to be uploaded
			messages: {type: 'string', optional: true, json: true},
			files: {type: 'string', optional: true, json: true},
			assignee : { type: 'string', optional: true },
			assign_date : { type: 'int', optional: true, date:true },
			goodwill : { type: 'bool', optional: true },
			critical : { type: 'bool', optional: true },
			defect : { type: 'bool', optional: true },
			isOthers : { type: 'bool', optional: true },
			remark : { type: 'string', optional: true },
			finish_date : { type: 'string', optional: true },
			selected : { type: 'bool', optional: true },
			comments: { type: 'string', optional: true },
			isDefect: { type: 'bool', optional: true },
			isFinish: { type: 'bool', optional: true },
			real_finish_date: { type: 'string', optional: true },
		}
	},
	{
		name: 'PendingItem',
		primaryKey: '_id',		
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },			
			project : { type: 'string', optional: true },	
				// project : { type: 'Project', pointer: true },
			unit : { type: 'string', optional: true },				
				// unit : { type: 'Unit', pointer: true },
			type : { type: 'string', optional: true },		
			tower : { type: 'string', optional: true },
			floor : { type: 'string', optional: true },
			flat : { type: 'string', optional: true },
			description : { type: 'string', optional: true },
			location : { type: 'string', optional: true },	
			option : { type: 'string', optional: true },	
			level1 : { type: 'string', optional: true },
			level2 : { type: 'string', optional: true },
			name : { type: 'string', optional: true },
			checklist : { type: 'string', optional: true },
			checklist_resp : { type: 'string', optional: true },
			checklist_item : { type: 'string', optional: true },
			status : { type: 'string', optional: true },
			status_history: {type: 'string', optional: true, json: true},
			pictures: {type: 'string', optional: true, json: true},	// the field for original images
			images: {type: 'string', optional: true, json: true},	// the filed for images going to be uploaded
			messages: {type: 'string', optional: true, json: true},
			files: {type: 'string', optional: true, json: true},
			assignee : { type: 'string', optional: true },
			goodwill : { type: 'bool', optional: true },
			critical : { type: 'bool', optional: true },
			defect : { type: 'bool', optional: true },
			isOthers : { type: 'bool', optional: true },
			remark : { type: 'string', optional: true },
			finish_date : { type: 'string', optional: true },
			selected : { type: 'bool', optional: true },
			saved_at: { type: 'int', optional: true, date:true },
		}
	},

	{
		name: 'AdhocFormOption',
		primaryKey: '_id',
		properties: {
			_id: { type: 'string', indexed: true },
			_updated_by: { type: 'string', optional: true },
			_updated_at: { type: 'int', optional: true, date:true },
			_created_by: { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			_acl: { type: 'string', optional: true, json: true  },	
			project : { type: 'string', optional: true },		
			name : { type: 'string', optional: true },	
			unit : { type: 'string', optional: true },			
			tower : { type: 'string', optional: true },
			floor : { type: 'string', optional: true },
			flat : { type: 'string', optional: true },
			location : { type: 'string', optional: true },	
			option : { type: 'string', optional: true },	
			level1 : { type: 'string', optional: true },
			level2 : { type: 'string', optional: true },
		}
	},
	{
		name: 'QrcodeData',
		primaryKey: '_id',
		properties: {
			_id: { type: 'string', indexed: true },
			key_status : { type: 'string', optional: true },		
			unit_status : { type: 'string', optional: true },	
			current_owner : { type: 'string', optional: true },			
			dialing_code : { type: 'string', optional: true },
			mobile : { type: 'string', optional: true },
			reason : { type: 'string', optional: true },
			remark : { type: 'string', optional: true },
			_created_at: { type: 'int', optional: true, date:true },
			tower : { type: 'string', optional: true },
			floor : { type: 'string', optional: true },
			flat : { type: 'string', optional: true },
			unit : { type: 'string', optional: true },	
			type : { type: 'string', optional: true },	
			saved_at: { type: 'int', optional: true, date:true },
		}
	},
]