// Type declarations for custom modules

declare module 'react-native-version-number' {
  interface VersionObject {
    appVersion: string;
    buildVersion: string;
    bundleIdentifier: string;
  }
  
  const VersionNumber: VersionObject;
  export default VersionNumber;
}

declare module 'Component' {
  export const Root: any;
  export const Item: any;
  export const Page: any;
  export const App: any;
}

declare module 'Page' {
  export const LoginPage: any;
  export const MainPage: any;
  export const PageSample: any;
  export const CheckList: any;
  export const CheckListItem: any;
  export const EditPhoto: any;
  export const PhotoView: any;
  export const FloorPlan: any;
  export const TaskDownload: any;
  export const ItemsDownload: any;
  export const ItemDetail: any;
  export const UnitDownload: any;
  export const UnitDetail: any;
  export const Items: any;
  export const AddHocForm: any;
  export const AdhocList: any;
  export const FormSubmission: any;
  export const RefreshLoginSession: any;
  export const ReadQRcode: any;
  export const Loading: any;
}

declare module 'Item' {
  export const ProgressView: any;
}

declare module 'Config' {
  export const DEBUG: boolean;
  export const APP_ID: string;
  export const SERVER_URL: string;
  export const API_KEY: string;
  export const OFFLINE_MODE: boolean;
  export const REALM: {
    schema: any[];
    schemaVersion: number;
    migration: (oldRealm: any, newRealm: any) => void;
  };
  export const SQLITE: {
    name: string;
    size: number;
    schema: any[];
  };
}

declare module 'Theme' {
  export interface AppTheme {
    theme: string;
    nav: {
      bg: string;
      title: string;
    };
    tab: {
      active: string;
      text: string;
      bg: string;
    };
    taskList: {
      bg: string;
      item: {
        bg: string;
        text: string;
        date: string;
        state: string;
      };
      bubble: {
        draft: string;
        extra: string;
        text: string;
      };
    };
    checkList: {
      bg: string;
      footer: {
        bg: string;
        border: string;
        button: string;
      };
      list: {
        header: {
          bg: string;
        };
        popUp: {
          bg: string;
          button: string;
        };
      };
      item: {
        bg: string;
        border: string;
        level: string;
        status: {
          text: string;
          unfill: string;
          satisfy: string;
          followUp: string;
        };
      };
      header: {
        unfill: string;
        satisfy: string;
        followUp: string;
      };
      section: {
        bg: string;
        border: string;
      };
    };
  }

  const theme: AppTheme;
  export default theme;
}

declare module 'Redux' {
  export const Action: any;
  export const init: any;
}

declare module 'Navigator' {
  const Navigator: any;
  export default Navigator;
}

declare module 'Key' {
  const key: () => Promise<string>;
  export default key;
}

declare module 'i18' {
  const i18: {
    language: string;
    setLanguage: (lang: string) => void;
    welcomeMsg: string;
    OK: string;
    [key: string]: any;
  };
  export default i18;
}

declare module 'Utilities' {
  const Utilities: any;
  export default Utilities;
}

declare module 'LocalDB' {
  const LocalDB: any;
  export default LocalDB;
}

declare module 'Server' {
  const Server: any;
  export default Server;
}

// Global variable declarations
declare const globalVal: {
  stopShowingWelcome: boolean;
  [key: string]: any;
};

// React Native module declarations
declare module 'react-native-actionsheet' {
  const ActionSheet: any;
  export default ActionSheet;
}

declare module 'react-native-animatable' {
  export const View: any;
  export const Text: any;
  export const Image: any;
}

declare module 'react-native-keyboard-aware-scroll-view' {
  export const KeyboardAwareScrollView: any;
}

declare module 'react-native-modal' {
  const Modal: any;
  export default Modal;
}

declare module 'react-native-modal-dropdown' {
  const ModalDropdown: any;
  export default ModalDropdown;
}

// Custom modules in cust_modules
declare module 'react-native-text' {
  const Text: any;
  export default Text;
}

declare module 'react-native-version-number' {
  interface VersionObject {
    appVersion: string;
    buildVersion: string;
    bundleIdentifier: string;
  }

  const VersionNumber: VersionObject;
  export default VersionNumber;
}

// Reduxsauce type declarations
declare module 'reduxsauce' {
  export interface ActionCreators {
    [key: string]: (...args: any[]) => any;
  }

  export interface ActionTypes {
    [key: string]: string;
  }

  export interface CreateActionsResult {
    Types: ActionTypes;
    Creators: ActionCreators;
  }

  export interface CreateActionsOptions {
    prefix?: string;
  }

  export function createActions(
    actions: { [key: string]: string[] | null | ((...args: any[]) => any) },
    options?: CreateActionsOptions
  ): CreateActionsResult;

  export function createReducer<TState>(
    initialState: TState,
    handlers: { [key: string]: (state: TState, action: any) => TState }
  ): (state: TState | undefined, action: any) => TState;
}
