// Redux specific type definitions
import { Action } from 'redux';

// Action Types
export interface ReduxAction<T = any> extends Action {
  type: string;
  payload?: T;
}

// Action Creators Types
export interface ActionCreators {
  navigation: NavigationActions;
  root: RootActions;
  loading: LoadingActions;
  auth: AuthActions;
  setting: SettingActions;
  units: UnitsActions;
  checklists: ChecklistsActions;
  progressBar: ProgressBarActions;
  project: ProjectActions;
  items: ItemsActions;
}

// Navigation Actions
export interface NavigationActions {
  replace: (name: string) => ReduxAction<{ name: string }>;
  setMainNavigation: (navigation: any) => ReduxAction<{ navigation: any }>;
  setSubNavigation: (navigation: any) => ReduxAction<{ navigation: any }>;
}

// Root Actions
export interface RootActions {
  test: () => ReduxAction;
}

// Loading Actions
export interface LoadingActions {
  apiStart: (key: string) => ReduxAction<{ key: string }>;
  apiEnd: (key: string) => ReduxAction<{ key: string }>;
  storageStart: (key: string) => ReduxAction<{ key: string }>;
  storageEnd: (key: string) => ReduxAction<{ key: string }>;
}

// Auth Actions
export interface AuthActions {
  loggedInUser: (user: any) => ReduxAction<{ user: any }>;
  loggedOutUser: () => ReduxAction;
  showAlertForgotPassword: (show: boolean) => ReduxAction<{ show: boolean }>;
}

// Setting Actions
export interface SettingActions {
  changeLanguage: (language: string) => ReduxAction<{ language: string }>;
  cameraStatus: (data: boolean) => ReduxAction<{ data: boolean }>;
  networkInfoUpdate: (info: any) => ReduxAction<{ info: any }>;
  setAreacodeData: (labels: string[], values: string[]) => ReduxAction<{ labels: string[]; values: string[] }>;
}

// Units Actions
export interface UnitsActions {
  // Define units actions
}

// Checklists Actions
export interface ChecklistsActions {
  // Define checklists actions
}

// Progress Bar Actions
export interface ProgressBarActions {
  reset: () => ReduxAction;
  cancel: () => ReduxAction;
  update: (progress: { total: number; current: number; message?: string }) => ReduxAction<{ total: number; current: number; message?: string }>;
}

// Project Actions
export interface ProjectActions {
  // Define project actions
}

// Items Actions
export interface ItemsActions {
  // Define items actions
}

// Redux Types
export interface ReduxTypes {
  root: RootTypes;
  loading: LoadingTypes;
  auth: AuthTypes;
  setting: SettingTypes;
  units: UnitsTypes;
  checklists: ChecklistsTypes;
  progressBar: ProgressBarTypes;
  project: ProjectTypes;
  items: ItemsTypes;
}

// Individual Type Enums
export interface RootTypes {
  TEST: string;
}

export interface LoadingTypes {
  API_START: string;
  API_END: string;
  STORAGE_START: string;
  STORAGE_END: string;
}

export interface AuthTypes {
  LOGGED_IN_USER: string;
  LOGGED_OUT_USER: string;
  SHOW_ALERT_FORGOT_PASSWORD: string;
}

export interface SettingTypes {
  CHANGE_LANGUAGE: string;
  CAMERA_STATUS: string;
  NETWORK_INFO_UPDATE: string;
  SET_AREACODE_DATA: string;
}

export interface UnitsTypes {
  // Define units types
}

export interface ChecklistsTypes {
  // Define checklists types
}

export interface ProgressBarTypes {
  RESET: string;
  CANCEL: string;
  UPDATE: string;
}

export interface ProjectTypes {
  // Define project types
}

export interface ItemsTypes {
  // Define items types
}

// Store Configuration
export interface StoreConfig {
  key: string;
  storage: any;
  transforms?: any[];
  blacklist?: string[];
  whitelist?: string[];
}
