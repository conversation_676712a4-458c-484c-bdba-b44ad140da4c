// Global type definitions

// Redux State Types
export interface RootState {
  navigation: NavigationState;
  loading: LoadingState;
  auth: AuthState;
  setting: SettingState;
  units: UnitsState;
  checklists: ChecklistsState;
  progressBar: ProgressBarState;
  project: ProjectState;
  items: ItemsState;
  root: RootReducerState;
}

// Root reducer state
export interface RootReducerState {
  someNum?: number;
}

// Navigation Types
export interface NavigationState {
  main: any;
  sub: any;
}

// Loading State
export interface LoadingState {
  api: string[];
  storage: string[];
}

// Auth State
export interface AuthState {
  user: User | null;
  lastLoginDateStr: string | null;
  showAlertForgotPassword: boolean;
}

// User Type
export interface User {
  username: string;
  email?: string;
  id: string;
  // Add other user properties as needed
}

// Setting State
export interface SettingState {
  language: string;
  cameraOpenned: boolean;
  connectionInfo: any;
  areacodeLabels: string[];
  areacodeValues: string[];
}

// Progress Bar State
export interface ProgressBarState {
  total: number;
  current: number;
  message?: string;
}

// Project State
export interface ProjectState {
  projectLists: Project[] | null;
  selectedProject: Project | null;
  projectDetail: ProjectDetail | null;
  checklistsCount: number;
  unitsCount: number;
  itemsCount: number;
  pendingCount: number;
  unitsInfo: UnitInfo[];
  vipUnits: any[];
  adhoc_options: AdhocOptions;
  unitsTree: Record<string, any>;
  adhocTreeInspec: Record<string, any>;
  adhocTreeSpec: Record<string, any>;
  adhoc_current_unit: any;
  qrcodeData: Record<string, any>;
  showKeyForm: boolean;
  pendingKey: any[];
  handoverForm: any[];
  floor_plans: any[];
  filteredOptions: FilteredOptions;
}

// Project Types
export interface Project {
  _id: string;
  code: string;
  name_en?: string;
  name_zh?: string;
  name_cn?: string;
  logo?: string;
  pic?: string;
  description_en?: string;
  description_zh?: string;
}

export interface ProjectDetail extends Project {
  // Add additional project detail properties
}

export interface UnitInfo {
  // Define unit info properties
}

export interface AdhocOptions {
  tower: string[];
  floor: string[];
  flat: string[];
  location: string[];
  level1: string[];
  level2: string[];
  isInspection: boolean;
}

export interface FilteredOptions {
  floor: string[];
  flat: string[];
}

// Units State
export interface UnitsState {
  // Define units state properties
}

// Checklists State
export interface ChecklistsState {
  // Define checklists state properties
}

// Items State
export interface ItemsState {
  // Define items state properties
}

// Component Props Types
export interface BaseComponentProps {
  navigation?: any;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Common Types
export type Language = 'zh_hk' | 'zh_cn' | 'en';

export interface Theme {
  theme: string;
  nav: {
    bg: string;
    title: string;
  };
  tab: {
    active: string;
    text: string;
    bg: string;
  };
  // Add other theme properties as needed
}

// Redux Action Types
export interface Action<T = any> {
  type: string;
  payload?: T;
}

// Utility Types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
