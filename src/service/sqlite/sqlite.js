/**
 * @providesModule SQLite
 */

import { DEBUG , SQLITE } from 'Config';
import _SQLite from 'react-native-sqlite-storage';
var hash = require('object-hash');

import insertEncode from './insertEncoder.js';
import updateEncode from './updateEncoder.js';
import decode from './decoder.js';
import key from 'Key';

_SQLite.DEBUG(DEBUG);
_SQLite.enablePromise(true);

function sqlTranslateDate(sql){
	if (sql){
		let sqlDate = sql.match(/((Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (0\d|[12]\d|3[01]) [12]\d{3} (0\d|1\d|2[0-3]):([0-5]\d):([0-5]\d) GMT(\+|\-)(0\d|1[0-2])(0|3)0 \([A-Z]{3}\)|\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))/g);

		if (sqlDate)
			sqlDate.forEach(date => {
				let timestamp = Date.parse(date);
				if (!Number.isNaN(timestamp))
					sql = sql.replace(date,timestamp);
			});
	}

	return sql;
}

function mapSchemaToSQL(schema){
	let sql = Object.keys(schema).map(key => {
		let type = schema[key].type;
		let index = !!schema[key].index;
		let size = schema[key].size;

		let output = key;

		switch(type){
			case "id": output += ` VARCHAR(${size || 20})`; break;
			case "string": output += ` VARCHAR(${size || 255})`; break;
			case "json": output += ` VARCHAR(${size || 65535})`; break;
			case "int": output += " INTEGER"; break;
			case "float": output += " FLOAT"; break;
			case "double": output += " DOUBLE"; break;
			case "bool": output += " BOOLEAN"; break;
			case "date": output += " INTEGER"; break;
		}

		if (schema[key].primaryKey)
			output += " PRIMARY KEY NOT NULL";
		else if (schema[key].notNull)
			output += " NOT NULL";
		
		return output;
	}).join(', ');

	return sql;
}

function mapRows(rows, func){
	let output = [];
	for (let i=0; i<rows.length; i++)
		output.push(rows.item(i));

	if (func)	return output.map(func);
	else			return output;
}

class SQLite {
	constructor() {
		console.log('Set SQLite with Config:', SQLITE);
		this._connect().then(this._checkSetup).then(this._close);
	}

	_connect = ()=>{
		return key().then(key => {
			return _SQLite.openDatabase({name:SQLITE.name, size:SQLITE.size});
		})
	}

	_close = (db)=>{
		return db.close();
	}

	_checkSetup = (db)=>{
		return db.executeSql('SELECT * FROM Version LIMIT 1').then(resp => {
			let length = resp[0].rows.length;		
			if (length !== 1)
				return db.transaction(this._setup);
			else if (resp[0].rows.item(0)._id !== hash(SQLITE,{encoding: 'base64'}).substr(0,10))
				return db.transaction(this._setup);

			console.log('DB is ready, version: '+resp[0].rows.item(0)._id);
		}).catch(error=>{
			return db.transaction(this._setup);
		}).then(()=>db);
	}

	_setup = (tx)=>{
		console.logTime('DB version unmatch, reset database...');
		tx.executeSql('DROP TABLE IF EXISTS Version;');	
		tx.executeSql('CREATE TABLE IF NOT EXISTS Version(_id VARCHAR(20) PRIMARY KEY NOT NULL);');
		Object.keys(SQLITE.schema).forEach(key => {
			tx.executeSql(`DROP TABLE IF EXISTS ${key};`);
			tx.executeSql(`CREATE TABLE IF NOT EXISTS ${key}(${mapSchemaToSQL(SQLITE.schema[key])});`);
		})
		let version = hash(SQLITE,{encoding: 'base64'}).substr(0,10);
		tx.executeSql(`INSERT INTO Version (_id) VALUES ('${version}');`);
		console.log('DB is ready, version: '+version);
	}

	async get(className, ids){
		let db;
		
		return this._connect().then(DB=>{
			db = DB;
			return this._get(db, className, ids);
		}).then( rows => {
			db.close();			
			return rows;
		});
	}

	async raw(className, sql){
		return this._connect().then(DB => {
			db = DB;
			return db.executeSql(sqlTranslateDate(sql));
		}).then(resp => {
			db.close();
			let schema = SQLITE.schema[className];			
			return mapRows(resp[0].rows, r => decode(schema, r || null));
		})
	}

	async fetch(className, fields, sql, sort, limit, skip){
		fields = fields? (fields instanceof Array? fields.join(',') : fields) : '*';
		let output = `SELECT ${fields} FROM ${className}`;

		if (sql)	output += ` WHERE ${sql}`;
		if (sort) output += ` ORDER BY ${sort}`;
		if (limit) output += ` LIMIT ${limit}`;		
		if (skip) output += ` OFFSET ${skip}`;		

		return this.raw(className, output);
	}

	async save(className, objects) {
		let db, objectList;

		if (!(objects instanceof Array))
			objectList = [objects];		
		else
			objectList = [...objects];
		
		return this._connect().then(DB => {
			db = DB;
			return Promise.sequence(
				objectList.map( o => () => this._insertOrUpdate(db, className, o) )
			)
		}).then(resp => {
			db.close();

			if (!(objects instanceof Array))
				return resp[0]
			else
				return resp;
		})
	}

	async delete(className, ids){
		let db, deletedRows;

		return this._connect().then(DB=>{
			db = DB;
			return this._get(db, className, ids);
		}).then(rows => {
			deletedRows = rows;

			let query = `_id = '${ids}'`;
			if (ids instanceof Array)
				query = ids.map(id => `_id = '${id}'`).join(' OR ');

			return db.executeSql(`DELETE FROM ${className} WHERE ${query}`);
		}).then( resp => {
			db.close();			
			return deletedRows;
		});
	}

	async _get(db, className, ids) {
		let query = `_id = '${ids}'`;
		if (ids instanceof Array)
			query = ids.map(id => `_id = '${id}'`).join(' OR ');

		return db.executeSql(`SELECT * FROM ${className} WHERE ${query}`).then(resp => {
			let rows = resp[0].rows;
			let schema = SQLITE.schema[className];			
			if (ids instanceof Array)
				return mapRows(rows,r => decode(schema, r || null));
			else
				return decode(schema, rows.item(0) || null);
		})
	}

	async _getOne(db, className, id){
		return db.executeSql(`SELECT * FROM ${className} WHERE _id = '${id}'`).then( resp => {
			if (resp[0].rows.length === 1)
				return decode(className, resp[0].rows.item(0));
			else
				return null;
		})
	}

	async _insertOrUpdate(db, className, object){
		return db.executeSql(`SELECT * FROM ${className} WHERE _id = '${object._id}'`).then(resp => {
			if (resp[0].rows.length === 1)
				return this._update(db, className, object);
			else
				return this._insert(db, className, object);			
		});
	}

	async _insert(db, className, object) {
		return db.executeSql(`INSERT INTO ${className} ${insertEncode(SQLITE.schema[className],object)}`).then(resp=>{
			if (resp[0].rowsAffected === 1)
				return this._getOne(db, className, object._id);
			return null;
		});
	}

	async _update(db, className, object) {
		return db.executeSql(`UPDATE ${className} SET ${updateEncode(SQLITE.schema[className],object)}`).then(resp=>{
			if (resp[0].rowsAffected === 1)
				return this._getOne(db, className, object._id);
			return null;
		});
	}

	async clearAll() {
		console.log("TODO: SQLite delete all function");
		return null;
	}
}

let sqlite = new SQLite();

module.exports = sqlite;