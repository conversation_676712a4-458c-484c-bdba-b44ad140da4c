import { SQLITE } from 'Config';

export default function encode(classNameOrSchema, object){
	let schema = classNameOrSchema;
	
	if (typeof classNameOrSchema === 'string')
		schema = SQLITE.schema[classNameOrSchema];

	let keys = Object.keys(object);
	keys.notInArray(Object.keys(schema)).forEach(k => {
		console.log(`${k} is not supported in SQLite, edit the schema to fix this.`);
	})
	keys = keys.filter( k => schema[k] );
	let values = keys.map( k => {
		if (object[k] === null)	return 'NULL';

		switch(schema[k].type){
			case 'id': 			return `'${object[k]}'`;
			case 'string': 	return `'${object[k]}'`;
			case "json": 		return `'${JSON.stringify(object[k])}'`;
			case "int": 		return object[k];
			case "float": 	return object[k];
			case "double": 	return object[k];
			case "bool": 		return object[k] === true ? 1 : object[k] === false ? 0 : 'NULL';
			case "date": 		return object[k] instanceof Date? object[k].getTime() : Date.parse(object[k]);
			default: return 'NULL';			
		}
	})

	return `(${keys.join(',')}) VALUES (${values.join(',')})`;
}