import { SQLITE } from 'Config';

export default function encode(classNameOrSchema, object){
	let schema = classNameOrSchema;
	
	if (typeof classNameOrSchema === 'string')
		schema = SQLITE.schema[classNameOrSchema];

	let keys = Object.keys(object);
	keys.notInArray(Object.keys(schema)).forEach(k => {
		console.log(`${k} is not supported in SQLite, edit the schema to fix this.`);
	})

	keys = keys.filter( k => schema[k] && k !== '_id' );
	let values = keys.map( k => {
		if (object[k] === null)	return `${k} = NULL`;

		switch(schema[k].type){
			case 'id': 			return `${k} = '${object[k]}'`;
			case 'string': 	return `${k} = '${object[k]}'`;
			case "json": 		return `${k} = '${JSON.stringify(object[k])}'`;
			case "int": 		return `${k} = ${object[k]}`;
			case "float": 	return `${k} = ${object[k]}`;
			case "double": 	return `${k} = ${object[k]}`;
			case "bool": 		return `${k} = ${object[k] === true ? 1 : object[k] === false ? 0 : 'NULL'}`;
			case "date": 		return `${k} = ${object[k] instanceof Date? object[k].getTime() : Date.parse(object[k])}`;
			default: return null;
		}
	}).filter(v => v).join(',');

	return values+` WHERE _id = '${object._id}'`;
}