import { SQLITE } from 'Config';

export default function decode(classNameOrSchema, object){
	if (object === null || object === undefined)
		return object;

	let schema = classNameOrSchema;
	
	if (typeof classNameOrSchema === 'string')
		schema = SQLITE.schema[classNameOrSchema];

	let keys = Object.keys(object);
	let output = {};

	keys.forEach(k => {
		if (schema[k].type === 'json')
			output[k] = JSON.parse(object[k]);
		else if (schema[k].type === 'date')
			output[k] = new Date(object[k]);
		else if (schema[k].type === 'bool')
			output[k] = object[k] === 1;
		else
			output[k] = object[k];
	})

	return output;
}