module.exports = {
	drawingUnsaved: "所有画图将不会储存,是否继续进行?",
	remarksCannotBeEmpty: "必需填写'注'",
	statusCannotBeEmpty: "必需选择'单位状态'",
	notValidUnit: "单位不正确",
	welcomeMsg: '欢迎',
    updateFound: 'New version found, please update your app',
	noInternetConnection: '没有网络连接！',
	noWifiDownload: '下载需时并会衍生流动数据费用, 建议先连接Wifi网络',
	noWifiUpload: '上载需时并会衍生流动数据费用, 建议先连接Wifi网络',
	KeyAskForInput: "借用人和电话不能为空。",
	unitNotAvailable: "单位不正确",
	tips: '提示',
	replaceData: '待处理的项目会被替换',
	handoverFormBack: '表格内容将不会储存,是否继续进行?',
	owner2MissingPhoneNumber: '请填上业主2的联络电话',
	invalidPhoneFormat: '电话号码格式不正确, 请只填上数字',
	noCameraPermission: "未能使用相机",
	pleaseSetCameraPermission: "e-IHS并未获准使用相机, 请到系统设定并容许e-IHS使用相机",
    getItemErrMsg: "搜寻期间因意外问题中断了, 请重新尝试",
    itemSearchMandatoryMsg: "请在执行搜寻前选择'座数','层数'",
    unitSearchMandatoryMsg: "请在执行搜寻前选择'座数','层数'",
    taskSearchMandatoryMsg: "请在执行搜寻前选择'座数','层数'",
    noDownLoadSelectionWarningMsg: "请至少选择一个项目进行下载"
};