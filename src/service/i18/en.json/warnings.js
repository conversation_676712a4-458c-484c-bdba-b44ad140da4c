module.exports = {
	drawingUnsaved: "The drawing(s) is/are not saved and will be lost if going back to previous page. Do you want to continue?",
	remarksCannotBeEmpty: "'Remarks' cannot be empty.",
	statusCannotBeEmpty: "'Status' cannot be empty.",
	notValidUnit: "The unit is not valid",
	welcomeMsg: 'Welcome',
	updateFound: 'New version found, please update your app',
	noInternetConnection: 'No Internet Connection!',
	noWifiDownload: 'It is recommended that you connect to a Wifi network to shorten download time and to avoid mobile data charges',
	noWifiUpload: 'It is recommended that you connect to a Wifi network to shorten upload time and to avoid mobile data charges',
	KeyAskForInput: "'Borrower' and 'Phone' cannot be empty.",
	unitNotAvailable: "The selected unit is not available",
	tips: 'Tips',
	replaceData: 'All pending items will be replaced',
	handoverFormBack: 'The unsubmitted form will be lost if going back to previous page. Do you want to continue?',
	owner2MissingPhoneNumber: 'Missing phone number for owner 2',
	invalidPhoneFormat: 'The phone number is invalid, please input the phone number with numbers only',
	noCameraPermission: "No Camera Permission",
	pleaseSetCameraPermission: "This app is not allowed to use the camera, please go to Settings and enable the Camera permission for this app.",
    getItemErrMsg: "Unexpected issue interrupted during the search, please try again",
    itemSearchMandatoryMsg: "Please ensure 'Tower', 'Floor' has been selected upon searching execution",
    unitSearchMandatoryMsg: "Please ensure 'Tower', 'Floor' has been selected upon searching execution",
    taskSearchMandatoryMsg: "Please ensure 'Tower', 'Floor' has been selected upon searching execution",
    noDownLoadSelectionWarningMsg: "Please at least select one entry for download"
};