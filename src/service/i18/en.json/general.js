module.exports = {
	Skip: "Skip",
	Settings: "Settings",
	Cancel: "Cancel",
	OK: "OK",
	Finish: "Finish",
	open: "Open",	
	Close: "Close",
	Save: "Save",
	NoTask: 'No Task List available. Please click "Download" to retry',
	NoUnit: 'No Unit List available. Please click "Download" to retry',
	NoItem: 'No Item List available. Please click "Download" to retry',
	NoTaskUpload: "No Pending Item",
	Submit: "Submit",
	Skip: 'Skip',
	AskForSubmitCheckList: 'Confirm submit checklist?',
	AskForSubmitUnit: 'Confirm submit unit?',
	AskForSubmitItem: 'Confirm submit item?',
	WaittingForUpload: 'Upload',
	filter: 'Filter',
	Details: 'Details',
	lastModfiy: 'Last Modfied',
	filled: 'Filled',
	chooseAll: 'Choose All',
	deselect: 'Deselect',
	download: 'Download',
	chooseBack: 'Back',
	choose: 'Choose',
	tower: 'Tower',
	floor: 'Floor',
	flat: 'Flat',
	location: 'Location',
	level1: 'Level 1',
	level2: 'Level 2',
	remarks: 'Remarks',
	importantItem: 'Important Item',
	yes: 'Yes',
	no: 'No',
	delete: 'Delete',
	update: 'Update',
	lastLogin: 'Last Login',
	shouldInputPhotoForFailed: 'Please upload image for failded item',
	shouldInputPhotoForSatisfy: 'Please upload image for satisfied item',
	changeLanguage: 'Change Language',
	logout: "Logout",
	formSubmission: 'Form Submission',
	keyManagement: 'Key Management',
	lastName: 'Last Name',
	firstName: 'First Name',
	email: 'Email',
	phone: 'Phone',
	address: 'Address',
	meterNumber: 'Meter Number',
	meterReading: 'Meter Reading',
	waterMeterNumber: 'Water Meter Number',
	waterMeterReading: 'Water Meter Reading',
	gasMeterNumber: 'Gas Meter Number',
	gasMeterReading: 'Gas Meter Reading',
	form: 'Form',
	form1: '交樓記錄表',
	form2: '供水申請表',
	form3: '意見表',
	form4: '住戶證申請及簽收記錄',
	updateRecord: 'Update Record',
	keyState: 'Key Status',
	lend: '借出',
	spoiled: '已還匙',
	unitState: 'Unit Status',
	ownerKeepKey: '業主留匙',
	ownerNotKeepKey: '未交樓單位',
	ownerGotIn: '單位已入住',
	keyID: 'Key ID',
	borrower: 'Borrower',
	reason: 'Reason',
	otherReason: 'Remarks',
	ChangeReason: 'Change Reason',
	fillStatus: 'Status',
	unitType: 'Unit Type',
	stage: 'Stage',
	internalStatus: 'Internal Status',
	signOff: 'Sign Off',
	currentProject: 'Current Project',
	lastItem: 'Previous',
	nextItem: 'Next',
	proprietor: 'Owner',
	emergencyContact: 'Emergency Contact',
	primaryContact: 'Primary Contact',
	photo: 'Photo',
	chinese: 'Chinese',
	english: 'English',
	other: 'Other',
	waterContact: 'Water Contact',
	electricalContact: 'Electrical Contact',
	confirm: 'Confirm',
	termsofuse: 'Terms of Use',
	reset: 'Reset',
	done: 'Done',
	drawing: 'Drawing',
	marking: 'Marking',
	erase: 'Erase',
	SelectAction: "Select Action",
	TakePhoto: "Take Photo",
	ChoosePhoto: "Choose From Album",
	deleteForm: "Delete form '",
	deleteFormMsg: 'The saved data will be deleted, confirm to delete?',
	changePassword: "Change Password",
	oldPassword: "Old Password",
	newPassword: "New Password",
	cfmPassword: "Confirm Password",
	changePassword_success: "Password changed successfully",
	changePassword_failed: "Failed to change the password, please try again later",
	changePassword_wrongPassword: "Failed to change the password: wrong password",
	changePassword_tryAgain: "Failed to change the password, please try again",
	search: "Search",
	retry: "Please retry",
	wait: "Please wait",

	refreshSession: "Refresh Login Session",
    refreshSuccess: "Login session was successfully renewed.",
    refreshFailed: "Unable to renew login session, please try again later.",
	incorrectCredential: "Incorrect login credential, please confirm and retry."

};