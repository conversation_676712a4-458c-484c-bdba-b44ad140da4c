/**
 * @providesModule Server
 */
import { DEBUG, OFFLINE_MODE } from 'Config';
import { _get, _post, _put, _delete, _setUser, _logout, _upload } from './request.js';
import { delay, checkParam } from 'Utilities';

export async function autoLogin(user){
	return _setUser(user);
}

async function login(username, password) {
	return _post('auth/login', { username, password }, true).then(resp => {
		if (resp && resp.token)
			return _setUser(resp).then(r => resp);
		else
			return resp;
	});
}

async function adValidate(sessionID) {
	return _post('saml/validate', { sessionID }, true).then(resp => {
		if (resp && resp.token)
			return _setUser(resp).then(r => resp);
		else
			return resp;
	});
}

async function logout() {
  return _logout('auth/logout').then(async () => {
    await _setUser(null)
  })
}

async function forgotPassword(email) {
	return _post('auth/forgetPassword', { email: email }).then(resp => {
		return resp;
	});
}

async function test(data = { someParam: 1, otherParam: "a" }) {
	return _post('test/post', data);
}

async function fetchChecklists(projectCode){
	return _get(`checklist/${projectCode}`);
}

async function fetchChecklist(projectCode, checklistId){
	return _get(`checklist/download/${projectCode}/${checklistId}`);
}

async function fetchUnitChecklist(projectCode, checklistId){
	return _get(`unit/checklist/${projectCode}/${checklistId}`);
}

async function uploadImage(file,progress){
	// return _upload(`image`,file,progress);
	return _upload(`blob_image`,file,progress);
}

async function fetchChecklistDropdownOptions(projectId){
    return _get('checklist/getSearchDropdownOptions/' + projectId);
}

async function fetchUnitDropdownOptions(projectId){
    return _get('unit/getSearchDropdownOptions/' + projectId);
}

async function fetchItemDropdownOptions(projectId){
    return _get('checklistRespItem/getSearchDropdownOptions/' + projectId);
}

async function fetchProjectLists(){
    return _get('project/');
}

async function fetchChecklistList(project){
	return _post('checklistTemplate/list', { project: project }).then(resp => {
		return resp;
	});
}

async function fetchProjectDetail(projectCode){
	return _get(`project/${projectCode}`);
}

async function createChecklistResp(objForResp) {
	console.log(objForResp);
	return _post('checklistResp/create', objForResp).then(resp => {
		return resp;
	});
}

async function createChecklistRespItem(checklist_resp, checklist_item, data, checklistId, originalId, checkingRequired) {
	// console.log("--------")
	// console.log(checklist_resp);
	// console.log(checklist_item);
	// console.log(data);
	return _post('checklistRespItem/create', { checklist_resp: checklist_resp, checklist_item: checklist_item, data: data
		, checkingRequired: checkingRequired}).then(resp => {
			resp.checklistId = checklistId;
			resp.originalId = originalId;
			resp.type2 = 'checklist';
			return resp;
	});
}

async function fetchItems(project) {
	return _post('checklistRespItem/listDefect', { project: project }).then(resp => {
		return resp;
	});
}

async function fetchChecklistsCustom(project, tower, floor, unit, checklist) {
    return _post('checklist/getChecklistsCustom', {
        project: project,
        tower: tower,
        floor: floor,
        unit: unit,
        checklist: checklist
    }).then(resp => {
        return resp;
    });
}

async function fetchUnitsCustom(project, type, tower, floor, unit, stage, status_int) {
	console.log("unit",unit);
    return _post('unit/getUnitsCustom', {
        project: project,
        type: type,
        tower: tower,
        floor: floor,
        unit: unit,
        stage: stage,
        status_int: status_int,
    }).then(resp => {
        return resp;
    });
}

async function fetchRespItemsCustom(project, status, tower, floor, unit, location, level1, level2, checklist, searchDefectType) {
    return _post('checklistRespItem/getRespItemCustom', {
    	project: project,
		tower: tower,
        floor: floor,
        unit: unit,
        status: status,
        location: location,
        level1: level1,
        level2: level2,
        checklist: checklist,
        searchDefectType: searchDefectType
	}).then(resp => {
        return resp;
    });
}

async function fetchItem(itemId){
	return _get(`checklistRespItem/get/${itemId}`);
}

async function fetchItemForUnit(itemId){
    return _get(`checklistResp/get/${itemId}`);
}

// Get runtime valid checklist resp
async function fetchValidCheckListResp(unit) {
    return _get(`checklistResp/getValidResponses/${unit}`);
}
async function addMessage(message, checklistRespItem) {
	return _post(`checklistRespItem/${checklistRespItem}/sendMessage`, { messages: message }).then(resp => {
		return resp;
	});
}

async function updateChecklistRespItem(checklist_resp, data) {
	return _put(`checklistRespItem/${checklist_resp}/finish`, data).then(resp => {
		resp.type2 = 'item';
		return resp;
	});
}

async function updateUnit(id, data) {
	return _put(`unit/update`, data).then(resp => {
		resp[0].type2 = 'unit';
		return resp[0];
	});
}

async function fetchUnits(project) {
	return _post('unit/list/all', { project: project }).then(resp => {
		return resp;
	});
}

async function fetchUnit(unitId){
	return _get(`unit/download/${unitId}`);
}

async function fetchUnitInfo(project){
	return _post('unit/listInfo', { project: project }).then(resp => {
		return resp;
	});
}

async function fetchItemStatusSummary(unitId){
    return _get(`unit/getItemStatusSummary/${unitId}`);
}

async function fetchChecklistRespSummary(unitId){
    return _get(`unit/getChecklistRespSummary/${unitId}`);
}

async function updateKey(data) {
	return _put(`key/update`, data).then(resp => {
		resp[0].type2 = 'key';
		if(data.originalId){
			resp[0].originalId = data.originalId;
		}
		return resp[0];
	});
}

async function fetchHandoverForm(project) {
	return _post('handoverForm/list', { project: project }).then(resp => {
		return resp;
	});
}

async function updateHandoverData(data) {
	return _put('unit/updateHandover', data).then(resp => {
		return resp;
	});
}

async function updateHandoverForm(data) {
	return _post('unit/uploadHandover', data).then(resp => {
		return resp;
	});
}

async function uploadPdf(file,progress) {
	return _upload(`blob_pdf`,file,progress);
}

async function getTermsOfUse() {
	return _get("termsOfUse");
}

async function fetchUsers() {
	return _post('user/names');
}

async function changePassword(params) {
	return _put('user/changePassword', params);
}

async function getAreaCode() {
	return _get('areaCode');
}

module.exports = {
	autoLogin,
	setUser: _setUser,
	test,
	login,
	adValidate,
	logout,
	forgotPassword,
	uploadImage,
	fetchChecklists,
	fetchUnitChecklist,
	fetchChecklist,
    fetchChecklistDropdownOptions,
    fetchUnitDropdownOptions,
    fetchItemDropdownOptions,
	fetchProjectLists,
	fetchChecklistList,
	fetchProjectDetail,
	createChecklistResp,
	createChecklistRespItem,
    fetchChecklistsCustom,
    fetchUnitsCustom,
    fetchRespItemsCustom,
	fetchItems,
	fetchItem,
	addMessage,
	updateChecklistRespItem,
	fetchUnits,
	fetchUnit,
	fetchItemForUnit,
    fetchValidCheckListResp,
	updateUnit,
	fetchUnitInfo,
    fetchItemStatusSummary,
    fetchChecklistRespSummary,
	updateKey,
	fetchHandoverForm,
	updateHandoverData,
	updateHandoverForm,
	uploadPdf,
	getTermsOfUse,
	fetchUsers,
	changePassword,
	getAreaCode
}
