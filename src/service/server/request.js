/**
 * @providesModule Request
 */

import { SERVER_URL, DEBUG, OFFLINE_MODE, API_KEY } from 'Config'
import { delay, checkParam } from 'Utilities'
import RNFetchBlob from 'react-native-fetch-blob'
import { Crashlytics, Answers } from 'react-native-fabric'
import pinch from 'react-native-pinch'
import { NativeModules, DeviceEventEmitter, Platform, Alert } from 'react-native'

let accessToken
const RNUploader = NativeModules.RNUploader
const FileTransfer = require('@remobile/react-native-file-transfer')

function _getHeaders () {
  return {
    'x-pm-api-key': API_KEY,
    'x-pm-access-token': accessToken
  }
}

function _getServerUrl () {
  let serverUrl = SERVER_URL
  if (!serverUrl.endsWith('/'))
    serverUrl = serverUrl + '/'

  return serverUrl
}

async function _fetch(path, option){
    return new Promise((resolver, rejector)=>{
        var xhttp = new XMLHttpRequest();

        // xhttp.timeout = 10000; // time in milliseconds

        xhttp.open(option.method, path, true);

        xhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                resolver(xhttp.responseText);
            } else if (this.readyState === 4){
                rejector(xhttp.responseText);
            }
        };

        Object.keys(option.headers).forEach(header => {
            xhttp.setRequestHeader(header, option.headers[header]);
        })

        xhttp.send(option.body);
    });
}

/*
async function _fetch (path, option) {
  if (path.startsWith('https://')) {
    return pinch.fetch(path, {
      method: option.method,
      headers: option.headers,
      body: option.body,
      timeoutInterval: 10 * 60 * 1000, // timeout after 10 seconds
      sslPinning: {
        cert: 'test.com', // cert file name without the `.cer`
      }
    }).then(res => res.bodyString)
      .catch(err => {
        if (Platform.OS !== 'ios') {
          Crashlytics.logException(`Cannot sent ${path} ${JSON.stringify(err)}`)
        } else {
          Crashlytics.recordError(`Cannot sent ${path} ${JSON.stringify(err)}`)
        }
        throw err
      })
  } else {
    return fetch(path, {
      method: option.method,
      headers: option.headers,
      body: option.body
    }).then(response => {
      return response.text()
    }).catch(err => {
      console.log(err)
      throw err
      // return Alert.alert(`Cannot download ${err.message}`)
    })
  }
}
*/

async function _req (method, path, body, ignoreToken) {

  let headers = {
    'x-pm-api-key': API_KEY,
    'Content-Type': 'application/json'
  }

  if (accessToken && !ignoreToken)
    headers['x-pm-access-token'] = accessToken

  let option = {method, headers}

  if (method !== 'GET') {
    if (!body) {
      body = {_limit: -1}
    } else if (!('_limit' in body)) {
      body._limit = -1
    }
    option.body = JSON.stringify(body)
  }

  let serverUrl = _getServerUrl()

  console.logTime('start', serverUrl, path)
  if (OFFLINE_MODE)
    return delay(3000).then(() => ({}))
  else
    return _fetch(serverUrl + path, option)
    // .then(resp => resp.text())
      .then(resp => {
        console.logTime('end', serverUrl, path)
        let json
        try {
          json = JSON.parse(resp)
        } catch (err) {
          return resp
        }

        if (json.error) {
          if (json.error === 'Forbidden' || json.error === 'Unauthorized')
            Alert.alert('User session expired, please logout and login again')
          throw json
        }
        return json
      })

}

async function _get (path, params, ignoreToken = false) {
  if (checkParam(params, 'object'))
    path = path + '?' + Object.keys(params).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join('&')

  return _req('GET', path, ignoreToken)
}

async function _post (path, body, ignoreToken = false) {
  return _req('POST', path, body, ignoreToken)
}

async function _logout (path) {
  return _req('POST', path, {token: accessToken}, true)
}

async function _put (path, body, ignoreToken = false) {
  return _req('PUT', path, body, ignoreToken)
}

async function _delete (path, body, ignoreToken = false) {
  return _req('DELETE', path, body, ignoreToken)
}

async function _setUser (user) {
  if (user)
    accessToken = user.token
  else
    accessToken = null
}

async function _upload (path, file, progress) {
  progress = progress ? progress : () => {}

  let ext = file.fileName.split('.')
  ext = ext[ext.length - 1]
  ext = ext.toLowerCase()

  let serverUrl = _getServerUrl()

  if (Platform.OS === 'ios') {
    DeviceEventEmitter.addListener('RNUploaderProgress', progress)

    let files = [{
      name: 'file',
      filename: file.fileName,
      filepath: file.origURL,
      filetype: 'image/' + ext,
    }]

    if (ext == 'pdf') {
      files[0].filetype = 'application/pdf'
    }

    let opts = {
      url: serverUrl + 'upload/' + path,
      files: files,
      method: 'POST',
      headers: {
        'x-pm-api-key': API_KEY,
        'x-pm-access-token': accessToken,
        'Accept': 'application/json'
      },
    }

    return new Promise((resolver, rejector) => {
      RNUploader.upload(opts, (err, response) => {
        DeviceEventEmitter.removeListener('RNUploaderProgress', progress)
        if (err)
          rejector(err)
        else {
          try {
            let json = response.data
            if (response.status === 200)
              resolver(json)
            else
              rejector(json)
          } catch (err) {
            rejector(err)
          }
        }
      })
    })
  } else {
    const fileTransfer = new FileTransfer()
    fileTransfer.onprogress = progress

    const fileURL = file.origURL
    const options = {}
    options.fileKey = 'file'
    options.fileName = file.fileName
    options.mimeType = 'image/' + ext
    if (ext == 'pdf') {
      options.mimeType = 'application/pdf'
    }

    options.headers = {
      'x-pm-api-key': API_KEY,
      'x-pm-access-token': accessToken,
      'Accept': 'application/json'
    }
    return new Promise((resolver, rejector) => {
      fileTransfer.upload(
        fileURL,
        serverUrl + 'upload/' + path,
        (response) => {
          console.log('fileTransfer.upload', response)
          try {
            let json = response
            if (response.responseCode === 200)
              resolver(json.response)
            else
              rejector(json)
          } catch (err) {
            rejector(err)
          }
        },
        (error) => {
          console.log('fileTransfer.upload', error)
          rejector(error)
        },
        options
      )
    })
  }
}

module.exports = {
  _get,
  _post,
  _put,
  _delete,
  _setUser,
  _logout,
  _upload,
  _getHeaders,
  _getServerUrl
}
