/**
 * @providesModule Key
 */

import * as SecureStore from 'expo-secure-store';
import { randomString } from '../../util/utilities';

import { decrypt, encrypt } from './aes.js';

var _key;

function key() {
  return Promise.resolve().then(async () => {
    if (_key) return _key;

    try {
      const value = await SecureStore.getItemAsync('key');
      if (!_key) {
        if (!value) {
          _key = randomString(64);
          const encryptedValue = encrypt(_key);
          await SecureStore.setItemAsync('key', encryptedValue);
        } else {
          _key = decrypt(value);
        }
      }
      return _key;
    } catch (error) {
      console.warn('Error accessing secure store:', error);
      // Fallback: generate a new key if there's an error
      _key = randomString(64);
      return _key;
    }
  });
}

module.exports = key;
