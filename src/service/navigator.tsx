/**
 * @providesModule Navigator
 */

import React, { useEffect } from 'react';
import { View } from 'react-native';

import { NavigationContainer, NavigationState } from '@react-navigation/native';
import {
  createStackNavigator,
  StackNavigationProp,
} from '@react-navigation/stack';
import { DEBUG } from 'Config';
import {
  AddHocForm,
  AdhocList,
  CheckList,
  CheckListItem,
  EditPhoto,
  FloorPlan,
  FormSubmission,
  ItemDetail,
  Items,
  ItemsDownload,
  Loading,
  LoginPage,
  MainPage,
  PhotoView,
  ReadQRcode,
  RefreshLoginSession,
  TaskDownload,
  UnitDetail,
  UnitDownload,
} from 'Page';
import { connect } from 'react-redux';
import { RootState, User } from '../types';

const { Action } = require('../redux');

// Define navigation param list
export type RootStackParamList = {
  Empty: undefined;
  Login: undefined;
  Main: undefined;
  CheckList: undefined;
  CheckListItem: undefined;
  FloorPlan: undefined;
  EditPhoto: undefined;
  PhotoView: undefined;
  TaskDownload: undefined;
  UnitDownload: undefined;
  ItemsDownload: undefined;
  ItemDetail: undefined;
  UnitDetail: undefined;
  Items: undefined;
  AddHocForm: undefined;
  AdhocList: undefined;
  FormSubmission: undefined;
  RefreshLoginSession: undefined;
  ReadQRcode: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

// Define props interface for Empty component
interface EmptyProps {
  navigation: StackNavigationProp<RootStackParamList, 'Empty'>;
  user: User | null;
  setNavigation: (navigation: any) => void;
}

const _Empty: React.FC<EmptyProps> = ({ navigation, setNavigation }) => {
  useEffect(() => {
    setNavigation(navigation);
  }, [navigation, setNavigation]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#fff',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Loading />
    </View>
  );
};

const Empty = connect(
  (state: RootState) => ({
    user: state.auth.user,
  }),
  (dispatch: any) => ({
    setNavigation: (navigation: any) =>
      dispatch(Action.navigation.setMainNavigation(navigation)),
  })
)(_Empty);

const StackNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName="Empty"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="Empty" component={Empty} />
      <Stack.Screen name="Login" component={LoginPage} />
      <Stack.Screen name="Main" component={MainPage} />
      <Stack.Screen name="CheckList" component={CheckList} />
      <Stack.Screen name="CheckListItem" component={CheckListItem} />
      <Stack.Screen name="FloorPlan" component={FloorPlan} />
      <Stack.Screen name="EditPhoto" component={EditPhoto} />
      <Stack.Screen name="PhotoView" component={PhotoView} />
      <Stack.Screen name="TaskDownload" component={TaskDownload} />
      <Stack.Screen name="UnitDownload" component={UnitDownload} />
      <Stack.Screen name="ItemsDownload" component={ItemsDownload} />
      <Stack.Screen name="ItemDetail" component={ItemDetail} />
      <Stack.Screen name="UnitDetail" component={UnitDetail} />
      <Stack.Screen name="Items" component={Items} />
      <Stack.Screen name="AddHocForm" component={AddHocForm} />
      <Stack.Screen name="AdhocList" component={AdhocList} />
      <Stack.Screen name="FormSubmission" component={FormSubmission} />
      <Stack.Screen
        name="RefreshLoginSession"
        component={RefreshLoginSession}
      />
      <Stack.Screen name="ReadQRcode" component={ReadQRcode} />
    </Stack.Navigator>
  );
};

function logNavigation(state: NavigationState | undefined) {
  if (!DEBUG) return;

  const groupStart = console.groupCollapsed || console.group || console.log;
  const groupEnd = console.groupEnd || console.log;

  const routeName = state?.routes?.[state.index]?.name;
  groupStart(
    `%c nav`,
    `color: gray; font-weight: lighter;`,
    routeName || 'Unknown'
  );
  groupEnd();
}

// Define props interface for AppNavigator
interface AppNavigatorProps {
  user: User | null;
  loading: boolean;
}

const AppNavigator: React.FC<AppNavigatorProps> = () => {
  const onStateChange = (state: NavigationState | undefined) => {
    logNavigation(state);
    // do other thing here, such as redux action.
  };

  return (
    <NavigationContainer onStateChange={onStateChange}>
      <StackNavigator />
    </NavigationContainer>
  );
};

const mapStateToProps = (state: RootState) => {
  return {
    user: state.auth.user,
    loading: state.loading.api.length > 0 || state.loading.storage.length > 0,
  };
};

const mapDispatchToProps = (_dispatch: any) => {
  return {};
};

export default connect(mapStateToProps, mapDispatchToProps)(AppNavigator);
