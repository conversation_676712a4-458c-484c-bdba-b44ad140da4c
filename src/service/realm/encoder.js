import {REALM} from 'Config';

function dateToTime(date){
	if (date instanceof Date)
		return date.getTime();

	try{
		return (new Date(date)).getTime();
	}catch(err){
		return null;
	}
}

export default function encode(className, object){
	let schema = REALM.schema.find(s => s.name === className);
	if (!schema)
		throw Error(`class ${className} not found.`);

	let fields = schema.properties;	
	let invalidField = object.trim(Object.keys(fields), true);
	Object.keys(invalidField).forEach(key => console.warn(`class ${className} not support this field: ${key}`));

	object = object.trim(Object.keys(fields));

	Object.keys(fields).forEach(key => {
		if (typeof fields[key] === 'object' && fields[key].type === 'string' && fields[key].json){
			if (typeof object[key] === 'object')
				object[key] = object[key] ? JSON.stringify(object[key]) : null;
		}else if (typeof fields[key] === 'object' && fields[key].type === 'int' && fields[key].date){
			object[key] = dateToTime(object[key]);
		}else if (typeof fields[key] === 'object' && fields[key].pointer){
			object[key] = encode(fields[key].type, object[key]);
		}else if (typeof fields[key] === 'object' && fields[key].type === 'list' && fields[key].objectType){
			object[key] = object[key].map(o => encode(fields[key].objectType, o));
		}
	})
	return object;
}