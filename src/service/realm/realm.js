/**
 * @providesModule Realm
 */

const _realm = require('realm');

import { checkParam } from 'Utilities';
import { REALM } from 'Config';

import encode from './encoder.js';
import decode from './decoder.js';
import key from 'Key';

function sqlTranslateDate(sql){
	if (sql){
		let sqlDate = sql.match(/((Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (0\d|[12]\d|3[01]) [12]\d{3} (0\d|1\d|2[0-3]):([0-5]\d):([0-5]\d) GMT(\+|\-)(0\d|1[0-2])(0|3)0 \([A-Z]{3}\)|\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))/g);

		if (sqlDate)
			sqlDate.forEach(date => {
				let timestamp = Date.parse(date);
				if (!Number.isNaN(timestamp))
					sql = sql.replace(date,timestamp);
			});
	}

	return sql;
}

async function realmKey() {
	return key().then(k => {
		// console.log('realmKey:',k);
		let str = k;
		var buf = new ArrayBuffer(64);
		var bufView = new Int8Array(buf);
		for (var i=0; i<64; i++)
			bufView[i] = str.charCodeAt(i);
		return buf;
	})
}

class Realm {
	constructor() {
		this.realm = null;
		this.setup();
	}

	async setup(){
		realmKey().then(key => {
			// console.log('Set Realm with Config:', REALM)
			this.realm = new _realm({...REALM, encryptionKey: key});
		});
	}

	/**
	 * get object / objects by id
	 * 
	 * @param {string} className - class name of the object/s
	 * @param {string | string[]} ids - id of the fetching object/s
	 * @return {object | object[]} - fetched object/s
	 * 
	 */
	async get(className, ids) {
		let { realm } = this;

		let query = `_id = "${ids}"`;
		if (ids instanceof Array)
			query = ids.map(id => `_id = "${id}"`).join(' OR ');

		return new Promise((resolver, rejector)=>{
			try {
				let decodedObjects = realm.objects(className).filtered(query).map(o => decode(className, null, o));

				if (ids instanceof Array)
					resolver(decodedObjects);
				else
					resolver(decodedObjects[0]);
			}catch(err){
				rejector(err);
			}
		})
	}

	/**
	 * delete object / objects
	 * 
	 * @param {string} className - class name of the object/s
	 * @param {string | string[]} ids - id of the removing object/s
	 * @return {object | object[]} - removed object/s
	 * 
	 */
	async delete(className, ids) {
		let { realm } = this;
		let resp;

		let query = `_id = "${ids}"`;
		if (ids instanceof Array)
			query = ids.map(id => `_id = "${id}"`).join(' OR ');

		let removedObjects = realm.objects(className).filtered(query);
		let schema = REALM.schema.find(s => s.name === className);		
		let decodedObjects = removedObjects.map(o => decode(schema, null, o));

		if (!(ids instanceof Array))
			decodedObjects = decodedObjects[0];

		return new Promise((resolver, rejector) => {
			try {
				realm.write(() => {
					realm.delete(removedObjects);
					resolver(decodedObjects);
				});
			} catch (err) {
				rejector(err);
			}
		})
	}

	async deleteAll(className){
		let { realm } = this;
		
		return new Promise((resolver, rejector)=>{
			let objects = realm.objects(className);
			try {
				realm.write(() => {
					realm.delete(objects);
					resolver(objects);
				});
			} catch (err) {
				rejector(err);
			}
		});
	}

	/**
	 * fetch object / objects by query
	 * 
	 * @param {string} className - class name of the object/s
	 * @param {string} fields - fields for fetching partial object/s
	 * @param {string} sql - sql for fetching object/s
	 * @param {string} sort - the field for sorting object/s, start from '-' mean desc
	 * @param {int} limit - limit for fetching object/s
	 * @param {int} skip - skip for fetching object/s
	 * @return {object | object[]} - fetched object/s
	 * 
	 */
	async fetch(className, fields, sql, sort, limit, skip) {
		let { realm } = this;

		if (!checkParam(sql,['array','null','undefined']))
			throw Error('fetch: fields can only be array');

		if (!checkParam(sql,['string','null','undefined']))
			throw Error('fetch: sql can only be string');
		sql = sqlTranslateDate(sql);

		if (!checkParam(sort,['string','null','undefined']))
			throw Error('fetch: sort can only be string');
		let sortDesc = sort && sort.startsWith('-')? true : false;
		if (sort && sort.startsWith('-'))
			sort = sort.substr(1);

		if (!checkParam(skip,['int','null','undefined']))
			throw Error('fetch: skip can only be string');
		if (!skip)	skip = 0;

		if (!checkParam(limit,['int','null','undefined']))
			throw Error('fetch: limit can only be string');
		if (!limit && limit!==0)	limit = 10000;

		return new Promise((resolver, rejector)=>{
			try {
				let objects = realm.objects(className);

				if (sql)	objects = objects.filtered(sql);
				if (sort)	objects = objects.sorted(sort,sortDesc);
				if (skip || limit)	objects = objects.slice(skip, limit);

				let schema = REALM.schema.find(s => s.name === className);
				resolver( objects.map(o => decode(schema, fields, o)) );
			}catch(err){
				rejector(err);
			}
		})
	}

	/**
	 * save object / objects
	 * 
	 * @param {string} className - class name of the object/s
	 * @param {object | object[]} objects - any object/s
	 * @return {object | object[]} - saved object/s
	 * 
	 */
	async save(className, objects) {
		let { realm } = this;

		if (!(objects instanceof Array))
			encodedObjects = [objects];
		else
			encodedObjects = [...objects];

		encodedObjects = encodedObjects.map(o => encode(className, o));

		return new Promise((resolver, rejector) => {
			try {
				realm.write(() => {
					encodedObjects.forEach(o => {
						realm.create(className, o, true);
					})
					resolver(objects);
				});
			} catch (err) {
				// alert(err);
				rejector(err);
			}
		})
	}



	/**
	 * clear the local db
	 */
	async clearAll() {
		let { realm } = this;

		return new Promise((resolver, rejector) => {
			try {
				realm.write(() => {
					realm.deleteAll();
				});
		
				resolver();
			} catch (err) {
				rejector(err);
			}
		});
	}
}
const realm = new Realm();

module.exports = realm;