import {REALM} from 'Config';

export default function decode(classNameOrSchema, fields, object){
	let schema = classNameOrSchema;
	if (typeof classNameOrSchema === 'string')
		schema = REALM.schema.find(s => s.name === className);

	if (!object)	return object;

	if (!schema)
		throw Error(`class ${className} not found.`);

	let props = schema.properties;

	let output = {};
	Object.keys(props).forEach(p => {
		if (fields && fields.every(f => f !== p)){
			// skip
		}else{
			if (typeof props[p] === 'object' && props[p].type === 'string' && props[p].json)
				output[p] = object[p] ? JSON.parse(object[p]) : object[p];
			else if (typeof props[p] === 'object' && props[p].type === 'int' && props[p].date)
				output[p] = object[p] ? (new Date(object[p])).toISOString() : object[p];
			else if (typeof props[p] === 'object' && props[p].pointer)
				output[p] = decode(props[p].type, null, object[p]);
			else if (typeof props[p] === 'object' && props[p].type === 'list' && props[p].objectType)
				output[p] = object[p].map(o => decode(props[p].objectType, null, o));
			else
				output[p] = object[p];
		}
	})

	return output;
}