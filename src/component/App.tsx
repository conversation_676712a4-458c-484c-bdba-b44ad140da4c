import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import {
  Alert,
  AppState,
  AppStateStatus,
  BackHandler,
  Platform,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import { connect } from 'react-redux';
import globalVal from '../../src/globalVal.js';
import { AppNavigator } from '../navigation';
import { ProgressBarState, RootState, User } from '../types';
import { Loading } from './page';

const { ProgressView } = require('../component/item');
const i18 = require('../service/i18');

const Actions = require('../redux').Action;

// Define component props interface
interface AppProps {
  loading: boolean;
  progress: ProgressBarState;
  resetProgress: () => void;
  cancelProgress: () => void;
  user: User | null;
  cameraOpenned: boolean;
}

const App: React.FC<AppProps> = ({
  loading,
  progress,
  resetProgress,
  cancelProgress,
  user,
  cameraOpenned,
}) => {
  const [appLoading, setAppLoading] = useState<boolean>(true);
  const [appState, setAppState] = useState<AppStateStatus>(
    AppState.currentState
  );
  const [inited, setInited] = useState<boolean>(false);

  const handleAppStateChange = useCallback(
    (nextAppState: AppStateStatus) => {
      console.log('cameraOpenned', cameraOpenned);
      if (
        appState.match(/inactive|background/) &&
        nextAppState === 'active' &&
        user &&
        user.username &&
        !cameraOpenned &&
        !inited
      ) {
        welcomeAlert(user);
      }
      setAppState(nextAppState);
    },
    [appState, user, cameraOpenned, inited]
  );

  const welcomeAlert = useCallback(async (user: User) => {
    const message = '';
    Alert.alert(i18.welcomeMsg + ', ' + user.username + '!', message, [
      {
        text: i18.OK,
        onPress: () => {
          globalVal.stopShowingWelcome = false;
          setInited(true);
        },
      },
    ]);
  }, []);

  useEffect(() => {
    if (Platform.OS === 'android') {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          console.log('hardware Back Press');
          return false;
        }
      );

      return () => backHandler.remove();
    }
    return undefined;
  }, []);

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, [handleAppStateChange]);

  useEffect(() => {
    if (!user && user && !globalVal.stopShowingWelcome) {
      welcomeAlert(user);
    }
  }, [user, welcomeAlert]);

  useEffect(() => {
    // Initialize app loading state
    setAppLoading(false);
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <StatusBar style="auto" />
      <AppNavigator />
      {loading || appLoading ? <Loading /> : null}
      <Modal backdropOpacity={0.5} isVisible={progress && progress.total > 0}>
        <ProgressView
          progress={progress}
          reset={resetProgress}
          cancel={cancelProgress}
        />
      </Modal>
    </View>
  );
};

const mapStateToProps = (state: RootState) => {
  if (i18.language !== state.setting.language)
    i18.setLanguage(state.setting.language);

  return {
    loading: state.loading.api.length > 0 || state.loading.storage.length > 0,
    progress: state.progressBar,
    cameraOpenned: state.setting.cameraOpenned,
    user: state.auth.user,
  };
};

const mapDispatchToProps = (dispatch: any) => {
  return {
    resetProgress: () => {
      dispatch(Actions.progressBar.reset());
    },
    cancelProgress: () => {
      dispatch(Actions.progressBar.cancel());
    },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(App);
