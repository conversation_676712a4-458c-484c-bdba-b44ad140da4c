'use strict';

import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView, Platform,
} from 'react-native';

import Text from 'react-native-text';
import { theme, nav } from 'Theme';
const Icon = require('./Icon.js');
import i18 from 'i18';

import DropDownButton from './DropDownButton.js';

class Nav extends Component {


	onOpenDropDownList(data) {
		this.props.onShowDropDownList(data);
	}

	renderDropDownButton() {
		let { dropDownData } = this.props;

		if(dropDownData){
			return dropDownData.map((item, i) => {
				let display;
				let showFilter = false;
				if(item.filter.length > 0){
					display = item.filter.toString();
					showFilter = true;
				}else {
					display = item.title;
				}

				return(
					<DropDownButton key={i} showFilter={showFilter} title={display} onOpenDropDownList={() => this.onOpenDropDownList(item)}/>
				);
			});
		}else {
			return null;
		}
	}

	render() {
		let { left, right, onLeftPress, onRightPress, showFilterButton, onSubmitFilter, isUnit, unitFirstRow, unitSecondRow, toggleLabels, toggledVal } = this.props;

		let isUnitHeight = isUnit ? 50 : 0;

		let showFilterButtonHeight = showFilterButton  ? 40 : 0;

		let additionalHeight = isUnitHeight + showFilterButtonHeight;

		let displayFilterButton = showFilterButton ? (
			<View style={{height: additionalHeight, justifyContent: 'center', alignItems: 'center'}}>
				<TouchableOpacity onPress={onSubmitFilter}>
					<View style={{
							width: Dimensions.get('window').width * 0.85 , 
							height: 31, justifyContent: 'center', 
							alignItems: 'center', borderWidth: 1, 
							borderColor: '#FD864A', 
							backgroundColor: '#FFFFFF',
							borderRadius: 4,
						}}>
						<Text style={{color: '#FD894D', fontSize: 16}}>{i18.filter}</Text>
					</View>
				</TouchableOpacity>	
			</View>
		) : null;


		let showUnitFirstRow = unitFirstRow ? <Text style={{color: '#404040', fontSize: 14}}>{unitFirstRow}</Text> : null;

		let showUnitSecondRow = unitSecondRow ? <Text style={{color: '#404040', fontSize: 14}}>{unitSecondRow}</Text> : null;

		let showUnit = isUnit ? (
			<View style={{height: additionalHeight, justifyContent: 'center', alignItems: 'center', marginBottom: 10}}>
				<TouchableOpacity onPress={()=>{
					this.props.showUnitDropDown();
					}}>
					<View style={{
						width: Dimensions.get('window').width, 
						height: 50, justifyContent: 'center', 
						alignItems: 'center', 
						backgroundColor: '#FFFFFF',
						flexDirection: 'row',
					}}>
						<View style={{flex: 1.5, justifyContent: 'center', alignItems: 'center'}}>
							<Icon style={[styles.icon,{fontSize: 25, color: '#333333'}]} icon={'UNITCHECKLIST'}/>
						</View>	
						<View style={{flex: 2, justifyContent: 'center'}}>
							{showUnitFirstRow}
						</View>	
						<View style={{flex: 2, justifyContent: 'center'}}>
							{showUnitSecondRow}
						</View>
						<View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
							<Icon style={[styles.icon,{fontSize: 9, color: '#333333'}]} icon={'DROPDOWN'}/>
						</View>		
					</View>
				</TouchableOpacity>	
			</View>
		) : null;

		let submitButton = <View style={[styles.empty, {flex: 1}]}/>;

		if(onRightPress && (right == "SUBMIT" || right == "SAVE" || right == "SKIP")) {
			let buttonName;
			if(right == "SUBMIT"){
				buttonName = i18.Submit;
			}
			if(right == "SAVE"){
				buttonName = i18.Save;
			}

			if(right == 'SKIP'){
				buttonName = i18.Skip;
			}

			submitButton = (
				<TouchableOpacity onPress={onRightPress} style={{flex: 1, alignItems:'flex-end'}}>
					<View style={{
						width: 80,
						height: 31,
						alignItems:'center',
        				justifyContent:'center',
						borderWidth: 1,
						borderColor: 'rgba(255, 237, 219, 1.0)',
						borderRadius: 4,
						marginRight: 10,
						marginTop: 5,
					}}>
						<Text style={{color: 'rgba(255, 237, 219, 1.0)', fontWeight: 'bold'}}>{buttonName}</Text>
					</View>
				</TouchableOpacity>		
			)
		} else if(onRightPress && right === "TOGGLE") {
            submitButton = (
                <View style={styles.toggleBtnContainer}>
                    <TouchableOpacity style={[styles.toggleBtn, toggledVal === 0? styles.toggleColorRed: (toggledVal === 1? styles.toggleColorGreen: styles.toggleColorOrange)]}
                                      onPress={onRightPress}>
						<Text style={[styles.toggleBtnTxt]}>
							{toggleLabels[toggledVal]}
						</Text>
					</TouchableOpacity>
                </View>
            )
        } else if(onRightPress &&  right != "SUBMIT" && right != "SAVE" && right != "SKIP") {
			submitButton = (
				<TouchableOpacity onPress={onRightPress} style={{flex: 1, alignItems:'flex-end', marginRight: 5}}>
					<Icon style={[styles.icon,{fontSize: 25, color: theme}]} icon={right}/>
				</TouchableOpacity>
			)
		}
		return (
			<View style={[styles.container, {height: (this.props.showDropDown ? 110 : 75) + additionalHeight}]}>
				<View style={[styles.upperContainer]}>
					{ onLeftPress ? <TouchableOpacity onPress={onLeftPress} style={{flex: 1}}><Icon style={[styles.icon,{fontSize: 25, color: theme}]} icon={left}/></TouchableOpacity>: <View style={[styles.empty, {flex: 1}]}/> }
					<View style={{flex: 2, justifyContent: 'center', alignItems: 'center',}}>
						<Text style={styles.title}>{this.props.children}</Text>
					</View>	
					{submitButton}					
				</View>
				{showUnit}
				<View style={{height: this.props.showDropDown ? 40 : 0}}>
					<ScrollView horizontal={true}>
						<View style={{width: 2}} />	
						{ this.renderDropDownButton() }
						<View style={{width: 11}} />			
					</ScrollView>
				</View>
				{displayFilterButton}
			</View>
		);
	}
}

const styles = StyleSheet.create({
	container: {
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: nav.bg,
	},
	upperContainer: {
		height: 60,
		width: Dimensions.get('window').width,
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
	},
	icon: {
		width: 50,
		backgroundColor:'transparent',
		textAlign:'center',
	},
	empty:{
		width: 50,
	},
	title:{
		fontSize: 16,
		lineHeight: 22,
		fontWeight: "600",
		color: nav.title,
		backgroundColor:'transparent',
		textAlign:'center',
	},
    toggleBtnContainer:{
        flex: 1,
        alignItems:'flex-end'
    },
    toggleBtn:{
        width: 90,
        height: 31,
        alignItems:'center',
        justifyContent:'center',
        borderWidth: 1,
        borderRadius: 4,
        marginRight: 10,
        marginTop: 5,
    },
    toggleBtnTxt:{
		color: '#FFF',
        fontSize: 11,
		fontWeight: '700'
    },
	toggleColorOrange: {
        borderColor: '#fd7f34',
        backgroundColor: '#fd7f34',
	},
    toggleColorGreen: {
        borderColor: '#69b29d',
        backgroundColor:'#69b29d',
    },
    toggleColorRed: {
        borderColor: '#C77777',
        backgroundColor:'#C77777',
    },
});

module.exports = Nav;