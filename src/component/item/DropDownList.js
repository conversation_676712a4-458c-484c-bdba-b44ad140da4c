import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
	ScrollView,
} from 'react-native';

import Text from 'react-native-text';
import { theme, nav } from 'Theme';
const Icon = require('./Icon.js');
import i18 from 'i18';

export default class DropDownList extends Component {

    renderDropDownList() {


        if(Object.keys(this.props.dropDownList).length > 0){
            return this.props.dropDownList.list.map((item, i) => {
                let showChecked = this.props.dropDownList.filter.includes(item) ? (<Icon icon={'checked'} style={styles.checkIcon} />) : null;
                
                return(
                    <TouchableOpacity key={i} onPress={() => {
                        this.props.onFilterList(this.props.dropDownList.title, item, this.props.dropDownList.filter);
                    }}>
                        <View style={{ width: '100%',height:60, borderBottomWidth: 1, alignItems: 'center', justifyContent: 'center', flexDirection: 'row'}}>
                            <View style={{flex: 1}}/>
                            <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                                <Text>{item}</Text>
                            </View>	   
                            <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                                {showChecked}
                            </View>    
                        </View>	
                    </TouchableOpacity>
                );
            });
        }else {
            return null;
        }
    }

    render() {

        let showRefreshButton = this.props.closeDropDownList ? (
            <TouchableOpacity style={{flex: 1, alignItems: 'flex-end', justifyContent: 'center'}}  onPress={() => {
                this.props.cleanDropDownList(this.props.dropDownList.title);
                }}> 
                {/* <Icon icon={'refresh'} style={styles.checkIcon} /> */}
                <View><Text style={{color: '#FF8238', marginRight: 10}}>Clear</Text></View>
            </TouchableOpacity>  
            ) : <View style={{flex: 1}}/>;

        return (

            <View style={{
                position: 'absolute',
                width: Dimensions.get('window').width,
                height: Dimensions.get('window').height,
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                <View style={styles.dropDownListContainer}>
                    <View style={{height: 50, alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: 1, backgroundColor: "#131913", flexDirection: 'row'}}>
                        <View style={{flex: 1}}/>
                        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                            <Text style={{color: '#fff'}}>{this.props.dropDownList.title}</Text>
                        </View>
                        {showRefreshButton}
                    </View>	
                    <ScrollView>
                        { this.renderDropDownList() }
                    </ScrollView>
                    <TouchableOpacity style={{height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'lightgray'}} onPress={() => {
                        this.props.closeDropDownList ? this.props.closeDropDownList() : this.props.cleanDropDownList(this.props.dropDownList.title)
                    }}>
                        <Text>{i18.Close}</Text>
                    </TouchableOpacity>

                </View>
            </View>  
        );
    }
}


const styles = StyleSheet.create({
    dropDownListContainer: {
		backgroundColor: '#fff',
		width: Dimensions.get('window').width * 0.85,
		height: Dimensions.get('window').height * 0.8,
		marginBottom: Dimensions.get('window').height * 0.05,
		borderWidth: 1,
    },
    checkIcon: {
        color: '#FF8238',
        width: 52,
		fontSize: 32,
		backgroundColor:'transparent',
		textAlign:'center',
    },
});

module.exports = DropDownList;