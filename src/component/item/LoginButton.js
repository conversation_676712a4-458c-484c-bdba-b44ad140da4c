import React, { Component } from 'react';
import {
	StyleSheet,
	TouchableOpacity,
	Text,
	View
} from 'react-native';
import ScalableText from 'react-native-text';
export default class But<PERSON> extends Component {
	render() {
		let { onPress, text } = this.props;

		if (!text) {
			text = "";
		}

		return (
			<View style={{alignItems:'center', justifyContent:'center', marginVertical:20, width:'80%'}}>
				<ScalableText style={styles.welcome}>
					{text}
				</ScalableText>
				<TouchableOpacity style={styles.container} onPress={onPress} />
			</View>
		);
	}
}

const styles = StyleSheet.create({
	container: {
		backgroundColor: '#fff',
		paddingHorizontal: 25,
		paddingVertical: 5,
		marginVertical: 3,
		opacity: 0.45,
		position:'absolute',
		flex:1, left:0, width:'100%', height:'100%'
	},
	welcome: {
		color: '#fff',
		fontWeight:'bold',
		fontSize: 16,
		textAlign: 'center',
		backgroundColor:'transparent',
		padding:10
	},
});

module.exports = Button;