import React, { Component } from 'react';
import { View, StyleSheet, Platform, Text, TouchableOpacity } from 'react-native';
import i18 from 'i18';

class ProgressView extends Component {

	cancel() {
		let { cancel } = this.props;
		cancel();
	}

	confirm(){
		let { reset } = this.props;
		reset();
	}

	/*
		successCount: success items count
		e.g. 1/21, 2/21
	*/
	render() {
		let { progress } = this.props;
		let { step, total, error, isDownload, cancelable, successCount } = progress;
		let desc1, desc2;

		// for old function which may have no successCount
		if(!successCount && successCount!== 0){
			successCount = step;
		}

		if (isDownload){
			desc1 = step < total ? i18.DownloadingFiles : i18.DownloadedFiles;
		}else{
			desc1 = step < total ? i18.UploadingFiles : i18.UploadedFiles;
		}

		desc2 = step < total ? i18.wait : i18.Finish;


		if(step === total && successCount !== total){
			desc2 = i18.retry;
		}

    if (step < total) {
      return (
        <View style={styles.container}>
          <Text style={styles.desc}>{desc1}</Text>
          <Text style={styles.progress}>{`${successCount}/${total}`}</Text>
          <Text style={styles.desc}>{desc2}</Text>
          <View style={styles.line}/>
          {cancelable === true ? null : <TouchableOpacity onPress={this.cancel.bind(this)}>
            <Text style={styles.button}>{i18.Cancel}</Text></TouchableOpacity>}
        </View>
      )
    } else {
      return (
        <View style={styles.container}>
          <Text style={styles.desc}>{desc1}</Text>
          <Text style={styles.progress}>{`${successCount}/${total}`}</Text>
          <Text style={styles.desc}>{desc2}</Text>

          <View style={styles.line}/>
          {
            <TouchableOpacity onPress={this.confirm.bind(this)} accessibilityLabel={"dl_ok"}>
							<Text
								style={styles.button}>{i18.OK}
							</Text>
						</TouchableOpacity>
          }
        </View>
      )
    }
  }
}

const styles = StyleSheet.create({
	container:{
		paddingTop:20,
		backgroundColor:'#eee',
		borderRadius:15,
		justifyContent:'center',
		alignSelf:'center',
	},
	desc:{
		color:'#333',
    fontSize: 16,
		textAlign: 'center',
		fontWeight: 'bold',
		backgroundColor:'transparent',
		paddingHorizontal:60,
		alignSelf:'center',
	},
  progress: {
		color:'#3a3',
		fontSize: 22,
		fontWeight: 'bold',
		marginVertical:8,
		backgroundColor:'transparent',
		paddingHorizontal:60,
		alignSelf:'center',
	},
	line:{
		marginTop:20,
		height:1,
		backgroundColor:'#ccc',
	},
	button:{
		color:'#07f',
		backgroundColor:'transparent',
		fontSize: 14,
		paddingVertical: 10,
		textAlign:'center',

	},
});

module.exports = ProgressView;
