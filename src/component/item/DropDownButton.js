import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
} from 'react-native';

import Text from 'react-native-text';
import { theme, nav } from 'Theme';
const Icon = require('./Icon.js');


const DropDownButton = (props) => {
  const { title, onOpenDropDownList, showFilter } = props;

  return (
        <View>
            <TouchableOpacity onPress={onOpenDropDownList}>
                <View style={[styles.dropdownContainer, {backgroundColor: showFilter ? '#EB7D3C' :'transparent' , borderColor: showFilter ? '#EB7D3C' : '#9D9C9C', }]}>	
                    <View style={{ minWidth: 60, alignItems: 'center'}}>
                        <Text style={styles.dropdownTitle}>{title}</Text>
                    </View>
                    <View  style={{ width: 30, alignItems: 'center'}}>
                        <Icon style={{ fontSize: 8, color: theme }} icon={"DROPDOWN"}/>
                    </View>		
                </View>
            </TouchableOpacity>
        </View>    
  );
};


const styles = StyleSheet.create({
  dropdownContainer: {
    minWidth: 99, 
    height: 28, 
    borderWidth: 1, 
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    marginLeft: 9,
},
dropdownTitle: {
    minWidth: 60,
    fontSize: 12,
    color: '#fff',
    textAlign: 'left',
    marginLeft: 20,
	}
});


export default DropDownButton;