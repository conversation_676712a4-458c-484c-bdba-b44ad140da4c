/**
 * @providesModule CustTextInput
 */
import React, { Component } from 'react';
import {
	TextInput,View,Text,Animated
} from 'react-native';

class CustTextInput extends Component {

	constructor(props){
		super(props);
	}

	get value(){
		return this.refs.input._lastNativeText;
	}

	clear(){
		this.refs.input._lastNativeText = "";
		return this.refs.input.clear();
	}

	isFocused(){
		return this.refs.input.isFocused();
	}

	render(){
		let {props} = this;	
		let style = props.style || {};
		if (Number.isInteger(style)) 
			style = [styles, style];
		if (style instanceof Array)
			style = [styles, ...style];
		else
			style = {...styles, ...style};

		return <TextInput underlineColorAndroid='transparent' autoCapitalize={'none'} autoCorrect={false} {...props} style={style} ref={'input'}/>;
	}
}

const styles = {
	// borderWidth:1,
	// borderColor:'#777',
	fontSize:14,
	height:22,
	// borderRadius:5,
	paddingHorizontal:5
}

module.exports = CustTextInput;