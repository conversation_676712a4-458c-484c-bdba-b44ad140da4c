import React, { Component } from 'react';
import { Text } from "react-native";

class Icon extends Component {
	render() {
		let iconText = "";

		switch (this.props.icon) {

			case "BACK": iconText = "\ue901"; break;
			case "CLOSE": iconText = "\ue902"; break;
			case "ADD": iconText = "\ue900"; break;
			case "DOWNLOAD": iconText = "\ue906"; break;
			// case "LOADING": iconText = "\uf44a"; break;
			
			case "TaskList": iconText = "\ue90f"; break;
			case "Unit": iconText = "\ue914"; break;
			case "Items": iconText = "\ue90d"; break;
			case "Upload": iconText = "\ue915"; break;
			case "Others": iconText = "\ue911"; break;

			case "ACCOUNT": iconText = "\ue90c"; break;
			case "PASSWORD": iconText = "\ue919"; break;
			case "DROPDOWN": iconText = "\ue921"; break;
			case "report": iconText = "\uf376"; break;
			case "home": iconText = "\ue926"; break;
			case "fillhome": iconText = "\ue926"; break;
			case "list-alt": iconText = "\ue90b"; break;
			case "checked": iconText = "\ue922"; break;
			case "unchecked": iconText = "\ue923"; break;
			case "refresh": iconText = "\uf3a8"; break;
			case "TakePhoto": iconText = "\ue926"; break;

			case "vip": iconText = "\ue927"; break;
			case "no-report": iconText = "\ue917"; break;

			case "COMMENTS": iconText = "\ue904"; break;
			case "FLOORPLAN": iconText = "\ue908"; break;
			case "PICTURE": iconText = "\ue91b"; break;

			case "UNITCHECKLIST": iconText = "\ue90f"; break;
			
			default: break;
		}

		if (this.props.style instanceof Array)
			return <Text style={[styles.icon, ...this.props.style]}>{iconText}</Text>;
		else
			return <Text style={[styles.icon, this.props.style]}>{iconText}</Text>;
	}
};

const styles = {
	icon: {
		fontFamily: 'testAppFont',
	}
};

module.exports = Icon;