import React, { Component } from 'react';
import { View, Text } from 'react-native';
import RNAnimated from 'Animated'
import AnimatedImplementation from 'AnimatedImplementation'



const TextInput = require('CustTextInput');
// const App = require('../../controllers/app.js');

const Animated = {
	...RNAnimated,
	TextInput: AnimatedImplementation.createAnimatedComponent(TextInput),
}


class FloatTextInput extends Component {

	constructor(props) {
		super(props);
		this.anim = new Animated.Value(0);
		this.shakeAnim = new Animated.Value(5);
		this.onFocus = this.onFocus.bind(this);
		this.onBlur = this.onBlur.bind(this);
		this.clear = this.clear.bind(this);
		this.shake = this.shake.bind(this);
	}

	get value() {
		return this.refs.input.value;
	}

	clear() {
		let { input } = this.refs;
		if (!input.isFocused())
			Animated.timing(this.anim, { toValue: 0, duration: 300 }).start();

		return input.clear();
	}

	shake(cb) {
		Animated.sequence([
			Animated.timing(this.shakeAnim, { toValue: 0, duration: 60 }),
			Animated.timing(this.shakeAnim, { toValue: 10, duration: 120 }),
			Animated.timing(this.shakeAnim, { toValue: 0, duration: 120 }),
			Animated.timing(this.shakeAnim, { toValue: 5, duration: 60 }),
		]).start(cb ? cb : () => { });
	}

	onFocus() {
		let { onFocus } = this.props;
		// onFocus();
		Animated.timing(this.anim, { toValue: 1, duration: 300 }).start();
	}

	onBlur() {
		let { onBlur } = this.props;
		let { input } = this.refs;
		// onBlur();
		if (!input.value || input.value === '')
			Animated.timing(this.anim, { toValue: 0, duration: 300 }).start();
	}

	render() {
		let { style, placeholder, secureTextEntry } = this.props;

		return (
			<View style={styles.container}>
				<Animated.View style={{
					...styles.placeholderContainer,
					//opacity: this.anim.interpolate({ inputRange: [0, 1], outputRange: [0.4, 1] }),
					transform: [
						{ translateY: this.anim.interpolate({ inputRange: [0, 1], outputRange: [0, -16] }) },
						{ translateX: this.anim.interpolate({ inputRange: [0, 1], outputRange: [0, -10] }) },
						{ scale: this.anim.interpolate({ inputRange: [0, 1], outputRange: [1, 0.8] }) },
					]
				}}>
					<Text style={styles.text}>{placeholder}</Text>
				</Animated.View>
				<Animated.View style={{ paddingLeft: this.shakeAnim }}>
					<TextInput underlineColorAndroid='transparent' autoCapitalize={'none'} autoCorrect={false} {...this.props} onFocus={this.onFocus} onBlur={this.onBlur} placeholder="" style={styles.input} ref={'input'} secureTextEntry={secureTextEntry} underlineColorAndroid={'transparent'} />
				</Animated.View>
				<View style={{ flexDirection: 'row' }}>
					<Animated.View style={{ backgroundColor: '#888', height: 1, width: this.anim.interpolate({ inputRange: [0, 1], outputRange: [200, 0] }) }} />
					<Animated.View style={{ backgroundColor: '#fff', height: 1, width: this.anim.interpolate({ inputRange: [0, 1], outputRange: [0, 200] }) }} />
				</View>
			</View>
		);
	}
}

const styles = {
	container: {
		marginTop: 15,
		marginBottom: 5
	},
	placeholderContainer: {
		position: 'absolute',
		top: 0, left: 0, bottom: 2,
		justifyContent: 'center',
		paddingHorizontal: 7,
	},
	text: {
		// fontSize: 12,
		color: '#fff',
	},
	input: {
		// fontSize: 12,
		color: '#fff',
		borderWidth: 0,
		paddingHorizontal: 2,
		paddingVertical: 0,
		marginVertical: 0,
	},
}


module.exports = FloatTextInput;