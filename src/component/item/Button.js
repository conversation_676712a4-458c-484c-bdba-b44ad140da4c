import React, { Component } from 'react';
import {
	StyleSheet,
	TouchableOpacity,
  Text,
  View
} from 'react-native';

export default class <PERSON><PERSON> extends Component {
  render() {
		let {onPress, children, style, textStyle, disabled} = this.props;

		let disableStyle = {};
		if (disabled) {
			disableStyle = { opacity: 0.2 };
		}

    return (
			<TouchableOpacity style={[styles.container,style,disableStyle]} disabled={disabled} onPress={onPress}>
				<Text style={[styles.text,textStyle]}>
					{children}
				</Text>
			</TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
	container:{
		backgroundColor:'#333',
		borderRadius:10,
		paddingHorizontal:25,
		paddingVertical:5,
		marginVertical:3,
	},
  text: {
		backgroundColor:'transparent',
		color:'#fff',
    fontSize: 14,
    textAlign: 'center',
  },
});

module.exports = Button;