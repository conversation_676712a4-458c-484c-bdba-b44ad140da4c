'use strict';

import React, { Component } from 'react';
import {
	StyleSheet,
	View,
	TouchableOpacity,
	Animated,
} from 'react-native';

import Text from 'react-native-text';

import Icon from './Icon';
import { tab as Theme } from 'Theme';
import i18 from 'i18';
import { connect } from 'react-redux'

class TabBar extends Component {

	componentDidMount() {
		
	}

	renderTab = (name, page) => {
		let { activeTab, tabs, goToPage } = this.props;
		var isTabActive = activeTab === page;

		let batchStyle = {
			position: 'absolute', top: 0, left: 20, right: 0, bottom: 0,
			width: 18, height: 18,
			backgroundColor: '#FF8034', borderColor: '#FF8034', borderRadius: 9,
			justifyContent: 'center', alignItems: 'center'
		};

		return (
			<TouchableOpacity key={name} onPress={() => goToPage(page)} style={styles.tab}>
				<View style={{ flex: 1, height: 20, alignItems: 'center', justifyContent: 'center' }}>
					<Icon style={[styles.icon, { color: isTabActive ? Theme.active : Theme.text, fontSize: page == 4 ? 9 : 18, marginBottom: page == 4 ? 6 : 4 }]} icon={tabs[page]} />
					{
						(page == 0 && this.props.checklistsCount > 0) ? (
							<View style={batchStyle}>
								<Text style={{ fontSize: 8, color: 'white' }}>{(this.props.checklistsCount > 99) ? "99+" : "" + this.props.checklistsCount }</Text>
							</View>
						) : null
					}
					{
						(page == 1 && this.props.unitsCount > 0) ? (
							<View style={batchStyle}>
								<Text style={{ fontSize: 8, color: 'white' }}>{(this.props.unitsCount > 99) ? "99+" : "" + this.props.unitsCount }</Text>
							</View>
						) : null
					}
					{
						(page == 2 && this.props.itemsCount > 0) ? (
							<View style={batchStyle}>
								<Text style={{ fontSize: 8, color: 'white' }}>{(this.props.itemsCount > 99) ? "99+" : "" + this.props.itemsCount }</Text>
							</View>
						) : null
					}
					{
						(page == 3 && this.props.pendingCount > 0) ? (
							<View style={batchStyle}>
								<Text style={{ fontSize: 8, color: 'white' }}>{(this.props.pendingCount > 99) ? "99+" : "" + this.props.pendingCount }</Text>
							</View>
						) : null
					}
				</View>
				<Text style={[styles.text, { color: isTabActive ? Theme.active : Theme.text }]}>
					{i18[tabs[page]]}
				</Text>
			</TouchableOpacity>
		);
	}

	render() {
		let { containerWidth, tabs, scrollValue } = this.props;
		let left = scrollValue.interpolate({ inputRange: [0, 1], outputRange: [0, containerWidth / tabs.length] });

		return (
			<View style={styles.container}>
				<View style={styles.tabs}>
					{tabs.map(this.renderTab)}
				</View>
				<Animated.View style={[styles.line, { width: containerWidth / tabs.length, left }]} />
			</View>
		);
	}
}

const styles = StyleSheet.create({
	container: {
		height: 50,
	},
	tab: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
		paddingBottom: 5,
	},
	tabs: {
		flexDirection: 'row',
		paddingTop: 3,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: Theme.bg,
		height: 50,
	},
	text: {
		fontWeight: '100',
		fontSize: 12
	},
	line: {
		position: 'absolute',
		height: 3,
		backgroundColor: Theme.active,
		bottom: 0,
	},
	icon: {
	}
});

import {
	CheckLists as checkListsMapDispatchToProps,
	Items as itemsMapDispatchToProps,
	Project as projectMapDispatchToProps,
	Upload as uploadMapDispatchToProps,
	Units as unitsMapDispatchToProps,
} from 'Controller';

let checkListsProps;
let itemsProps;
let projectProps;
let uploadProps;
let unitsProps;

module.exports = connect((state => {
	return {
		pendingCount: state.project.pendingCount,
		itemsCount: state.project.itemsCount,
		checklistsCount: state.project.checklistsCount,
		unitsCount: state.project.unitsCount
	}
}), (dispatch, ownProps) => {
	checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
	itemsProps = itemsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	uploadProps = uploadMapDispatchToProps(dispatch, ownProps)
	unitsProps = unitsMapDispatchToProps(dispatch, ownProps)
	return { ...checkListsProps, ...projectProps, ...uploadProps, ...itemsProps, ...unitsProps };
})(TabBar);