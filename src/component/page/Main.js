import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	Alert,
	AppState,
	NetInfo,
} from 'react-native';
import { connect } from 'react-redux';
import ScrollableTabView from 'react-native-scrollable-tab-view';

import Server from 'Server';
import i18 from 'i18';
import { Button, TabBar, Nav } from 'Item';
import { Action } from 'Redux';

import TaskList from './TaskList.js';
import Others from './Others.js';
import Units from './Units.js';
import Items from './Items.js';
import Upload from './Upload.js';
import TaskDownload from './TaskDownload.js';
import ItemsDownload from './ItemsDownload.js';
import ReadQRcode from './ReadQRcode.js';
import UnitDownload from './UnitDownload.js';
import UnitDetail from './UnitDetail.js';
import AddHocForm from './AddHocForm.js';
import AdhocList from './AdhocList.js';
import ItemDetail from './ItemDetail.js';
import FormSubmission from './FormSubmission.js';


export default class Main extends Component {

	constructor(props) {
		super(props);

		this.state = {
			loading: true,
		};
	}

	componentDidMount() {
		let { props } = this;
		let { user } = props;
		// handling the issue on too much dispatch when directly loaded into Main (i.e. the case of auto-login)
		setTimeout(() => {
			this.setState({
				loading: false,
			});	
		}, 0);

		NetInfo.fetch().done(
			(connectionInfo) => { 
				connectionInfo = connectionInfo.toLowerCase();
				// console.log("fetch con", connectionInfo)
				props.networkInfoUpdate(connectionInfo);
			}

		);


		NetInfo.addEventListener(
			'connectionChange',
			this.handleFirstConnectivityChange.bind(this)
		);
	}

	// componentWillMount() {
	// 	let { props } = this;
	// 	setTimeout(() => {
	// 		props.setStopShowingWelcome(false);
	// 	}, 0);
	// }

	handleFirstConnectivityChange(connectionInfo) {
		connectionInfo = connectionInfo.toLowerCase();
		// console.log(connectionInfo)
		let { props } = this;
		props.networkInfoUpdate(connectionInfo);
		
	}



	componentWillUnmount() {

		NetInfo.removeEventListener(
			'connectionChange',
			this.handleFirstConnectivityChange.bind(this)
		);
	}


	render() {
		if (this.state.loading) {
			return <View />;
		}

		let { props } = this;
		let { navigation } = props;

		return (
			<View style={styles.container}>
				<ScrollableTabView style={styles.body} tabBarPosition={'bottom'} initialPage={0} renderTabBar={()=><TabBar />} locked={true} prerenderingSiblingsNumber={0}>
					<TaskList tabLabel={'TaskList'} navigation={navigation} />
					<Units tabLabel={'Unit'} navigation={navigation} />
					<Items tabLabel={'Items'} navigation={navigation} />
					<Upload tabLabel={'Upload'} navigation={navigation} />
					<Others tabLabel={'Others'} navigation={navigation} />
				</ScrollableTabView>
			</View>
		);
	}
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
	body:{
		flex:1,
		backgroundColor: '#F2F2F2',		
	},
	icon: {
		width: 80,
		height: 80,
	},
});

const mapStateToProps = (state, ownProps) => {
	return {
		language: state.setting.language,
		user: state.auth.user,
		cameraOpenned: state.setting.cameraOpenned,
	}
}


import { 
	Project as projectMapDispatchToProps, 
	Others as othersMapDispatchToProps
} from 'Controller';

let projectProps;
let othersProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	othersProps = othersMapDispatchToProps(dispatch, ownProps)

	return { ...projectProps, ...othersProps};
})(Main);