import React, { Component } from 'react';
import {
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  Alert,
  Dimensions,
  ScrollView,
} from 'react-native';
const TextInput = require('CustTextInput');
import { connect } from 'react-redux'
import LocalDB from 'LocalDB';

const Button = require('Item').Button;
const i18 = require('i18');
import { taskList as Theme } from 'Theme';
import { Icon, Nav } from 'Item';
import ActionSheet from 'react-native-actionsheet';

import CachedImage from 'react-native-cached-image';
const {
    ImageCacheProvider
} = CachedImage;
import Text from 'react-native-text';

import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import RefreshLoginContractorView from "./RefreshLoginContractorView";

const CANCEL_INDEX = 0;
const keyTitle = i18.ChangeReason;

import VersionNumber from 'react-native-version-number';
import ChangePasswordView from './ChangePasswordView';

export default class Others extends Component {

  constructor(props) {
    super(props);
    this.state = {
      isModalVisible: false,
      type: '',
      unit: '',
      keyState: "",
      unitState: "",
      borrower: '',
      areaCode: '',
      phone: '',
      reason: '',
      otherReason: '',
      showKeyDropDown: false,
      keyReasons: [],
      showProjectList: false,
      showNontestLogin: false
    };
  }

  componentDidMount() {
    let { props } = this;
    let { projectDetail, user } = props;
    let keyReasons = [];
    keyReasons.push(i18.Cancel);

    projectDetail.key_stage.forEach((item) => {
      keyReasons.push(item.name);
    })

    this.setState({
      keyReasons: keyReasons
    });
  }

  componentWillReceiveProps(nextProps) {
    let { props } = this;
    let { units, language } = props;


    if (language == nextProps.language && nextProps.showKeyForm) {
      this._showModal(nextProps.qrcodeData);
    }
  }

  _showModal(qrcodeData) {
    let { props } = this;
    let { unitsInfo, user } = props;
    let type;
    let unit;

    unitsInfo.find((item) => {
      if (item.tower == qrcodeData.tower && item.floor == qrcodeData.floor && item.flat == qrcodeData.flat) {
        type = item.type;
        unit = item._id;
        return true
      }

      return false
    })

    this.setState({
      isModalVisible: true,
      showKeyDropDown: true,
      keyState: this.state.keyState ? this.state.keyState : (qrcodeData.key_status || i18.lend),
      unitState: this.state.unitState ? this.state.unitState : (((qrcodeData.unit_status != i18.ownerKeepKey && qrcodeData.unit_status != i18.ownerNotKeepKey && qrcodeData.unit_status != i18.ownerGotIn)
        || qrcodeData.unit_status == "") ? i18.ownerKeepKey : qrcodeData.unit_status),
      borrower: qrcodeData.current_owner || user.username,
      areaCode: qrcodeData.dialing_code || "+852",
      phone: qrcodeData.mobile,
      reason: this.state.reason ? this.state.reason : (qrcodeData.reason ? qrcodeData.reason : this.state.keyReasons[1]),
      otherReason: this.state.otherReason ? this.state.otherReason : qrcodeData.remark,
      type: type,
      unit: unit,
      tower: qrcodeData.tower,
      floor: qrcodeData.floor,
      flat: qrcodeData.flat,
      showChangePassword: false,
    })
  }

  logout (c) {
    if (c == 'none') {
        Alert.alert(
            i18.noInternetConnection,
            '',
            [
                {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
            ]
        )
        return
    }
    Alert.alert(
      'Confirm logout',
      'All unsubmitted data will be deleted and cannot be restored. Logout now?',
      [
        {text: 'Cancel', onPress: () => { }, style: 'cancel'},
        {
          text: 'Logout', onPress: () => {
            this.logoutAction()
          }
        },
      ]
    )
  }

  logoutAction () {
    let {props, context} = this
    props.logout().then(async () => {
      if (context.store.persistor) {
        context.store.persistor.purge()
      }
      await LocalDB.clearAll()
      await ImageCacheProvider.clearCache()
    })
  }

  changeProject(item) {
    let { props, context } = this;
    let { user, projectLists, navigation } = props;

    Alert.alert(
      'Confirm Change Project',
      'All unsubmitted data will be deleted and cannot be restored. Change now?',
      [
        { text: 'Cancel', onPress: () => { }, style: 'cancel' },
        {
          text: 'Change', onPress: async () => {
            this.props.displayLoading()
            await props.changeProject(item, user, projectLists, context, navigation)
            this.props.removeLoading()
          }
        },
      ]
    )
  }

  _hideModal() {
    this.setState({
      isModalVisible: false,
      showKeyDropDown: false
    })
  }

  showActionSheet() {
    this.ActionSheet.show()
  }

  handlePress(i) {
    let { props } = this;
    if (i != 0) {
      this.setState({ reason: this.state.keyReasons[i] });
    }
  }

  renderDropDownList() {
    let { props } = this;
    let { projectLists } = props;
      let currentLanguage = i18.getLanguage();
    let nameLang = 'name_en';
    if (currentLanguage == 'zh_hk') {
      nameLang = 'name_zh';
    } else if (currentLanguage == 'cn') {
      nameLang = 'name_cn';
    }
    if (projectLists.length > 0) {
      return projectLists.map((item, i) => {
        return (
          <TouchableOpacity key={i} onPress={() => {
            this.setState({
              showProjectList: false,
            })
            this.changeProject(item);
          }}>
            <View style={{ height: 60, borderBottomWidth: 1, alignItems: 'center', justifyContent: 'center' }}>
              <Text>{item[nameLang]}</Text>
            </View>
          </TouchableOpacity>
        );
      });
    } else {
      return null;
    }
  }

  refreshToken() {
      let { user } = this.props;
      if(user.email.includes('@test.com') || user.email.includes('@pm.test.com')) {
          this.props.navigation.navigate('RefreshLoginSession');
      } else {
          this.setState({
              showNontestLogin: true
          })
      }
  }

  render() {

    if (this.props.user) {

      let { props } = this;
      let { language, qrcodeData, unit_address, projectDetail, user, projectLists, selectedProject, connectionInfo } = props;

      let translatedUnitAddress;

      if (this.state.type) {
        translatedUnitAddress = unit_address[this.state.type] || "";
      }

      if (this.state.tower) {
        translatedUnitAddress = translatedUnitAddress.replace("{tower}", this.state.tower);
      }

      if (this.state.flat) {
        translatedUnitAddress = translatedUnitAddress.replace("{flat}", this.state.flat);
      }

      if (this.state.floor) {
        translatedUnitAddress = translatedUnitAddress.replace("{floor}", this.state.floor);
      }

      var showDropDownList = this.state.showProjectList ? (
        <View style={{
          position: 'absolute',
          width: Dimensions.get('window').width,
          height: Dimensions.get('window').height,
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <View style={styles.dropDownListContainer}>
            <View style={{ height: 50, alignItems: 'center', justifyContent: 'center', borderBottomWidth: 1, backgroundColor: "#131913" }}>
              <Text style={{ color: '#fff' }}>{i18.Items}</Text>
            </View>
            <ScrollView>
              {this.renderDropDownList()}
            </ScrollView>
            <TouchableOpacity style={{ height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'lightgray' }} onPress={() => {
              this.setState({
                showProjectList: false
              });
            }}>
              <Text>{i18.Close}</Text>
            </TouchableOpacity>

          </View>
        </View>
      ) : null;

      let showModal = this.state.isModalVisible ? (
        <View style={{ alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.7)', position: 'absolute', width: '100%', height: '100%', justifyContent: 'center' }}>
          <View style={{ backgroundColor: 'rgba(252, 252, 252, 0.9)', width: '80%', minHeight: 467, borderRadius: 8 }}>
            <KeyboardAwareScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingVertical: 10 }}>
              <Text style={{ fontWeight: '600', fontSize: 17, textAlign: 'center' }}>{i18.updateRecord}</Text>
              <Text style={{ fontWeight: '600', fontSize: 15, textAlign: 'center' }}>{translatedUnitAddress}</Text>

              <View style={{ paddingTop: 15, paddingBottom: 6, flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{ marginLeft: '5%', paddingVertical: 4 }}>{i18.keyState}</Text>
                <View
                  style={{
                    marginLeft: '5%',
                    width: '65%',
                    borderRadius: 3,
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: '#FF8034',
                    flexDirection: 'row',
                  }}
                >
                  <TouchableOpacity
                    onPress={() => this.setState({ keyState: i18.lend })}
                    style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.keyState == i18.lend ? '#FF8034' : 'transparent' }}>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5 }}>
                      <Text style={{ color: this.state.keyState == i18.lend ? '#FFF' : '#A2A2A2', fontSize: 13 }}>{i18.lend}</Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => this.setState({ keyState: i18.spoiled })}
                    style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.keyState == i18.spoiled ? '#FF8034' : 'transparent' }}>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5 }}>
                      <Text style={{ color: this.state.keyState == i18.spoiled ? '#FFF' : '#A2A2A2', fontSize: 13 }}>{i18.spoiled}</Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={{ paddingTop: 15, paddingBottom: 6, flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{ marginLeft: '5%', paddingVertical: 4 }}>{i18.unitState}</Text>
                <View
                  style={{
                    marginLeft: '5%',
                    width: '65%',
                    borderRadius: 3,
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: '#FF8034',
                    flexDirection: 'row',
                  }}
                >
                  <TouchableOpacity
                    onPress={() => this.setState({ unitState: i18.ownerKeepKey })}
                    style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.unitState == i18.ownerKeepKey ? '#FF8034' : 'transparent' }}>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5 }}>
                      <Text style={{ color: this.state.unitState == i18.ownerKeepKey ? '#FFF' : '#A2A2A2', fontSize: 13 }}>{i18.ownerKeepKey}</Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => this.setState({ unitState: i18.ownerNotKeepKey })}
                    style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.unitState == i18.ownerNotKeepKey ? '#FF8034' : 'transparent' }}>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5 }}>
                      <Text style={{ color: this.state.unitState == i18.ownerNotKeepKey ? '#FFF' : '#A2A2A2', fontSize: 13 }}>{i18.ownerNotKeepKey}</Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => this.setState({ unitState: i18.ownerGotIn })}
                    style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.unitState == i18.ownerGotIn ? '#FF8034' : 'transparent' }}>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5 }}>
                      <Text style={{ color: this.state.unitState == i18.ownerGotIn ? '#FFF' : '#A2A2A2', fontSize: 13 }}>{i18.ownerGotIn}</Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={{ paddingVertical: 6 }}>
                <Text style={{ marginLeft: '5%', paddingVertical: 4 }}>{i18.borrower}</Text>
                <TextInput
                  underlineColorAndroid='transparent'
                  style={{
                    marginLeft: '5%',
                    width: '90%',
                    height: 25,
                    borderRadius: 3,
                    backgroundColor: "#FFFFFF",
                    fontSize: 16,
                    padding: 5,
                    borderWidth: 1,
                    borderColor: '#D9D9D9'
                  }}
                  onChangeText={(text) => this.setState({ borrower: text })}
                  value={this.state.borrower}
                />
              </View>

              <View style={{ paddingVertical: 6 }}>
                <Text style={{ marginLeft: '5%', paddingVertical: 4 }}>{i18.phone}</Text>
                <View style={{ flexDirection: 'row', marginLeft: '5%', width: '90%' }}>
                  <TextInput
                    underlineColorAndroid='transparent'
                    style={{
                      width: '37%',
                      height: 25,
                      borderRadius: 3,
                      backgroundColor: "#FFFFFF",
                      fontSize: 16,
                      padding: 5,
                      borderWidth: 1,
                      borderColor: '#D9D9D9'
                    }}
                    onChangeText={(text) => this.setState({ areaCode: text })}
                    value={this.state.areaCode}
                  />
                  <TextInput
                    underlineColorAndroid='transparent'
                    style={{
                      marginLeft: '3%',
                      width: '60%',
                      height: 25,
                      borderRadius: 3,
                      backgroundColor: "#FFFFFF",
                      fontSize: 16,
                      padding: 5,
                      borderWidth: 1,
                      borderColor: '#D9D9D9'
                    }}
                    onChangeText={(text) => this.setState({ phone: text })}
                    value={this.state.phone}
                  />
                </View>
              </View>


              <View style={{ paddingVertical: 6 }}>
                <Text style={{ marginLeft: '5%', paddingVertical: 4 }}>{i18.reason}</Text>
                <View style={{ flexDirection: 'row', marginLeft: '5%', width: '90%' }}>
                  <TextInput
                    editable={false}
                    underlineColorAndroid='transparent'
                    style={{
                      width: '90%',
                      height: 25,
                      borderRadius: 3,
                      backgroundColor: "#FFFFFF",
                      fontSize: 16,
                      padding: 5,
                      borderWidth: 1,
                      borderColor: '#D9D9D9'
                    }}
                    onChangeText={(text) => this.setState({ reason: text })}
                    value={this.state.reason}
                  />

                  <TouchableOpacity
                    style={{
                      height: 25, width: '10%', backgroundColor: '#FFFFFF', justifyContent: 'center', alignItems: 'center',
                      borderWidth: 1, borderColor: '#D9D9D9', borderRadius: 3,
                    }} onPress={() => {
                      this.ActionSheet.show();
                    }}>
                    <View style={[styles.center]}>
                      <Icon style={{ fontSize: 8, color: '#000', textAlign: 'center' }} icon={"DROPDOWN"} />
                    </View>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={{ paddingVertical: 6 }}>
                <Text style={{ marginLeft: '5%', paddingVertical: 4 }}>{i18.otherReason}</Text>
                <TextInput
                  underlineColorAndroid='transparent'
                  style={{
                    marginLeft: '5%',
                    width: '90%',
                    height: 25,
                    borderRadius: 3,
                    backgroundColor: "#FFFFFF",
                    fontSize: 16,
                    padding: 5,
                    borderWidth: 1,
                    borderColor: '#D9D9D9'
                  }}
                  onChangeText={(text) => this.setState({ otherReason: text })}
                  value={this.state.otherReason}
                />
              </View>
            </KeyboardAwareScrollView>
            <View style={{
              backgroundColor: 'rgba(252, 252, 252, 0.9)', borderTopColor: '#D2CACA', borderTopWidth: 1, borderRadius: 8,
              justifyContent: 'center', alignItems: 'center', flexDirection: 'row', position: 'absolute', width: '100%', bottom: 0
            }}>
              <TouchableOpacity onPress={() => this._hideModal()} style={{ flex: 1 }}>
                <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                  <Text style={{ color: '#2583F9', fontSize: 15, fontWeight: '500' }}>{i18.Cancel}</Text>
                </View>
              </TouchableOpacity>
              <View style={{ height: 44, backgroundColor: '#D2CACA', width: 1 }} />
              <TouchableOpacity onPress={() => {
                let data = {
                  type: this.state.type,
                  unit: this.state.unit,
                  key_status: this.state.keyState,
                  unit_status: this.state.unitState,
                  current_owner: this.state.borrower,
                  dialing_code: this.state.areaCode,
                  mobile: this.state.phone,
                  reason: this.state.reason,
                  remark: this.state.otherReason,
                };


                if (data.current_owner && data.mobile && data.dialing_code) {
                  props.updateKey(data);
                  setTimeout(() => {
                    this._hideModal();
                  }, 0)
                } else {
                  Alert.alert(
                    i18.KeyAskForInput,
                    '',
                    [
                      { text: i18.Cancel, onPress: () => console.log('Cancel Pressed') },
                    ]
                  )
                }

              }} style={{ flex: 1 }}>
                <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><Text style={{ color: '#2583F9', fontSize: 15, fontWeight: '500' }}>{i18.update}</Text></View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      ) : null;


      let name = '';
      let code = '';

      let currentLanguage = i18.getLanguage();
      let nameLang = 'name_en';
      if (currentLanguage == 'zh_hk') {
        nameLang = 'name_zh';
      } else if (currentLanguage == 'cn') {
        nameLang = 'name_cn';
      }

      if (selectedProject) {
        name = selectedProject[nameLang];
        code = selectedProject._id;
      } else if (projectLists != null && projectLists.length > 0) {
        name = projectLists[0][nameLang];
        code = projectLists[0]._id;
      }

      return (
        <View style={styles.container}>
          <Nav>
            {i18.Others}
          </Nav>
          <View style={{ width: '100%', marginTop: 5, paddingHorizontal: 15, paddingVertical:5, backgroundColor: '#FFF', flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
            <View style={{ flex: 1,  justifyContent: 'center', alignItems: 'center'}}>
              <Text style={{ fontWeight: 'bold', lineHeight: 20, fontSize: 14 }}>{this.props.user.username}</Text>
              {
                (this.props.loginDateStr) ? (
                  <Text style={{ color: '#9D9D9D', lineHeight: 20, fontSize: 12 }}>{i18.lastLogin + ": " + this.props.loginDateStr}</Text>
                ) : null
              }
            </View>
          </View>
          <View style={{ width: '100%', paddingHorizontal: 10, paddingVertical: 0, backgroundColor: '#FFF', flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
            <View style={{ flex: 0.7, justifyContent: 'center', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={() => this.refreshToken()}
                style={styles.button}>

                <Text style={{ color: '#CC7777', fontWeight: 'bold' }}>{i18.refreshSession}</Text>

              </TouchableOpacity>
            </View>
              <View style={{ flex: 0.7, justifyContent: 'center', alignItems: 'center' }}>
                  <TouchableOpacity
                      onPress={() => this.logout(connectionInfo)}
                      style={styles.button}>

                      <Text style={{ color: '#CC7777', fontWeight: 'bold' }}>{i18.logout}</Text>

                  </TouchableOpacity>
              </View>

          </View>
          <View style={{ width: '95%', paddingVertical: 10 }}>
            <Text style={{ marginLeft: '2%' }}>{i18.currentProject}</Text>
            <TouchableOpacity
              onPress={() => {
                props.fetchProjectLists();
                this.setState({ showProjectList: true });
              }}
              style={{
                marginTop: 5, height: 40, width: '100%', borderColor: '#AEAEAE',
                borderWidth: 1, flexDirection: 'row', backgroundColor: '#FFF', alignItems: 'center'
              }}
              >
              <View style={{ flex: 8.5 }}>
                <Text style={{ marginLeft: '5%' }}>{name}</Text>
              </View>
              <View style={{ flex: 1.5 }}>
                <Icon style={{ fontSize: 8, color: '#AEAEAE', textAlign: 'center' }} icon={"DROPDOWN"} />
              </View>
            </TouchableOpacity>
          </View>

          <View style={{ width: '95%', height: 1, backgroundColor: '#AEAEAE', marginVertical: 15 }} />
          <TouchableOpacity
            onPress={() => { this.props.navigation.navigate("FormSubmission"); }}
            style={styles.pageButton}>
            <Icon icon={'home'} style={[styles.icon, { fontSize: 20 }]} />
            <Text style={{ fontWeight: 'bold' }}>{i18.formSubmission}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => { this.props.navigation.navigate("ReadQRcode", { fromScreen: 'others' }) }}
            style={styles.pageButton}>
            <Icon icon={'home'} style={[styles.icon, { fontSize: 20 }]} />
            <Text style={{ fontWeight: 'bold' }}>{i18.keyManagement}</Text>

          </TouchableOpacity>
          {
            (!this.props.user.email.includes('@test.com') && !this.props.user.email.includes('@pm.test.com')) ? (
              <TouchableOpacity
                onPress={() => {
                  if(this.props.user.email.includes('@test.com') || this.props.user.email.includes('@pm.test.com')){
                    Alert.alert(
                      i18.InputUsername,
                      '',
                      [
                        {text: i18.OK, onPress: () => console.log("OK")
                        },
                      ]
                    )
                  } else {
                    this.setState({
                      showChangePassword: true
                    })
                  }
                }}
                style={styles.pageButton}>
                <Icon icon={'home'} style={[styles.icon, { fontSize: 20 }]} />
                <Text style={{ fontWeight: 'bold' }}>{i18.changePassword}</Text>

              </TouchableOpacity>
            ) : null
          }

          <View style={{ width: '95%', height: 1, backgroundColor: '#AEAEAE', marginVertical: 20 }} />

          <View style={{ width: '90%', flexDirection: 'row', marginTop: 5 }}>
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={() => props.changeLanguage('en')}
                style={[styles.langButton, { backgroundColor: language == 'en' ? '#FF8034' : 'transparent', borderTopLeftRadius: 5, borderBottomLeftRadius: 5 }]}>

                <Text style={{ color: language == 'en' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>English</Text>

              </TouchableOpacity>
            </View>
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={() => props.changeLanguage('zh_hk')}
                style={[styles.langButton, { backgroundColor: language == 'zh_hk' ? '#FF8034' : 'transparent', borderLeftWidth: 0 }]}>

                <Text style={{ color: language == 'zh_hk' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>繁體中文</Text>

              </TouchableOpacity>
            </View>
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <TouchableOpacity
                onPress={() => props.changeLanguage('cn')}
                style={[styles.langButton, { backgroundColor: language == 'cn' ? '#FF8034' : 'transparent', borderLeftWidth: 0, borderTopRightRadius: 5, borderBottomRightRadius: 5 }]}>

                <Text style={{ color: language == 'cn' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>简体中文</Text>

              </TouchableOpacity>
            </View>
          </View>

          <Text style={{
            position: 'absolute',
            bottom: 10,
            left: 10,
            flex: 0,
          }}>{"bundle updated: 20180521"}</Text>

          <Text style={{
            position: 'absolute',
            bottom: 10,
            right: 10,
            flex: 0,
          }}>{VersionNumber.appVersion + " (" + VersionNumber.buildVersion + ")"}</Text>

          {
            (this.state.keyReasons && this.state.keyReasons.length > 0) ? (
              <ActionSheet
                ref={o => this.ActionSheet = o}
                title={keyTitle}
                options={this.state.keyReasons}
                cancelButtonIndex={CANCEL_INDEX}
                onPress={(i) => this.handlePress(i)}
              />
            ) : null
          }

          {showDropDownList}
          {showModal}
          {
            (this.state.showChangePassword) ? (<ChangePasswordView onDismiss={() => {
              this.setState({
                showChangePassword: false
              });
            }} />) : null
          }
          {
              (this.state.showNontestLogin) ? (<RefreshLoginContractorView onDismiss={() => {
                this.setState({
                    showNontestLogin: false
                    });
                }} />) : null
          }
        </View>
      );
    } else {
      return <View />;
    }
  }
};

Others.contextTypes = { store: React.PropTypes.object };

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.bg,
    alignItems: 'center'
  },
  icon: {
    width: 80,
    height: 80,
  },
  langButton: {
    width: '100%',
    height: 35,
    borderColor: '#FF8034',
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width: '90%',
    height: 40,
    borderColor: '#CC7777',
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
  },
  pageButton: {
    width: '95%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.item.bg,
    padding: 9,
    marginHorizontal: 5,
    marginTop: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
  },
  icon: {
    color: '#484848',
    width: 30,
    backgroundColor: 'transparent',
    textAlign: 'center',
    marginRight: 10,
  },
  dropDownListContainer: {
    backgroundColor: '#fff',
    width: Dimensions.get('window').width * 0.85,
    height: Dimensions.get('window').height * 0.8,
    marginBottom: Dimensions.get('window').height * 0.05,
    borderWidth: 1,
  },
});

const mapStateToProps = (state, ownProps) => {
  let unit_address;
  if (state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0) {
    if (state.setting.language == 'en') {
      unit_address = state.project.projectDetail.unit_address_en;
    } else if (state.setting.language == 'zh_hk') {
      unit_address = state.project.projectDetail.unit_address_zh;
    } else {
      unit_address = state.project.projectDetail.unit_address_cn;
    }
  }

  return {
    projectDetail: state.project.projectDetail,
    language: state.setting.language,
    connectionInfo: state.setting.connectionInfo,
    loginDateStr: state.auth.lastLoginDateStr,
    user: state.auth.user,
    qrcodeData: state.project.qrcodeData,
    showKeyForm: state.project.showKeyForm,
    unitsInfo: state.project.unitsInfo,
    unit_address: unit_address,
    projectLists: state.project.projectLists,
    selectedProject: state.project.selectedProject,
  }
}

import {
  Project as projectMapDispatchToProps,
  Others as othersMapDispatchToProps,
  Loading as loadingMapDispatchToProps
} from 'Controller';

let projectProps;
let othersProps;
let loadingProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps) => {
  projectProps = projectMapDispatchToProps(dispatch, ownProps)
  othersProps = othersMapDispatchToProps(dispatch, ownProps)
  loadingProps = loadingMapDispatchToProps(dispatch, ownProps)
  return { ...projectProps, ...othersProps, ...loadingProps };
})(Others);
