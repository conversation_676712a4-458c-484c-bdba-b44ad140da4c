import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	ScrollView,
	TouchableOpacity,
	Alert,
	Platform,
} from 'react-native';
const TextInput = require('CustTextInput');
import { connect } from 'react-redux'

import Server from 'Server';
import { Icon, Nav } from 'Item';
import { checkList as Theme } from 'Theme';
import Modal from 'react-native-modal';
import i18 from 'i18';

const Button = require('Item').Button;
const Actions = require('Redux').Action.root;

export default class CheckListItem extends Component {

	constructor(props) {
		super(props);
		this.state = {
			question: this.props.navigation.state.params.question,
			translatedUnitAddress: this.props.navigation.state.params.translatedUnitAddress,
			total: this.props.navigation.state.params.total,
			label: this.props.navigation.state.params.label,
			selected: this.props.navigation.state.params.question.selectOptions,
			otherText: this.props.navigation.state.params.question.otherText,
			images: this.props.navigation.state.params.question.images,
			isModalVisible: false,
			comments: this.props.navigation.state.params.question.comments ? this.props.navigation.state.params.question.comments : "",
			isUnit: this.props.navigation.state.params.isUnit,
			showAlertOnBack: false,
			showAlertNextItem: false
		};
	}

	componentWillMount(){
		this.setState({
			showAlertOnBack: false,
			showAlertNextItem: false,
		})
	}

	componentWillReceiveProps(nextProps) {
		let { props } = this;
		let { checklist } = props;

		if (nextProps.checklist) {
			let checklist = nextProps.checklist;
			let selected;
			let images;
			let question;
			// checklist.questions.forEach((sectionData)=>{
			// 	sectionData.data.forEach((item)=>{
			// 		if(item._id == this.state.question._id) {
			// 			selected = item.selectOptions;
			// 			images = item.images;
			// 		}
			// 	})
			// }); 
			let breakLoop = false;
			for (var i = 0, len = checklist.questions.length; i < len; i++) {
				let sectionData = checklist.questions[i];
				for (var j = 0, len2 = sectionData.data.length; j < len2; j++) {
					let item = sectionData.data[j];
					if (item._id == this.state.question._id) {
						question = item;
						selected = item.selectOptions;
						images = item.images;
						breakLoop = true;
						break;
					}
				}
				if (breakLoop) {
					break;
				}
			}

			this.setState({
				selected: selected,
				images: images,
				question: question
			});
		}
	}

	launchImageEditor() {
		this.props.navigation.navigate('EditPhoto', { questionId: this.state.question._id, isUnit: this.state.isUnit });
	}

	onBack = () => {
		let { props } = this;
		let { checklist} = props;

		if((this.state.images == undefined || this.state.images.length < 1) && this.state.question.status != "Unfill" && !this.state.showAlertOnBack && !this.state.isUnit ){
			if(this.state.question.status == "FollowUp" && checklist.require_photo_for_failed) {
				Alert.alert(
					i18.shouldInputPhotoForFailed,
					'',
					[
						{text: i18.OK, onPress: () => this.setState({showAlertOnBack: true})},
					]
				)
			}else if(this.state.question.status == "FollowUp" && !checklist.require_photo_for_failed) {
				this.props.navigation.goBack();
			}

			if(this.state.question.status == "Satisfy" && checklist.require_photo_for_statisfy) {
				Alert.alert(
					i18.shouldInputPhotoForSatisfy,
					'',
					[
						{text: i18.OK, onPress: () => this.setState({showAlertOnBack: true})},
					]
				)
			}else if(this.state.question.status == "Satisfy" && !checklist.require_photo_for_statisfy) {
				this.props.navigation.goBack();
			}
		}else{
			this.props.navigation.goBack();
		}
	}

	updateSelected(val) {
		let { props } = this;
		let { checklist } = props;
		let selected = this.state.selected;

		if (val != '滿意') {
			let arraycontain = (selected.indexOf(val) > -1);
			let containDeleted = (selected.indexOf('滿意') > -1);
			if (containDeleted) {
				for (var i = selected.length - 1; i >= 0; i--) {
					if (selected[i] === '滿意') {
						selected.splice(i, 1);
					}
				}
			}
			if (!arraycontain) {
				let newArray = [];
				newArray.push(val);
				selected = selected.concat(newArray);
			} else {
				selected = selected;
				for (var i = selected.length - 1; i >= 0; i--) {
					if (selected[i] === val) {
						selected.splice(i, 1);
					}
				}
			}
		} else {
			let containDeleted = (selected.indexOf('滿意') > -1);
			if (containDeleted) {
				selected = [];
			} else {
				selected = ['滿意'];
			}
		}

		let label;
		if (selected.indexOf('滿意') > -1) {
			label = "Satisfy";
		} else if (selected.length < 1) {
			label = "Unfill";
		} else {
			label = "FollowUp";
		}


		let id = [];
		id.push(this.state.question._id);

		props.updateSelectOptions(id, label, checklist._id, selected, 'checklistDetail');

		props.reCalLabelData();
	}

	renderOption = (val, idx) => {
		let seleted = false;
		let containVal = (this.state.selected.indexOf(val) > -1);
		if (containVal && this.state.selected.length > 0) {
			seleted = true;
		}

		let status = this.state.question.status;
		let selectedColor = Theme.item.status.unfill;
		if (val === '滿意') {
			selectedColor = Theme.item.status.satisfy;
		} else {
			selectedColor = Theme.item.status.followUp;
		}

		return (
			<TouchableOpacity key={idx} style={[styles.option, {
				backgroundColor: seleted ? selectedColor : 'rgba(242.25, 242.25, 242.25, 1)',
				borderBottomColor: seleted ? '#FFFFFF' : 'gray',
			}]} onPress={() => {
				if (!this.state.isUnit) {
					this.props.displayLoading();
					setTimeout(() => {
						this.updateSelected(val)
						this.props.removeLoading();
					}, 0);
				}
			}}>
				<Text style={[styles.optionText, { color: seleted ? '#FFFFFF' : '#000000' }]}>{val}</Text>
			</TouchableOpacity>
		);
	}

	viewOtherItem(action, index) {

		let { props } = this;
		let { checklist, unfillData, satisfyData, followUpData } = props;
		// console.log(this.state.images);
		// console.log(this.state.question);

		let viewIndex = action == 'next' ? index + 1 : index - 1;
		let newItem;
		let fullCheckLists = checklist.questions;

		// fullCheckLists.forEach((item)=>{
		// 	item.data.forEach((x) => {
		// 		if(x.currentIndex == viewIndex) {
		// 			newItem = x;
		// 		}
		// 	})
		// })
		fullCheckLists.some((item) => {
			item.data.some((x) => {
				if (x.currentIndex == viewIndex) {
					newItem = x;
				}
				return newItem;
			})
			return newItem;
		})

		if((this.state.images == undefined || this.state.images.length < 1) && this.state.question.status != "Unfill" && !this.state.showAlertNextItem){
			if(this.state.question.status == "FollowUp" && checklist.require_photo_for_failed) {
				Alert.alert(
					i18.shouldInputPhotoForFailed,
					'',
					[
						{text: i18.OK, onPress: () => {
							this.setState({
								showAlertNextItem: true,
							});
						}},
					]
				)
			}else if(this.state.question.status == "FollowUp" && !checklist.require_photo_for_failed){
				this.setState({
					question: newItem,
					selected: newItem.selectOptions,
					otherText: newItem.otherText,
					images: newItem.images,
					comments: newItem.comments ? newItem.comments : '',
					showAlertNextItem: false,
				});
			}

			if(this.state.question.status == "Satisfy" && checklist.require_photo_for_statisfy) {
				Alert.alert(
					i18.shouldInputPhotoForSatisfy,
					'',
					[
						{text: i18.OK, onPress: () => {
							this.setState({
								showAlertNextItem: true,
							})
						}},
					]
				)
			}else if(this.state.question.status == "Satisfy" && !checklist.require_photo_for_statisfy){
				this.setState({
					question: newItem,
					selected: newItem.selectOptions,
					otherText: newItem.otherText,
					images: newItem.images,
					comments: newItem.comments ? newItem.comments : '',
					showAlertNextItem: false,
				});
			}
		}else  {
			this.setState({
				question: newItem,
				selected: newItem.selectOptions,
				otherText: newItem.otherText,
				images: newItem.images,
				comments: newItem.comments ? newItem.comments : '',
				showAlertNextItem: false,
			});
		}
		
	}

	renderFooter = () => {
		let total = this.state.total;
		let question = this.state.question;

		let renderPrevButton = question.currentIndex > 1 ? (
			<Button style={footerStyles.nextButton} textStyle={footerStyles.nextButtonText} onPress={() => this.viewOtherItem('prev', question.currentIndex)}>{i18.lastItem}</Button>
		) : (<View />);

		let renderNextButton = question.currentIndex < total ? (
			<Button style={footerStyles.nextButton} textStyle={footerStyles.nextButtonText} onPress={() => this.viewOtherItem('next', question.currentIndex)}>{i18.nextItem}</Button>
		) : (<View style={{ width: 80 }} />);

		return (
			<View style={footerStyles.container}>
				{/* <Button style={footerStyles.saveButton} textStyle={footerStyles.saveButtonText}>儲存草稿</Button> */}
				<View />
				<View style={footerStyles.paging}>
					{renderPrevButton}
					<Text>{question.currentIndex}/{total}</Text>
					{renderNextButton}
				</View>
			</View>
		);
	}

	_showModal = () => this.setState({ isModalVisible: true })

	_hideModal = () => this.setState({ isModalVisible: false })

	render() {
		let { props } = this;
		let { checklist } = props;

		let question = this.state.question;
		let translatedUnitAddress = this.state.translatedUnitAddress;
		let defaultOptions = [];
		if(checklist.has_option_statisfy){
			defaultOptions.push('滿意');
		}
		if(checklist.can_batch_failed){
			defaultOptions.push('需跟進');
		}

		if(checklist.has_option_missing){
			defaultOptions.push('未安裝');
		}


		let additionalOptions = [];

		if(!checklist.can_batch_failed){
			additionalOptions = this.state.question.options;
		}


		let options = defaultOptions.concat(additionalOptions);

		let images = this.state.images;

		let showInputOther = checklist.has_option_other ? (
		<TextInput
			underlineColorAndroid='transparent'
			style={styles.otherInput}
			editable={this.state.isUnit ? false : true}
			onChangeText={(text) => {
				this.setState({ otherText: text });
			}}
			onBlur={() => {
				props.updateOtherText(question._id, this.state.otherText);
			}}
			placeholder="其他(於「其他描述」一項詳細描述)"
			value={this.state.otherText}
		/>) : null;
		
		let showImageButton = (this.state.isUnit && ( !images || images.length < 1 )) ? (
			<TouchableOpacity style={[styles.iconButton, {opacity: 0.2}]} textStyle={styles.iconButtonText} disabled={true}>
				<View style={{ flexDirection: 'row' }}>
					<Icon style={styles.iconButtonText} icon={"PICTURE"} />
				</View>
			</TouchableOpacity>
		) : (
			<TouchableOpacity onPress={() => this.launchImageEditor()} style={styles.iconButton} textStyle={styles.iconButtonText}>
				<View style={{ flexDirection: 'row' }}>
					<Icon style={styles.iconButtonText} icon={"PICTURE"} />
					{
						(images && images.length > 0) ? (
							<Text style={styles.iconButtonText}>{images.length}</Text>
						) : null
					}
				</View>
			</TouchableOpacity>
		)

		return (
			<View style={styles.container}>

				<Nav
					left='BACK'
					showDropDown={false}
					onLeftPress={this.onBack}
				>{translatedUnitAddress + "\n檢查清單"}</Nav>

				<View style={headerStyles.container}>
					<Text style={headerStyles.title}>{question.location}</Text>
				</View>

				<View style={styles.body}>
					<View style={styles.header}>
						<Text style={{ width: '60%' }}>{question.description}</Text>
						<Button style={styles.iconButton} textStyle={styles.iconButtonText} onPress={() => this._showModal()}>
							<Icon style={styles.iconButtonText} icon={"COMMENTS"} />
						</Button>
						<Button style={styles.iconButton} textStyle={styles.iconButtonText} disabled={(this.props.floor_plans.length == 0)} onPress={() => {
							this.props.navigation.navigate('FloorPlan', { floor_plans: this.props.floor_plans, questionId: this.state.question._id, isUnit: this.state.isUnit });
						}}>
							<Icon style={styles.iconButtonText} icon={"FLOORPLAN"} />
						</Button>
						{showImageButton}
					</View>
					
					{showInputOther}
					

					<ScrollView style={[styles.list, { marginTop: 5 }]}>
						{options.map(this.renderOption)}
					</ScrollView>

				</View>

				<Modal isVisible={this.state.isModalVisible} style={{ alignItems: 'center' }}>
					<View style={{ backgroundColor: 'rgba(252, 252, 252, 0.9)', width: 270, height: 268, borderRadius: 8, justifyContent: 'center', alignItems: 'center', position: 'absolute', top: '12%' }}>
						<Text style={{ fontWeight: '600', fontSize: 17 }}>{i18.Comment}</Text>
						<TextInput
							underlineColorAndroid='transparent'
							editable={this.state.isUnit ? false : true}
							style={{
								marginTop: 12,
								marginLeft: '10%',
								width: '80%',
								height: 160,
								borderRadius: 3,
								backgroundColor: "#FFFFFF",
								fontSize: 16,
								padding: 5
							}}
							onChangeText={(text) => this.setState({ comments: text })}
							value={this.state.comments}
							multiline={true}
						/>
						<View style={{ borderTopColor: '#D2CACA', borderTopWidth: 1, justifyContent: 'center', alignItems: 'center', flexDirection: 'row', width: '100%', marginTop: 5 }}>
							
							<TouchableOpacity onPress={() => this._hideModal()} style={{ flex: 1, height: 44  }}>
								<View style={{ flex: 1, justifyContent: 'center', alignItems: 'center'}}>
									<Text style={{ color: '#2583F9', fontSize: 17, fontWeight: '500' }}>{i18.Cancel}</Text>
								</View>
							</TouchableOpacity>
							{	
								(!this.state.isUnit) ? (
									<View style={{ height: 44, backgroundColor: '#D2CACA', width: 1 }} />
								): null
							}
							{	
								(!this.state.isUnit) ? (	
									<TouchableOpacity onPress={() => {
										props.updateComments(question._id, this.state.comments);
										this._hideModal();
									}} style={{ flex: 1, height: 44 }}>
										<View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><Text style={{ color: '#2583F9', fontSize: 17, fontWeight: '500' }}>{i18.Submit}</Text></View>
									</TouchableOpacity>
								): null
							}
						</View>
					</View>
				</Modal>

				{this.renderFooter()}
			</View>
		);
	}
}

const headerStyles = StyleSheet.create({
	container: {
		flex: 0.8,
		backgroundColor: 'rgba(242.25, 242.25, 242.25, 1)',
		justifyContent: 'center'
	},
	title: {
		paddingHorizontal: 10
	}
})

const footerStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: 'rgba(242.25, 242.25, 242.25, 1)',
		justifyContent: 'space-between',
		alignItems: 'center',
		borderTopColor: 'black',
		borderTopWidth: 1,
		paddingVertical: 6,
	},
	saveButton: {
		borderColor: 'gray',
		borderWidth: 1,
		borderRadius: 3,
		paddingHorizontal: 6,
		paddingVertical: 3,
		marginHorizontal: 10,
		backgroundColor: 'white'
	},
	saveButtonText: {
		color: 'rgba(255, 127.5, 51, 1)',
		padding: 5,
		backgroundColor: 'transparent',
		fontSize: 16,
		fontWeight: '500',
	},
	paging: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-around'
	},
	nextButton: {
		borderRadius: 3,
		paddingHorizontal: 6,
		paddingVertical: 3,
		marginHorizontal: 10,
		backgroundColor: 'rgba(112.2, 112.2, 112.2, 1)',
	},
	nextButtonText: {
		color: 'white',
		padding: 5,
		backgroundColor: 'transparent',
		fontSize: 16,
		fontWeight: '500',
	},
})

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: 'white',
	},
	body: {
		flex: 10,
		padding: 5
	},
	header: {
		flexDirection: 'row',
		paddingHorizontal: 5,
		paddingVertical: 10,
		alignItems: 'center',
		justifyContent: 'space-around'
	},
	icon: {
		width: 80,
		height: 80,
	},
	iconButton: {
		paddingHorizontal: 0,
		paddingVertical: 0,
		borderColor: 'gray',
		borderWidth: 0.5,
		borderRadius: 3,
		backgroundColor: 'white'
	},
	iconButtonText: {
		color: 'rgba(255, 127.5, 51, 1)',
		padding: 5,
		backgroundColor: 'transparent'
	},
	otherInput: {
		height: 40,
		borderColor: 'gray',
		borderWidth: 1,
		paddingHorizontal: 20,
	},
	list: {
		flex: 1,
		marginVertical: 5
	},
	option: {
		flex: 1,
		borderBottomWidth: 1,
	},
	optionText: {
		padding: 20,
	},
});

const mapStateToProps = (state, ownProps) => {
	return {
		sectionData: state.checklists.sectionData,
		unfillData: state.checklists.unfillData,
		satisfyData: state.checklists.satisfyData,
		followUpData: state.checklists.followUpData,
		checklist: state.checklists.filteredChecklist,
		floor_plans: state.project.floor_plans
	}
}
import { CheckLists as checkListsMapDispatchToProps, Project as projectMapDispatchToProps, Loading as loadingMapDispatchToProps } from 'Controller';

let checkListsProps;
let projectProps;
let loadingProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps) => {
	checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	loadingProps = loadingMapDispatchToProps(dispatch, ownProps)
	return { ...checkListsProps, ...projectProps, ...loadingProps };
})(CheckListItem);
