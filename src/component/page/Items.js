import React, { Component } from 'react';
import {
	StyleSheet,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Animated,
	Alert,
	Text

} from 'react-native';
import { connect } from 'react-redux'
// import Text from 'react-native-text';

import { taskList as Theme } from 'Theme';
import { Icon, Nav, DropDownList } from 'Item';
import i18 from 'i18';
import ScrollableTabView, { DefaultTabBar, } from 'react-native-scrollable-tab-view';
import ActionSheet from 'react-native-actionsheet';


const CANCEL_INDEX = 0;
const DESTRUCTIVE_INDEX = 100;
const title = i18.ChangeStatus;


class TabBar extends Component {

	renderTab = (item, page) => {
		const styles = tabStyles;

		let { activeTab, tabs, goToPage } = this.props;		
		var isTabActive = activeTab === page;

		return (
			<TouchableOpacity key={item.label} onPress={() => goToPage(page)} style={styles.tab}>
				<Text style={[styles.text]}>
					{ i18[item.label] } ({item.label == 'All' ? this.props.tasks.length : this.props.tasks.filter(item => item.selected == true).length})
				</Text>
			</TouchableOpacity>
		);
	}

	render() {
		const styles = tabStyles;
		
		let { containerWidth, tabs, scrollValue} = this.props;
		let left = scrollValue.interpolate({ inputRange: [0, 1], outputRange: [0, containerWidth / tabs.length ] });

		return (
			<View style={styles.container}>
				<View style={styles.tabs}>
					{ tabs.map(this.renderTab) }
				</View>
				<Animated.View style={[styles.line, { width:containerWidth / tabs.length, left }]} />
			</View>
		);
	}
}

const tabStyles = StyleSheet.create({
	container:{
		height:50,
		borderBottomWidth:1,
		borderColor:'#888',
	},
	tab: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
	},
	tabs: {
		flex:1,
		flexDirection: 'row',
		justifyContent: 'center',
		backgroundColor:'#fff',
	},
	text:{
		fontSize: 14
	},
	line: {
		position: 'absolute',
		height: 3,
		backgroundColor: '#000',
		bottom: 0,
	}
});


export default class Items extends Component {

	constructor(props) {
		super(props);
		this.state = {
			showSelectionButtons: false,
			selectedData:[],
			dropDownList: {},
			dropDownData: [],
			firstLoad: false,
			options: [],
		};
	}

	componentWillMount() {
		let { props } = this;
		let { projectDetail, user } = props;

		if(!this.props.navigation.state.hasOwnProperty('params')){
			props.cleanItemData();
			props.fetchLocalItems();
		}

		let options = [];
		options.push(i18.Cancel);
		let secondArray = user.roles;
		
		projectDetail.defect_item_status.forEach((item)=>{
			let firstArray = item.acl;
			let found = firstArray.some(r=> secondArray.includes(r));
			if(found){
				options.push(item.name);
			}
		})
		this.setState({options});
	}

	componentWillReceiveProps(nextProps) {
		let { props } = this;
		let { language } = props;

		if((nextProps.items && nextProps.items.length != props.items.length)  || nextProps.language != language ){
			let items = nextProps.items;

			props.updateItemsCount(items.length);

			if (!this.state.firstLoad) {
				let language = nextProps.language;
				
				let dropDownData = props.getItemDropDownData(this.state.dropDownList, items, language);
	
				this.setState({
					dropDownData: dropDownData,
					// firstLoad: true
				});
			}
		}
	}

	componentDidMount() {
		//console.log(this.props.navigation.state.hasOwnProperty('params'))
		if(this.props.navigation.state.hasOwnProperty('params')){
			let { props } = this;
			let { language, items } = props;
			
			let dropDownData;
			console.log("items", items)
			dropDownData = props.getItemDropDownData(this.state.dropDownList, items, language);
			console.log(dropDownData);
			this.filterData(items, dropDownData);
			
			this.setState({
				dropDownData: dropDownData,
				firstLoad: true
			});	
		}	
	
	}

	onShowDropDownList(data) {
		this.setState({
			dropDownList: data,
		});
	}

	onFilterList(title, list, filter) {
		let { props } = this;
		let { items, language } = props;

		var newDropDownData = this.state.dropDownData;
		let findIndexOfTitle = 0;

		for(var key in newDropDownData)
		{
			if(newDropDownData[key].title==title){
				findIndexOfTitle = key;
			}
		}

		let dropDownList = this.state.dropDownList;
		newDropDownData.forEach((item, i) => {
			if(item.title == title) {
				let array;
				let compareValueExist = false;
				// filter.forEach((a)=>{
				// 	if(item.list.includes(a)){
				// 		compareValueExist = true;
				// 	}
				// });

				compareValueExist = filter.some((a) => {
					return item.list.includes(a);
				});

				if(!compareValueExist){
					array = [];
				}else {
					array = filter;
				}
				
				if(!item.filter.includes(list)) {
					array.push(list);
				}else {
					var index = array.indexOf(list);
					array.splice(index, 1);
				}

				item.filter = array;
				dropDownList.filter = array;
			}else {
				if( i > findIndexOfTitle) {
					item.filter = [];
				}
			}
		});


		let dropDownData = props.getItemDropDownData(newDropDownData, items, language);

		this.setState({
			dropDownData: dropDownData,
			dropDownList: dropDownList
		});

		this.filterData(items, dropDownData);

	}

	cleanDropDownList(title) {
		let { props } = this;
		let { items, language } = props;

		for(var key in this.state.dropDownData)
		{
			if(this.state.dropDownData[key].title==title){
				findIndexOfTitle = key;
			}
		}

		let newDropDownData = this.state.dropDownData.map((data, i)=>{
			if(data.title == title){
				data.filter = [];
			}else {
				if( i > findIndexOfTitle) {
					data.filter = [];
				}
			}
			return data;
		});

		let dropDownData = props.getItemDropDownData(newDropDownData, items, language);
		let dropDownList = this.state.dropDownList;
		
		if(dropDownList.title == title) {
			dropDownList.filter = [];

		}


		this.setState({
			dropDownData: dropDownData,
			dropDownList: dropDownList
		});
		this.filterData(items, dropDownData);
	}


	closeDropDownList() {
		this.setState({
			dropDownList: {},
		});
	}


	filterData(sectionData, newDropDownData, clear) {
		
		let { props } = this;

		let filteredSectionData;
		let dropDownData = newDropDownData;
		let filter = {};

		if(this.props.navigation.state.hasOwnProperty('params') && !clear){
			let newArray = [];
			if(this.props.navigation.state.params.status != 'showAll'){
				newArray.push(this.props.navigation.state.params.status);
			}
			dropDownData[0].filter = newArray;
			sectionData = sectionData.filter(item => item.isDefect).filter(item => item.unit == this.props.navigation.state.params.unit).filter(item => item.checklist == this.props.navigation.state.params.checklist);
		}

		if(dropDownData.length > 0){
			if(dropDownData[0].filter.length > 0) {
				filter.status = dropDownData[0].filter;
			}
			if(dropDownData[1].filter.length > 0) {
				filter.tower = dropDownData[1].filter;
			}
			if(dropDownData[2].filter.length > 0) {
				filter.floor = dropDownData[2].filter;
			}
			if(dropDownData[3].filter.length > 0) {
				filter.flat = dropDownData[3].filter;
			}
			if(dropDownData[4].filter.length > 0) {
				filter.location = dropDownData[4].filter;
			}
			if(dropDownData[5].filter.length > 0) {
				filter.level1 = dropDownData[5].filter;
			}
			if(dropDownData[6].filter.length > 0) {
				filter.level2 = dropDownData[6].filter;
			}
			
	
			let filteredSectionData = sectionData.filter((item)=>{
				for(let key in filter) {
					if(item[key] === null || item[key] === undefined || !filter[key].includes(item[key])){
						return false;
					}
				}
				return true;
			}) 
			
			props.filterItem(filteredSectionData);
		}
		
		
	}

	renderItem = (item) => {
		
		const styles = itemStyles;
		let containerStyle = [styles.container];
		let { props } = this;
		let { unit_address, filterData } = props;
		
		let itemDetail = item.item;

		
		let translatedUnitAddress;
		translatedUnitAddress = unit_address[itemDetail.type] || "";
				
		if(itemDetail.tower) {
			translatedUnitAddress = translatedUnitAddress.replace("{tower}", itemDetail.tower);
		}

		if(itemDetail.flat) {
			translatedUnitAddress = translatedUnitAddress.replace("{flat}", itemDetail.flat);
		}

		if(itemDetail.floor) {
			translatedUnitAddress = translatedUnitAddress.replace("{floor}", itemDetail.floor);
		}

		if(this.state.showSelectionButtons) {
			additionalStyleForChangeStatus = -50;
		} else {
			additionalStyleForChangeStatus = 0;
		}


		let showCheckBox;
		let selected = false;
		if (this.state.selectedData.indexOf(itemDetail._id) > -1) {
			selected = true;
		}

		if(this.state.showSelectionButtons){
			showCheckBox = (
				<TouchableOpacity style={{
					left: 0, top: 0,  position:'absolute', height: '100%', alignItems: 'center', 
					justifyContent: 'center'
				}}onPress={() => {
					
					let selectedData = this.state.selectedData;

					if (selectedData.indexOf(itemDetail._id) > -1) {
						var index = selectedData.indexOf(itemDetail._id);
						selectedData.splice(index, 1);
					}else {
						selectedData.push(itemDetail._id);
					}

					this.setState({
						selectedData: selectedData,
					});

					// let sectionData = filterData.map((section, i) => {
					// 	if(i == 0) {
					// 		section.fake = section.fake ? false : true;
					// 	}
					// 	return section;   
					// });
					let sectionData = filterData.slice();
					if (sectionData.length > 0)
						sectionData[0].fake = !sectionData[0].fake;

					props.fakeUpdateItemData(sectionData);
	
                }}>
					<View style={{ alignItems: 'center', justifyContent: 'center'}}>
						<Icon icon={selected ? 'checked' : 'unchecked'} style={styles.checkIcon} />
					</View>	
				</TouchableOpacity>
			)
		}else {
			showCheckBox = <View/>;
		}

		let option;

		if (itemDetail.option.length > 16) {
			option = itemDetail.option.substring(0, 13) + '...'
		} else {
			option = itemDetail.option
		}
		
		
		return (
			<View>
				<TouchableOpacity style={[containerStyle, {right: additionalStyleForChangeStatus }]} onPress={() => {
						props.modifyItem(itemDetail._id);
						props.getUnitFloorPlan(itemDetail.unit);
						this.props.navigation.navigate("ItemDetail");
					}}>
					<View style={styles.detail}>
						<View style={styles.address}>
							<View style={{flex: 1.8, flexDirection: 'row', alignItems:'center'}}>
								<View>
									<Icon icon={'home'}  style={[styles.icon,{fontSize: 20}]} />
								</View>	
								<Text style={[styles.text, {fontWeight: 'bold', maxWidth: 140}]}>{translatedUnitAddress + (itemDetail.location ? ( " - " + itemDetail.location) : "")}</Text>
							</View>	
							<View style={{flex: 1, flexDirection: 'row', justifyContent:'center', alignItems:'center'}}>
								<Text style={[styles.text, {fontWeight: 'bold', color: '#767676', width: 80}]}>{itemDetail.level1} {itemDetail.level2}</Text>
							</View>	
						</View>
                        <View style={[styles.info, option === '滿意'? styles.infoBorderSatisfied: styles.infoBorderFollowup]}>
                            <Text style={[styles.text, option === '滿意'? styles.optionsSatisfied: styles.optionsFollowup]}>{option}</Text>
                        </View>
					</View>

					<View style={styles.statusContainer}>
						<Text style={styles.statusText}>{itemDetail.status ? itemDetail.status : " - "}</Text>
					</View>
				</TouchableOpacity>
				{showCheckBox}
			</View>
		);
	}


	renderList(label) {
		
		const styles = listStyles;
		let { props } = this;
		let { filterData } = props;
		
		if(label == 'All' && filterData.length > 0) {
			return (
				<View style={styles.container} tabLabel={{label}}>
					<FlatList style={styles.list} data={filterData} renderItem={this.renderItem} />
				</View>
			);
		}else if(label == 'Selected' && filterData.length > 0) {
			return (
				<View style={styles.container} tabLabel={{label}}>
					<FlatList style={styles.list} data={filterData} renderItem={this.renderItem} />
				</View>
			);
		}
	}

	updateSelectionButtons(val) {
		this.setState({
			showSelectionButtons: val,
			selectedData: [],
		});
	}

	onDownload(c) {
        if (c == 'none') {
            Alert.alert(
                i18.noInternetConnection,
                '',
                [
                    {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                ]
            )
            return
        }
		this.setState({
			showSelectionButtons: false
		})
		this.props.navigation.navigate("ItemsDownload");
	}

	showActionSheet() {
		this.ActionSheet.show()
	}
	
	handlePress(i) {
		let { props } = this;
		let { filterData } = props;
		if(i != 0){
			let option = this.state.options[i];

			props.updateSelectOptions(this.state.selectedData, option);
			dropdownData = this.state.dropDownData.map((item, i)=>{
				if(i == 0){
					if(!item.list.includes(option)){
						item.list.push(option);
					}
				}
				return item 
			});

			// let sectionData = filterData.map((section, i) => {
			// 	if(i == 0) {
			// 		section.fake = section.fake ? false : true;
			// 	}
			// 	return section;   
			// });

			Alert.alert(
				i18.AskForSubmitItem,
				'',
				[
					{text: i18.Cancel, onPress: () => {
						this.setState({
							selectedData: [],
							dropDownData: dropdownData
						});

						let sectionData = filterData.slice();
						if (sectionData.length > 0){
							sectionData[0].fake = !sectionData[0].fake;
							props.fakeUpdateItemData(sectionData);
						}
			
					}},
					{text: i18.OK, onPress: () => {
						props.selectItemsForPending(this.state.selectedData, this.props.items || []);
						this.setState({
							selectedData: [],
							dropDownData: dropdownData
						});

						let sectionData = filterData.slice();
						if (sectionData.length > 0){
							sectionData[0].fake = !sectionData[0].fake;
							props.fakeUpdateItemData(sectionData);
						}
					}},
				]
			)


		}
	}


	render() {
		let { props } = this;

		let { filterData, projectDetail, items, connectionInfo} = props;

		let showItem;
		if(this.state.showSelectionButtons && filterData.length > 0){
			showItem = (
				<ScrollableTabView
					tabBarPosition={'top'}
					style={styles.list}
					prerenderingSiblingsNumber={3}
					renderTabBar={() => <TabBar tasks={filterData}/>}>
					{this.renderList('All')}
					{this.renderList('Selected')}
				</ScrollableTabView>
			)
		}else if (!this.state.showSelectionButtons && filterData.length > 0) {
			showItem = (<FlatList style={styles.list} data={filterData} renderItem={this.renderItem} />)
		}else {
			showItem = (
				<View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
					<Icon style={{
						fontSize: 100,
						backgroundColor:'transparent',
						color: '#A8A8A8'}} icon={'no-report'}/>
					<Text style={{marginTop: 10, textAlign: 'center', width: 148, fontSize: 16, color: '#B9B9B9', lineHeight: 22, fontWeight: '600'}}>{i18.NoItem}</Text>
				</View>
			)
		}
		let showChangeStatusButton = null;
		if(this.state.showSelectionButtons && this.state.selectedData.length > 0) {
			showChangeStatusButton = (
				<View style={[styles.bottomContainer,{justifyContent: 'center', alignItems: 'center'}]}>
					<View style={{flex: 1}} />
					<View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
						<TouchableOpacity  onPress={() => {
							this.showActionSheet();
						}}>
							<View style={[styles.bottomButtonContainer, {borderColor: '#404040'}]}>
								<Text style={[styles.bottomButtonText, {color: '#404040'}]}>{i18.ChangeStatus}</Text>
							</View>
						</TouchableOpacity>
					</View>	
					<View style={{flex: 1, justifyContent: 'center', alignItems: 'flex-end'}}>
						<TouchableOpacity  onPress={() => {
							Alert.alert(
								i18.AskForSubmitItem,
								'',
								[
									{text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
									{text: i18.OK, onPress: () => {
										props.selectItemsForPending(this.state.selectedData, this.props.items || []);
									}},
								]
							)
						}}>
							<View style={[styles.bottomButtonContainer, {borderColor: '#404040'}]}>
								<Text style={[styles.bottomButtonText, {color: '#404040'}]}>{i18.Submit}</Text>
							</View>
						</TouchableOpacity>
					</View>		
				</View>	
			)
		}

		let showSelectionButtons = null;
		if(filterData.length > 0) {
            filterData.map(data => {
                data.sortKey = (data.tower + ' ' + data.floor + ' ' + data.flat).toLowerCase();
            });
            filterData = filterData.sort(function(a, b) {
                return naturalCompare(a.sortKey, b.sortKey);
            });
			showSelectionButtons = this.state.showSelectionButtons ? (
				<View style={[styles.bottomContainer,{justifyContent: 'space-between',}]}>
					<TouchableOpacity  onPress={() => {
						let selectedData = [];
						filterData.forEach((item)=> {
							selectedData.push(item._id);
						})

						this.setState({selectedData: selectedData});
						// let sectionData = filterData.map((section, i) => {
						// 	if(i == 0) {
						// 		section.fake = section.fake ? false : true;
						// 	}
						// 	return section;   
						// });
						let sectionData = filterData.slice();
						if (sectionData.length > 0)
							sectionData[0].fake = !sectionData[0].fake;
						props.fakeUpdateItemData(sectionData);
					}}>
					<View style={[styles.bottomButtonContainer, {borderColor: '#E1E1E1', backgroundColor: '#FFFFFF'}]}>
						<Text style={[styles.bottomButtonText, {color: '#FF8136'}]}>{i18.chooseAll}</Text>
					</View>
					</TouchableOpacity>
					<TouchableOpacity  onPress={() => {
						this.setState({selectedData: []});
						// let sectionData = filterData.map((section, i) => {
						// 	if(i == 0) {
						// 		section.fake = section.fake ? false : true;
						// 	}
						// 	return section;   
						// });
						let sectionData = filterData.slice();
						if (sectionData.length > 0) {
							sectionData[0].fake = !sectionData[0].fake;
						}
						props.fakeUpdateItemData(sectionData);
					}}>
					<View style={[styles.bottomButtonContainer, {borderColor: '#FF863E'}]}>
					<Text style={[styles.bottomButtonText, {color: '#FF8136'}]}>{i18.deselect}</Text>
					</View>
					</TouchableOpacity>
					<TouchableOpacity  onPress={() => this.updateSelectionButtons(false)}>
					<View style={[styles.bottomButtonContainer, {borderColor: '#404040'}]}>
						<Text style={[styles.bottomButtonText, {color: '#404040'}]}>{i18.chooseBack}</Text>
					</View>
					</TouchableOpacity>   
				</View>  
			) : (
				<View style={[styles.bottomContainer, {justifyContent: 'center'}]}>
					<TouchableOpacity  onPress={() => this.updateSelectionButtons(true)}>
					<View style={[styles.bottomButtonContainer, {borderColor: '#404040'}]}>
						<Text style={[styles.bottomButtonText, {color: '#404040'}]}>{i18.choose}</Text>
					</View>
					</TouchableOpacity> 
				</View>  
			);
		}

		console.log(this.state.dropDownList)
		let showDropDownList = Object.keys(this.state.dropDownList).length > 0  ? (
			<DropDownList 
				closeDropDownList={()=> this.closeDropDownList()}
				dropDownData={this.state.dropDownData}
				dropDownList={this.state.dropDownList}
				cleanDropDownList={(title) => this.cleanDropDownList(title)}
				onFilterList={(title, list, filter)=> this.onFilterList(title, list, filter)}
				/>
		) : null;

		

		return (
			<View style={styles.container}>
				<Nav 
					onRightPress={() => {this.props.navigation.state.hasOwnProperty('params') ? null : this.onDownload(connectionInfo)}}
					right={this.props.navigation.state.hasOwnProperty('params') ? null: "DOWNLOAD"}
					left={this.props.navigation.state.hasOwnProperty('params')? "BACK" : null}
					onLeftPress={()=> {
						if(this.props.navigation.state.hasOwnProperty('params')){
							props.cleanItemData();
							props.fetchLocalItems();
							this.props.navigation.goBack();
						}
					}}
					showDropDown={(filterData && filterData.length > 0)}
					onShowDropDownList={(val)=>this.onShowDropDownList(val)} 
					dropDownData={this.state.dropDownData}>
					{i18.Items}
				</Nav>
				{showItem}
				{showChangeStatusButton}
				{showSelectionButtons}
				{showDropDownList}   
				<ActionSheet
					ref={o => this.ActionSheet = o}
					title={title}
					options={this.state.options}
					cancelButtonIndex={CANCEL_INDEX}
					onPress={(i) => this.handlePress(i)}
       			/>
			</View>
		);
	}
}


const listStyles = StyleSheet.create({
	container: {
        flex: 1,
	},
	header: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingVertical:12,
	},
	headerField: {
		flex: 2,
		fontSize: 12,
		textAlign: 'center',
	},
	list: {
		flex: 1,
	},
	popUp: {
		flexDirection: 'row',
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,

		borderTopColor: 'black',
		borderTopWidth: 0.5,
		justifyContent: 'space-around',
		alignItems: 'center',
		height: 40,
	},
});

const itemStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: Theme.item.bg,
		justifyContent:'space-between',
		marginHorizontal: 5,
		marginTop: 6,
		shadowColor: '#9f9f9f',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.9,
		shadowRadius: 2,
		minHeight: 84,

	},
	icon: {
		color: '#484848',
        width: 35,
		backgroundColor:'transparent',
		textAlign:'center',
	},
	detail: {
		flex: 4.5,
	},
	address: {
		flex:1,
		flexDirection: 'row',
		alignItems:'center',
		paddingLeft: 5,	
	},
	info: {
		flex:1,		
		flexDirection: 'row',
		alignItems:'center',
		backgroundColor: '#F5F5F5',
		borderRightWidth: 1,
		borderRightColor: '#FFFFFF',
		borderBottomWidth: 3,
		borderBottomColor: '#C77777',
		paddingLeft: 5,	
	},
    infoBorderSatisfied: {
        borderBottomColor: '#69b29d',
    },
    infoBorderFollowup: {
        borderBottomColor: '#C77777',
    },
	text: {
		paddingHorizontal: 3,
		color: Theme.item.text,
		fontSize:14,
	},
	statusContainer: {
		flex:1,	
		backgroundColor: "#F5F5F5",
		alignItems: 'center',
		justifyContent: 'center',
	},
	statusText: {	
		fontSize: 14,
		color: '#353535',
		fontWeight: 'bold'
	},
	checkIcon: {
        color: '#FF8238',
        width: 52,
		fontSize: 26,
		backgroundColor:'transparent',
		textAlign:'center',
    },
    optionsSatisfied: {
        color: '#69b29d'
    },
    optionsFollowup: {
        color: '#C77777'
    },
});

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	list: {
		flex: 1,
	},
	bottomContainer: {
		padding: 5,  
		minHeight: 44,
		borderTopWidth: 1,
		borderColor: '#AEAEAE' ,
		flexDirection: 'row',
		alignItems: 'center',
	},
	bottomButtonContainer: {
		justifyContent: 'center',
		alignItems: 'center',
		minHeight: 31,
		width: 95,
		borderWidth: 1,
		borderRadius: 4,
	},
	bottomButtonText: {
		textAlign: 'center',
		width: 90,
		fontSize: 16,
		fontWeight: 'bold',
	},
});

const mapStateToProps = (state, ownProps) => {
	let unit_address;
	if(state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0){
		if(state.setting.language == 'en') {
			unit_address = state.project.projectDetail.unit_address_en;
		}else if(state.setting.language == 'zh_hk') {
			unit_address = state.project.projectDetail.unit_address_zh;
		}else {
			unit_address = state.project.projectDetail.unit_address_cn;
		}
	}

	return {
		projectDetail: state.project.projectDetail,
		user: state.auth.user,
		language: state.setting.language,
        connectionInfo: state.setting.connectionInfo,
		items: state.items.itemsWithHistory,
		unit_address: unit_address,
		filterData: state.items.filterDataWithHistory,	
	}
}


import { Items as itemsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';
import naturalCompare from "string-natural-compare";

let itemsProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	itemsProps = itemsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {...itemsProps, ...projectProps};
})(Items);
