import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Dimensions,
	ScrollView
} from 'react-native';
import { connect } from 'react-redux'

import { taskList as Theme, nav } from 'Theme';
import { Icon, Nav, DropDownList } from 'Item';
import i18 from 'i18';
import moment from 'moment';


const TaskListItem = (props) => {
	const { itemDetail, total, unfill, date, translatedUnitAddress, onPress } = props;

	const styles = itemStyles;
		
	return (
		<TouchableOpacity style={styles.container} onPress={() => onPress()}>
			<View style={styles.detail}>
				<View style={styles.address}>
					<Icon icon={'home'}  style={[styles.icon,{fontSize: 20}]} />
					<Text style={[styles.text, {fontWeight: 'bold'}]}>{translatedUnitAddress}</Text>
				</View>

				<View style={styles.info}>
					<Icon icon={'list-alt'} style={[styles.icon,{fontSize: 22}]} />
					<Text style={styles.text}>{itemDetail.name == 'Inspection' ? 'Ad-hoc Inspection' : itemDetail.name}</Text>
					{
						(total - unfill > 0) ? (
							<View style={[styles.bubble, styles.draft]}>
								<Text style={styles.bubbleText}>{i18.draft}</Text>
							</View>
						) : null
					}
					{
						(itemDetail.vip) ? (
							<Icon icon={'vip'} style={[styles.icon,{fontSize: 15, color: '#FFC800'}]} />
						) : null
					}
					{/* {
						(this.state.itemDetail.source) ? (
							<View style={[styles.bubble, styles.extraList]}>
								<Text style={styles.bubbleText}>{this.state.itemDetail.label}</Text>
							</View>
						) : null
					} */}
				</View>
			</View>

			<View style={styles.dateContainer}>
				<Text style={styles.date}>{date}</Text>
				<Text style={styles.state}>{i18.filled + " " + (total - unfill) + "/" + total}</Text>
			</View>
		</TouchableOpacity>
	);
};

const itemStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: Theme.item.bg,
		justifyContent:'space-between',
		padding: 9,
		marginHorizontal: 5,
		marginTop: 8,
		shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,

	},
	icon: {
		color: '#484848',
        width: 35,
		backgroundColor:'transparent',
		textAlign:'center',
		marginBottom: 5,
	},
	detail: {
		flex:1,
		justifyContent:'space-between',	
	},
	address: {
		flex:1,
		flexDirection: 'row',
		alignItems:'center',		
	},
	info: {
		flex:1,		
		flexDirection: 'row',
		alignItems:'center',
	},
	text: {
		paddingHorizontal: 3,
		color: Theme.item.text,
		fontSize:14,
	},
	bubble: {
		marginHorizontal: 2,
		paddingHorizontal: 6,
		paddingVertical: 3,
		borderRadius: 10,
		alignItems: 'center',
		justifyContent: 'center'
	},
	bubbleText: {
		fontSize:12,
		color: Theme.bubble.text,
	},
	draft: {
		backgroundColor: Theme.bubble.draft,
	},
	extraList: {
		backgroundColor: Theme.bubble.extra
	},
	dateContainer: {
		alignItems: 'flex-end',
		justifyContent: 'space-around',
	},
	date: {	
		fontSize:14,
		color: Theme.item.date,
	},
	state:{
		fontSize:14,
		color: Theme.item.state,
	},
});

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	list: {
		flex: 1,
		width: '100%'
	},
});

export default TaskListItem;
