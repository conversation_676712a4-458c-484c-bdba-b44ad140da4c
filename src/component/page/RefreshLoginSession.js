import React, { Component } from 'react';
import {
    Alert, Dimensions, StyleSheet,
    View,
    WebView,
} from 'react-native';
import {connect} from "react-redux";
const i18 = require('i18');
import { taskList as Theme } from 'Theme';
import { Icon, Nav } from 'Item';
import { SERVER_URL } from 'Config';

export default class RefreshLoginSession extends Component {
    constructor(props) {
        super(props);
    }

    samlOnLoad(event) {
        var url = event.nativeEvent.url;
        // console.log("samlOnLoad", url);
        if (url.indexOf('/saml/failure') > -1) {
            fetch(`${SERVER_URL}/saml/login`);
        } else {
            var components = url.split('?sessionID=');
            if (components.length > 1) {
                var session = components[1];
                setTimeout(() => {
                    this.props.refreshAdLogin(session).then((result) => {
                        this.props.navigation.goBack();
                        if (result) {
                            alert(i18.refreshSuccess);
                        } else {
                            alert(i18.incorrectCredential);
                        }
                    }).catch(err => {
                        alert(i18.refreshFailed);
                    })
                }, 0);
            }
        }
    }

    render() {
        let path = `${SERVER_URL}/saml/login`;
        return (
            <View style={styles.container}>
                <Nav
                    onLeftPress={()=>{
                        Alert.alert(
                            i18.handoverFormBack,
                            '',
                            [
                                {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                                {text: i18.yes, onPress: () => {
                                        this.props.navigation.goBack()
                                    }},
                            ]
                        )
                    }}
                    left="BACK">
                    {i18.refreshSession}
                </Nav>
                <WebView
                style={{flex: 10}}
                source={{uri: path}}
                userAgent={"MobileApp/1.0"}
                automaticallyAdjustContentInsets={false}
                javaScriptEnabled={true}
                scalesPageToFit={true}
                onLoadStart={this.samlOnLoad.bind(this)}
                />
            </View>
        );

    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Theme.bg,
    }
});

const mapStateToProps = (state, ownProps) => {
    return {
        user: state.auth.user
    }
}

import { Login as loginMapDispatchToProps } from 'Controller';

let loginProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
    loginProps = loginMapDispatchToProps(dispatch, ownProps)
    return {...loginProps};
})(RefreshLoginSession);