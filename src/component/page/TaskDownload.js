'use strict';

import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
    SectionList,
    ListView,
    Animated,
    FlatList,
    NetInfo,
    Alert,
} from 'react-native';

import Text from 'react-native-text';
import {theme, nav, taskList} from 'Theme';
import {Icon, Nav, DropDownList} from 'Item';
import i18 from 'i18';
import ScrollableTabView, {DefaultTabBar,} from 'react-native-scrollable-tab-view';
import {connect} from 'react-redux';
import naturalCompare from 'string-natural-compare';


class TabBar extends Component {

    renderTab = (item, page) => {
        const styles = tabStyles;

        let {activeTab, tabs, goToPage, selectedData} = this.props;
        var isTabActive = activeTab === page;

        return (
            <TouchableOpacity key={item.label} onPress={() => goToPage(page)} style={styles.tab}>
                <Text style={[styles.text]}>
                    {i18[item.label]} ({item.label == 'All' ? this.props.tasks.length : selectedData.length})
                </Text>
            </TouchableOpacity>
        );
    }

    render() {
        const styles = tabStyles;

        let {containerWidth, tabs, scrollValue} = this.props;
        let left = scrollValue.interpolate({inputRange: [0, 1], outputRange: [0, containerWidth / tabs.length]});

        return (
            <View style={styles.container}>
                <View style={styles.tabs}>
                    {tabs.map(this.renderTab)}
                </View>
                <Animated.View style={[styles.line, {width: containerWidth / tabs.length, left}]}/>
            </View>
        );
    }
}

const tabStyles = StyleSheet.create({
    container: {
        height: 50,
        borderBottomWidth: 1,
        borderColor: '#888',
    },
    tab: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    tabs: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'center',
        backgroundColor: '#fff',
    },
    text: {
        fontSize: 14
    },
    line: {
        position: 'absolute',
        height: 3,
        backgroundColor: '#000',
        bottom: 0,
    }
});


export default class TaskDownload extends Component {

    constructor(props) {
        super(props);
        this.state = {
            dropDownList: {},
            dropDownData: [],
            filterTasks: false,
            selectedData: [],
            isFirstLoad: true,
            filterData: []
        };
    }

    componentWillMount() {
        let {props} = this;
        let {projectDetail} = props;
        props.fetchChecklistList(projectDetail._id).then(() => {
            props.fetchChecklistDropdownOptions(projectDetail._id).then(dropDownData => {
                this.setState({
                    dropDownData: dropDownData,
                    isFirstLoad: false,
                });
            });
        });
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.filterData && this.state.filterData.length != nextProps.filterData.length) {
            this.setState({
                filterData: nextProps.filterData
            });
        }
    }

    onShowDropDownList(data) {
        this.setState({
            dropDownList: data,
        });
    }

    onBack = () => {
        this.props.navigation.goBack();
    }

    onFilterList(title, list) {
        // this.onSubmitFilter(false);
        var newDropDownData = this.state.dropDownData;
        newDropDownData.forEach((item, i) => {
            if (item.title == title) {
                if (!item.filter.includes(list)) {
                    item.filter.push(list);
                }
                else {
                    var index = item.filter.indexOf(list);
                    item.filter.splice(index, 1);
                }
            }
        });
        this.setState({
            dropDownData: newDropDownData,
        });
    }

    cleanDropDownList(title) {
        // let {props} = this;
        // let {checklists, language} = props;

        let dropDownData = this.state.dropDownData.map((data) => {
            if (data.title == title) {
                data.filter = [];
            }
            return data;
        });
        this.setState({
            dropDownData: dropDownData
        });
        // Deprecated logic
        // let checkDropDownHasFilter = false;
        //
        // checkDropDownHasFilter = dropDownData.some((item) => {
        //     return item.filter.length > 0;
        // });
        // if (checkDropDownHasFilter) {
        //     this.onSubmitFilter(false);
        // }

        // this.filterData(checklists, dropDownData);
    }

    checkUnitIsVip(tower, floor, flat) {
        let unitInfo = this.props.vipUnits.find((info) => {
            let match = false;
            if (info.tower) {
                match = info.tower == tower;
            }
            if (match)
                if (info.floor) {
                    match = info.floor == floor;
                }
            if (match)
                if (info.flat) {
                    match = info.flat == flat;
                }
            return match;
        });

        if (unitInfo) {
            return true;
        }

        return false;
    }

    renderItem(item, tab) {
        var index = 0;

        const styles = itemStyles;
        let {props} = this;
        let {unit_address} = props;

        let filterData = this.state.filterData;

        let translatedUnitAddress;
        translatedUnitAddress = unit_address[item.type] || "";


        if (item.tower) {
            // console.log(item.tower);
            translatedUnitAddress = translatedUnitAddress.replace("{tower}", item.tower);
        }

        if (item.flat) {
            translatedUnitAddress = translatedUnitAddress.replace("{flat}", item.flat);
        }

        if (item.floor) {
            translatedUnitAddress = translatedUnitAddress.replace("{floor}", item.floor);
        }


        var date = item._created_at;

        if (date) {
            date = date.split('T')[0];
        } else {
            // for debugging the case that the "item" is not the required object, possibly is an error object from the promise
            console.log(date);
        }

        let selected = false;
        if (this.state.selectedData.indexOf(item._id) > -1) {
            selected = true;
        }

        if (tab == 'All' || selected) {
            return (
                <TouchableOpacity key={index} style={styles.container} onPress={() => {

                    this.props.displayLoading();
                    setTimeout(() => {
                        let selectedData = this.state.selectedData;

                        if (selectedData.indexOf(item._id) > -1) {
                            var index = selectedData.indexOf(item._id);
                            selectedData.splice(index, 1);
                        } else {
                            selectedData.push(item._id);
                        }

                        this.setState({
                            selectedData: selectedData,
                        });

                        // let sectionData = filterData.map((section, i) => {
                        // 	if(i == 0) {
                        // 		section.fake = section.fake ? false : true;
                        // 	}
                        // 	return section;
                        // });
                        let sectionData = filterData.slice();
                        if (sectionData.length > 0)
                            sectionData[0].fake = !sectionData[0].fake;

                        // props.fakeUpdateTaskData(sectionData);
                        this.setState({
                            filterData: sectionData
                        });
                        this.props.removeLoading();
                    }, 0)
                }}>
                    <View style={styles.selection}>
                        <Icon icon={selected ? 'checked' : 'unchecked'} style={styles.checkIcon}/>
                    </View>
                    <View style={styles.mainContainer}>
                        <View style={styles.detail}>
                            <View>
                                <View style={styles.address}>
                                    <Icon icon={'home'} style={[styles.icon, {fontSize: 20}]}/>
                                    <Text style={[styles.text, {fontWeight: 'bold'}]}>
                                        {translatedUnitAddress}
                                    </Text>
                                </View>

                                <View style={styles.info}>
                                    <Icon icon={'list-alt'} style={[styles.icon, {fontSize: 20}]}/>
                                    <Text style={styles.text}>{item.name}</Text>
                                    {
                                        (this.checkUnitIsVip(item.tower, item.floor, item.flat)) ? (
                                            <Icon icon={'vip'} style={[styles.icon, {fontSize: 15, color: '#FFC800'}]}/>
                                        ) : null
                                    }
                                </View>
                            </View>
                        </View>

                        <View style={styles.dateContainer}>
                            <Text style={styles.date}>{date}</Text>
                        </View>
                    </View>
                </TouchableOpacity>
            );
        } else {
            return null
        }
        index++;
    }


    filterData(sectionData, newDropDownData) {

        let {props} = this;

        let filteredSectionData;
        let dropDownData = newDropDownData;
        let filter = {};

        if (dropDownData.length > 0) {
            if (dropDownData[0].filter.length > 0) {
                filter.name = dropDownData[0].filter;
            }
            if (dropDownData[1].filter.length > 0) {
                filter.tower = dropDownData[1].filter;
            }
            if (dropDownData[2].filter.length > 0) {
                filter.floor = dropDownData[2].filter;
            }
            if (dropDownData[3].filter.length > 0) {
                filter.flat = dropDownData[3].filter;
            }

            filteredSectionData = sectionData.filter((item) => {
                for (let key in filter) {
                    if (item[key] === null || item[key] === undefined || !filter[key].includes(item[key])) {
                        return false;
                    }
                }
                return true;
            });

            props.filterTask(filteredSectionData);
            // this.setState({
            //     filterData: filteredSectionData
            // });
        }

    }


    renderList(label) {

        const styles = listStyles;
        let {props} = this;
        let {checklists} = props;

        let filterData = this.state.filterData;
        filterData.map(data => {
            data.sortKey = (data.tower + ' ' + data.floor + ' ' + data.flat).toLowerCase();
        });
        filterData = filterData.sort(function(a, b) {
            return naturalCompare(a.sortKey, b.sortKey);
        });
        if (label == 'All' && filterData.length > 0) {
            return (
                <View style={styles.container} tabLabel={{label}}>
                    <FlatList style={styles.list} data={filterData} renderItem={({item}) => this.renderItem(item, 'All')}/>
                </View>
            );
        } else if (label == 'Selected' && filterData.length > 0) {
            return (
                <View style={styles.container} tabLabel={{label}}>
                    <FlatList style={styles.list} data={filterData}
                              renderItem={({item}) => this.renderItem(item, 'Selected')}/>
                </View>
            );
        }
    }

    // updateListSelection(selected) {

    //     let { props } = this;
    //     let { checklists } = props;

    //     var tasks = checklists;
    //     tasks.map((task)=>{
    //         task.selected = selected;
    //     });

    //     props.updateListSelection(tasks);

    // }

    onSubmitFilter(val) {
        let {dropDownData} = this.state;
        // if(dropDownData[1].filter.length === 0 || dropDownData[2].filter.length === 0) {
        //     alert(i18.taskSearchMandatoryMsg);
        //     return;
        // }
        let { props } = this;
        props.searchChecklists(props.projectDetail._id, dropDownData);
    }

    closeDropDownList() {
        this.setState({
            dropDownList: {},
        });
    }


    render() {
        let {props} = this;
        let {checklists, projectDetail, connectionInfo} = props;

        let filterData = this.state.filterData;
        var showDropDownList = Object.keys(this.state.dropDownList).length > 0 ? (
            <DropDownList
                dropDownList={this.state.dropDownList}
                dropDownData={this.state.dropDownData}
                closeDropDownList={() => this.closeDropDownList()}
                cleanDropDownList={(title) => this.cleanDropDownList(title)}
                onFilterList={(title, list) => this.onFilterList(title, list)}
            />
        ) : null;

        let showFilterButton = false;
        let dropDownData = this.state.dropDownData;

        if (dropDownData.length > 0) {
            if (dropDownData[0].filter.length > 0 || dropDownData[1].filter.length > 0 || dropDownData[2].filter.length > 0 || dropDownData[3].filter.length > 0) {
                showFilterButton = true;
            }
        }

        return (
            <View style={styles.container}>
                <Nav
                    left='BACK'
                    showDropDown={true}
                    onLeftPress={this.onBack}
                    showFilterButton={showFilterButton}
                    onSubmitFilter={() => {
                        this.onSubmitFilter(true);
                        this.filterData(checklists, this.state.dropDownData);
                    }}
                    onShowDropDownList={(val) => this.onShowDropDownList(val)}
                    dropDownData={this.state.dropDownData}>
                    {i18.TaskDownloadList}
                </Nav>
                <ScrollableTabView
                    tabBarPosition={'top'}
                    style={styles.list}
                    prerenderingSiblingsNumber={3}
                    renderTabBar={() => <TabBar tasks={filterData} selectedData={this.state.selectedData}/>}>
                    {this.renderList('All')}
                    {this.renderList('Selected')}
                </ScrollableTabView>
                <View style={styles.bottomContainer}>
                    <TouchableOpacity onPress={() => {
                        this.props.displayLoading();
                        setTimeout(() => {
                            let selectedData = this.state.selectedData;
                            let sectionData = filterData.slice();
                            if (sectionData.length > 0)
                                sectionData[0].fake = !sectionData[0].fake;
                            sectionData.forEach((item, idx) => {
                                if (idx == 0) {
                                    item.fake = item.fake ? false : true;
                                }
                                if (!selectedData.includes(item._id)) {
                                    selectedData.push(item._id);
                                }
                            })
                            this.setState({
                                selectedData: selectedData,
                                filterData: sectionData
                            });
                            this.props.removeLoading();
                        }, 0);
                        // let sectionData = filterData.map((section, i) => {
                        // 	if(i == 0) {
                        // 		section.fake = section.fake ? false : true;
                        // 	}
                        // 	return section;
                        // });
                        // props.fakeUpdateTaskData(sectionData);
                        // let sectionData = filterData.slice();
                    }}>
                        <View style={[styles.bottomButtonContainer, {borderColor: '#E1E1E1'}]}>
                            <Text style={[styles.bottomButtonText, {color: '#FF8136'}]}>{i18.chooseAll}</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {

                        this.props.displayLoading();
                        setTimeout(() => {
                            this.setState({selectedData: []})
                            //  let sectionData = filterData.map((section, i) => {
                            // 	if(i == 0) {
                            // 		section.fake = section.fake ? false : true;
                            // 	}
                            // 	return section;
                            // });
                            let sectionData = filterData.slice();
                            if (sectionData.length > 0)
                                sectionData[0].fake = !sectionData[0].fake;
                            // props.fakeUpdateTaskData(sectionData);
                            this.setState({
                                filterData: sectionData
                            });
                            this.props.removeLoading();
                        }, 0);
                    }}>
                        <View style={[styles.bottomButtonContainer, {borderColor: '#FF863E'}]}>
                            <Text style={[styles.bottomButtonText, {color: '#FF8136'}]}>{i18.deselect}</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        if (connectionInfo == 'wifi' || connectionInfo == 'wimax' || connectionInfo == 'ethernet') {
                            props.downloadChecklist(this.state.selectedData, projectDetail._id);
                            this.setState({selectedData: []});
                        } else if (connectionInfo == 'none') {
                            Alert.alert(
                                i18.noInternetConnection,
                                '',
                                [
                                    {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                                ]
                            )
                        } else {
                            Alert.alert(
                                i18.noWifiDownload,
                                '',
                                [
                                    {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                                    {
                                        text: i18.download, onPress: () => {
                                            props.downloadChecklist(this.state.selectedData, projectDetail._id);
                                            this.setState({selectedData: []});
                                        }
                                    },
                                ]
                            )
                        }
                    }}>
                        <View style={[styles.bottomButtonContainer, {
                            borderColor: '#FF8034',
                            backgroundColor: '#FF8034'
                        }]}>
                            <Text style={[styles.bottomButtonText, {color: '#FFFFFF'}]}>{i18.download}</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                {showDropDownList}
            </View>
        );
    }
}


const itemStyles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        marginTop: 8
    },
    mainContainer: {
        flex: 2,
        backgroundColor: taskList.item.bg,
        flexDirection: 'row',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.5,
        shadowRadius: 2,
    },
    checkIcon: {
        color: '#FF8238',
        width: 52,
        fontSize: 26,
        backgroundColor: 'transparent',
        textAlign: 'center',
    },
    icon: {
        color: '#484848',
        width: 44,
        marginBottom: 5,
        backgroundColor: 'transparent',
        textAlign: 'center',
    },
    detail: {
        flex: 2,
        padding: 7,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
    },
    selection: {
        flex: 0.5,
        alignItems: 'center',
        justifyContent: 'center',
    },
    address: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    info: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    text: {
        paddingHorizontal: 3,
        fontSize: 13,
    },
    bubble: {
        marginHorizontal: 2,
        paddingHorizontal: 6,
        paddingVertical: 3,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    bubbleText: {
        fontSize: 12,

    },
    dateContainer: {
        flex: 1,
        padding: 7,
        alignItems: 'center',
        justifyContent: 'center',
    },
    date: {
        fontSize: 13,
        marginBottom: 6,
        color: taskList.item.date,
    },
});


const listStyles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
    },
    headerField: {
        flex: 2,
        fontSize: 12,
        textAlign: 'center',
    },
    list: {
        flex: 1,
    },
    popUp: {
        flexDirection: 'row',
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,

        borderTopColor: 'black',
        borderTopWidth: 0.5,
        justifyContent: 'space-around',
        alignItems: 'center',
        height: 40,
    },
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: taskList.bg,

    },
    list: {
        flex: 1,
        width: '100%',
    },
    dropDownListContainer: {
        backgroundColor: '#fff',
        width: Dimensions.get('window').width * 0.85,
        height: Dimensions.get('window').height * 0.8,
        marginBottom: Dimensions.get('window').height * 0.05,
        borderWidth: 1,
    },
    bottomContainer: {
        padding: 5,
        minHeight: 44,
        backgroundColor: '#FFF',
        borderTopWidth: 1,
        borderColor: '#AEAEAE',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    bottomButtonContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 31,
        minWidth: 95,
        borderWidth: 1,
        borderRadius: 4,
    },
    bottomButtonText: {
        textAlign: 'center',
        minWidth: 90,
        fontSize: 16,
        fontWeight: 'bold',
    },
});

const mapStateToProps = (state, ownProps) => {
    let unit_address;
    if (state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0) {
        if (state.setting.language == 'en') {
            unit_address = state.project.projectDetail.unit_address_en;
        } else if (state.setting.language == 'zh_hk') {
            unit_address = state.project.projectDetail.unit_address_zh;
        } else {
            unit_address = state.project.projectDetail.unit_address_cn;
        }
    }
    return {
        checklists: state.checklists.readyToDownloadChecklists,
        unit_address: unit_address,
        projectDetail: state.project.projectDetail,
        language: state.setting.language,
        filterData: state.checklists.filterData,
        connectionInfo: state.setting.connectionInfo,
        vipUnits: state.project.vipUnits
    }
}


import {
    CheckLists as checkListsMapDispatchToProps,
    Project as projectMapDispatchToProps,
    Loading as loadingMapDispatchToProps
} from 'Controller';

let checkListsProps;
let projectProps;
let loadingProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps) => {
    checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
    projectProps = projectMapDispatchToProps(dispatch, ownProps)
    loadingProps = loadingMapDispatchToProps(dispatch, ownProps)
    return {...checkListsProps, ...projectProps, ...loadingProps};
})(TaskDownload);

