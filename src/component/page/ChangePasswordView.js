import React, { Component } from 'react';
import {
    View,
    Text,
    TextInput
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import Server from 'Server';
import { Button } from 'Item';
const i18 = require('i18');
import {error} from 'Constant';

export default class ChangePasswordView extends Component {

    constructor(props) {
        super(props);
        this.state = {
            oldPwd: "",
            newPwd: "",
            cmfPwd: ""
        }
    }

    inputValidation() {
        if (this.state.oldPwd.length == 0) {
            // this.props.actions.msgError('Old password is empty');
            this.refs['oldPwd'].focus();
            return false;
        }
        if (this.state.newPwd.length == 0) {
            // this.props.actions.msgError('New password is empty');
            this.refs['newPwd'].focus();
            return false;
        }
        if (this.state.cfmPwd != this.state.newPwd ) {
            // this.props.actions.msgError('The new password validation is failed');
            this.refs['cfmPwd'].focus();
            return false;
        }
        return true;
    }

    onUpdate = () => {
        if (this.inputValidation()) {
            let params = {
                _id: this.props.user._id,
                newPassword: this.state.newPwd,
                oldPassword: this.state.oldPwd
            }
            this.props.displayLoading("changePassword");
            Server.changePassword(params)
                .then((resp) => {
                    alert(i18.changePassword_success);
                    this.props.removeLoading("changePassword");
                    this.props.onDismiss();
                })
                .catch((err) => {
                    console.log("change password error", err);
                    if(err){
                        switch(err.message){
                            case error.ERROR_PASSWORD:
                                alert(i18.changePassword_wrongPassword);
                                break;
                            default:
                                alert(i18.changePassword_tryAgain);
                        }
                    }
                    console.log("change password error:", err);
                    this.props.removeLoading("changePassword");
                });
        }
    }

    render() {
        let { value } = this.state;

        return (
            <View style={styles.overlay}>
                <View style={styles.container}>
                    <Text style={styles.title}>{i18.changePassword}</Text>
                    <View style={styles.body}>
                        <View style={styles.columnStyle}>
                            <Text>{i18.oldPassword}</Text>
                            <TextInput
                                ref={'oldPwd'}
                                underlineColorAndroid='transparent'
                                secureTextEntry={true}
                                style={styles.inputStyle}
                                value={this.state.oldPwd}
                                onChangeText={oldPwd => this.setState({ oldPwd })} />
                        </View>
                        <View style={styles.columnStyle}>
                            <Text>{i18.newPassword}</Text>
                            <TextInput
                                ref={'newPwd'}
                                underlineColorAndroid='transparent'
                                secureTextEntry={true}
                                style={styles.inputStyle}
                                value={this.state.newPwd}
                                onChangeText={newPwd => this.setState({ newPwd })} />
                        </View>
                        <View style={styles.columnStyle}>
                            <Text>{i18.cfmPassword}</Text>
                            <TextInput
                                ref={'cfmPwd'}
                                underlineColorAndroid='transparent'
                                secureTextEntry={true}
                                style={styles.inputStyle}
                                value={this.state.cfmPwd}
                                onChangeText={cfmPwd => this.setState({ cfmPwd })} />
                        </View>
                        <View style={styles.rowStyle}>
                            <Button onPress={this.props.onDismiss}>{i18.chooseBack}</Button>
                            <Button onPress={this.onUpdate}>{i18.update}</Button>
                        </View>
                    </View>
                </View>
            </View>
        )
    }
}

const styles = {
    container: {
        backgroundColor: '#fff',
        display: 'flex',
        flexDirection: 'column',
        width: "100%"
    },
    title: {
        backgroundColor: '#aaa',
        color: '#fff',
        padding: 10,
    },
    body: {
        padding: 5,
        display: 'flex',
        flexDirection: 'column',
    },
    overlay: {
        position: 'absolute',
        top: 0, bottom: 0,
        left: 0, right: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        display: 'flex',
        paddingLeft: 20, paddingRight: 20
    },
    columnStyle: {
        padding: 5
    },
    rowStyle: {
        flexDirection: 'row',
        padding: 5,
        justifyContent: 'space-between'
    },
    inputStyle: {
        padding: 5,
        height: 40,
        borderWidth: 1
    }
}

const mapStateToProps = (state, ownProps) => {
    return {
        user: state.auth.user
    }
  }
  
  import {
    Others as othersMapDispatchToProps,
    Loading as loadingMapDispatchToProps
  } from 'Controller';
  
  let othersProps;
  let loadingProps;
  
  module.exports = connect(mapStateToProps, (dispatch, ownProps) => {
    othersProps = othersMapDispatchToProps(dispatch, ownProps)
    loadingProps = loadingMapDispatchToProps(dispatch, ownProps)
    return { ...othersProps, ...loadingProps };
  })(ChangePasswordView);