import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
} from 'react-native';
import { connect } from 'react-redux';

import Server from 'Server';
import { RootState } from '../../types';

const Button = require('Item').Button;
const Actions = require('Redux').Action.root;

// Define component props interface
interface PageSampleProps {
  someNum?: number;
  onClickTest: () => void;
  onClickTestFetch: () => void;
}

const PageSample: React.FC<PageSampleProps> = ({ someNum, onClickTest, onClickTestFetch }) => {
  const [showText, setShowText] = useState<boolean>(true);

  useEffect(() => {
    // Toggle the state every second
    const interval = setInterval(() => {
      setShowText(previousState => !previousState);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Use showText in the render to avoid unused variable warning
  const displayText = showText ? 'Visible' : 'Hidden';

  return (
    <View style={styles.container}>
      <Text style={styles.welcome}>
        Welcome to React Native!
      </Text>
      <Image style={styles.icon} source={require('Asset/Image').icon} />
      <Text style={styles.instructions}>
        {someNum} - {displayText}
      </Text>
      <Button onPress={onClickTest} text={'Test'} />
      <Button onPress={onClickTestFetch} text={'TestFetch'} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5FCFF',
  },
  welcome: {
    fontSize: 20,
    textAlign: 'center',
    margin: 10,
  },
  instructions: {
    textAlign: 'center',
    color: '#333333',
    marginBottom: 5,
  },
  icon: {
    width: 80,
    height: 80,
  },
});

const mapStateToProps = (state: RootState) => {
  return {
    someNum: state.root?.someNum,
  };
};

const mapDispatchToProps = (dispatch: any) => {
  return {
    onClickTest: () => {
      dispatch(Actions.test());
    },
    onClickTestFetch: () => {
      dispatch(Actions.testFetch());
      Server.test().then((resp: any) => {
        dispatch(Actions.testFetchSuccess(resp));
      }).catch((error: any) => {
        dispatch(Actions.testFetchError(error));
      });
    },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(PageSample);