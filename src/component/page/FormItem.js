import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Dimensions,
	ScrollView,
	NativeModules,
	Alert,
	Platform
} from 'react-native';
import { connect } from 'react-redux'

import { taskList as Theme, nav } from 'Theme';
import { Icon, Nav, DropDownList } from 'Item';
import i18 from 'i18';
import moment from 'moment';

const PdfModule = NativeModules.PdfModule;
import { _getHeaders } from 'Request';

export default class FormItem extends Component {

	constructor(props) {
		super(props);

		this.handleSavedPdfData = this.handleSavedPdfData.bind(this);
		this.handleSavedPdfPath = this.handleSavedPdfPath.bind(this);
		this.deletePdfData = this.deletePdfData.bind(this);
	}

	componentWillReceiveProps(nextProps) {
		
	}
	
	componentWillUnmount() {
		
	}

	componentDidMount() {
		
	}

	handleSavedPdfData(resultData) {
		// the data is stored for update
		console.log("handleSavedPdfData", resultData);
		PdfModule.dismissEditor();
		this.props.formObj.data = resultData;
		this.forceUpdate();
	}

	handleSavedPdfPath(resultPath) {
		// the pdf is signed and become static, ready to submit
		console.log("handleSavedPdfPath", resultPath);
		if (resultPath) {
			this.props.formObj.dataPath = resultPath;
		}
		PdfModule.dismissSignPdf();
		this.forceUpdate();
	}

	deletePdfData() {
		Alert.alert(
			i18.deleteForm + this.props.formObj.title + "'?",
			i18.deleteFormMsg,
			[
				{text: 'Cancel', onPress: () => console.log('Cancel Pressed'), style: 'cancel'},
				{text: 'OK', onPress: () => {
					if (this.props.formObj.dataPath && this.props.formObj.dataPath.length > 0) {
						PdfModule.deletePdf(this.props.formObj.dataPath, (result, err) => {
							console.log("PdfModule.deletePdf", err);
						});
					}
					this.props.formObj.dataPath = '';
					this.props.formObj.data = null;
					this.forceUpdate();
				}},
			],
		);
	}

	render() {
		if (Platform.OS == 'ios') {

			let buttons = (
				<View style={{flex: 2.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>   
					<TouchableOpacity  onPress={() => {
						let path = this.props.formObj.originalPath;
						/* TODO -- demo purpose auto-fill */
						let data = this.props.source();

						let pdfData = {
							fill_tower: data.tower,
							fill_floor: data.floor,
							fill_unit: data.flat,
							fill_owner_chi_lastname_1: data.lastName_zh,
							fill_owner_chi_firstname_1: data.firstName_zh,
							fill_owner_eng_lastname_1: data.lastName_en,
							fill_owner_eng_firstname_1: data.firstName_en,
							fill_owner_email_1: data.email,
							fill_owner_phone_1: data.phone,
							fill_owner_address_1: data.address,
							fill_owner_chi_lastname_2: data.lastName2_zh,
							fill_owner_chi_firstname_2: data.firstName2_zh,
							fill_owner_eng_lastname_2: data.lastName2_en,
							fill_owner_eng_firstname_2: data.firstName2_en,
							fill_owner_email_2: data.email2,
							fill_owner_phone_2: data.phone2,
							fill_owner_address_2: data.address2,
							fill_emergency_name_1: data.contactLastName + " " + data.contactFirstName,
							fill_emergency_phone_1: data.contactPhone,
							fill_emergency_name_2: data.contactLastName2 + " " + data.contactFirstName2,
							fill_emergency_phone_2: data.contactPhone2,
							fill_elec_num_1: data.meterNumber,
							fill_elec_reading_1: data.meterReading,
							fill_elec_num_2: data.meterNumber2,
							fill_elec_reading_2: data.meterReading2,
							fill_water_num_1: data.waterMeterNumber,
							fill_water_reading_1: data.waterMeterReading,
							fill_water_num_2: data.waterMeterNumber2,
							fill_water_reading_2: data.waterMeterReading2,
							fill_town_gas_num: data.gasMeterNumber,
							fill_town_gas_reading: data.gasMeterReading,
							fill_watercontact_title: '',
							fill_watercontact_chi_lastname: '',
							fill_watercontact_chi_firstname: '',
							fill_watercontact_eng_lastname: '',
							fill_watercontact_eng_firstname: '',
							fill_watercontact_phone: '',
							fill_watercontact_email: '',
							fill_watercontact_address: '',
							fill_electricitycontact_title: '',
							fill_electricitycontact_chi_lastname: '',
							fill_electricitycontact_chi_firstname: '',
							fill_electricitycontact_eng_lastname: '',
							fill_electricitycontact_eng_firstname: '',
							fill_electricitycontact_phone: '',
							fill_electricitycontact_email: '',
							fill_electricitycontact_address: '',
							fill_house: data.type,
							fill_staff: '',
							fill_handover_date: '',
							fill_owner_title_1: data.ownerTitle,
							fill_owner_title_2: data.ownerTitle2

						};
						
						if(data.electricalSupplyFormContact_1 == 'purchaser1') {
							pdfData.fill_electricitycontact_title = data.ownerTitle;
							pdfData.fill_electricitycontact_chi_lastname = data.ownerTitle2;
							pdfData.fill_electricitycontact_chi_firstname = data.firstName_zh;
							pdfData.fill_electricitycontact_eng_lastname = data.lastName_en;
							pdfData.fill_electricitycontact_eng_firstname = data.firstName_en;
							pdfData.fill_electricitycontact_phone =  data.phone;
							pdfData.fill_electricitycontact_email = data.email;
							pdfData.fill_electricitycontact_address = data.address;
						} else if (data.electricalSupplyFormContact_1 == 'purchaser2') {
							pdfData.fill_electricitycontact_title = data.lastName_zh;
							pdfData.fill_electricitycontact_chi_lastname = data.lastName2_zh;
							pdfData.fill_electricitycontact_chi_firstname = data.firstName2_zh;
							pdfData.fill_electricitycontact_eng_lastname = data.lastName2_en;
							pdfData.fill_electricitycontact_eng_firstname = data.firstName2_en;
							pdfData.fill_electricitycontact_phone =  data.phone2;
							pdfData.fill_electricitycontact_email = data.email2;
							pdfData.fill_electricitycontact_address = data.address2;
						}

						if(data.waterSupplyFormContact_1 == 'purchaser1') {
							pdfData.fill_watercontact_title = data.ownerTitle;
							pdfData.fill_watercontact_chi_lastname = data.lastName_zh;
							pdfData.fill_watercontact_chi_firstname = data.firstName_zh;
							pdfData.fill_watercontact_eng_lastname = data.lastName_en;
							pdfData.fill_watercontact_eng_firstname = data.firstName_en;
							pdfData.fill_watercontact_phone =  data.phone;
							pdfData.fill_watercontact_email = data.email;
							pdfData.fill_watercontact_address = data.address;
						} else if (data.waterSupplyFormContact_1 == 'purchaser2') {
							pdfData.fill_watercontact_title = data.ownerTitle2;
							pdfData.fill_watercontact_chi_lastname = data.lastName2_zh;
							pdfData.fill_watercontact_chi_firstname = data.firstName2_zh;
							pdfData.fill_watercontact_eng_lastname = data.lastName2_en;
							pdfData.fill_watercontact_eng_firstname = data.firstName2_en;
							pdfData.fill_watercontact_phone =  data.phone2;
							pdfData.fill_watercontact_email = data.email2;
							pdfData.fill_watercontact_address = data.address2;
						}


						let headers = _getHeaders();
						let pdfDict = {
							pdfPath: path,
							accessToken : headers["x-pm-access-token"],
							apiKey: headers["x-pm-api-key"],
							cancelButtonLbl: i18.Cancel,
							saveButtonLbl: i18.Save
						};
						PdfModule.openPdf(pdfDict, pdfData, this.handleSavedPdfData.bind(this));
					}}>  
						<View style={[styles.buttonContainer, {borderColor: '#FF8034', backgroundColor: '#FF8034'}]}>
							<Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.open}</Text>
						</View>
					</TouchableOpacity> 
				</View>   
			);

			if (this.props.formObj.dataPath && this.props.formObj.dataPath.length > 0) {
				// the pdf is signed and save
				buttons = (
					<View style={{flex: 2.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>   
						<TouchableOpacity onPress={this.deletePdfData}>  
							<View style={[styles.buttonContainer, {borderColor: '#CB7474', backgroundColor: '#CB7474'}]}>
								<Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.delete}</Text>
							</View>
						</TouchableOpacity> 
						<TouchableOpacity style={{marginLeft: 7}} onPress={() => {
							let path = this.props.formObj.dataPath;
							// the file should be with all data and signature and cannot be edited
							PdfModule.readOnlyPdf(path, i18.chooseBack);
						}}>  
							<View style={[styles.buttonContainer, {borderColor: '#707070', backgroundColor: '#707070'}]}>
								<Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.open}</Text>
							</View>
						</TouchableOpacity>
					</View> 
				);
			} else if (this.props.formObj.data != null) {
				// data is filled but able to edit
				buttons = (
					<View style={{flex: 2.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>   
						<TouchableOpacity onPress={this.deletePdfData}>  
							<View style={[styles.buttonContainer, {borderColor: '#CB7474', backgroundColor: '#CB7474'}]}>
								<Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.delete}</Text>
							</View>
						</TouchableOpacity> 
						<TouchableOpacity style={{marginLeft: 7}} onPress={() => {
							let path = this.props.formObj.originalPath;
							/* TODO -- demo purpose auto-fill */
							let data = this.props.source();
							let pdfData = {
								...this.props.formObj.data,
								fill_tower: data.tower,
								fill_floor: data.floor,
								fill_unit: data.flat,
								fill_owner_chi_lastname_1: data.lastName_zh,
								fill_owner_chi_firstname_1: data.firstName_zh,
								fill_owner_eng_lastname_1: data.lastName_en,
								fill_owner_eng_firstname_1: data.firstName_en,								
								fill_owner_email_1: data.email,
								fill_owner_phone_1: data.phone,
								fill_owner_address_1: data.address,
								fill_owner_chi_lastname_2: data.lastName2_zh,
								fill_owner_chi_firstname_2: data.firstName2_zh,
								fill_owner_eng_lastname_2: data.lastName2_en,
								fill_owner_eng_firstname_2: data.firstName2_en,
								fill_owner_email_2: data.email2,
								fill_owner_phone_2: data.phone2,
								fill_owner_address_2: data.address2,
								fill_emergency_name_1: data.contactLastName + " " + data.contactFirstName,
								fill_emergency_phone_1: data.contactPhone,
								fill_emergency_name_2: data.contactLastName2 + " " + data.contactFirstName2,
								fill_emergency_phone_2: data.contactPhone2,
								fill_elec_num_1: data.meterNumber,
								fill_elec_reading_1: data.meterReading,
								fill_elec_num_2: data.meterNumber2,
								fill_elec_reading_2: data.meterReading2,
								fill_water_num_1: data.waterMeterNumber,
								fill_water_reading_1: data.waterMeterReading,
								fill_water_num_2: data.waterMeterNumber2,
								fill_water_reading_2: data.waterMeterReading2,
								fill_town_gas_num: data.gasMeterNumber,
								fill_town_gas_reading: data.gasMeterReading,
								fill_house: data.type,
								fill_staff: '',
								fill_handover_date: '',
								fill_owner_title_1: data.ownerTitle,
								fill_owner_title_2: data.ownerTitle2,
								fill_watercontact_title: '',
								fill_watercontact_chi_lastname: '',
								fill_watercontact_chi_firstname: '',
								fill_watercontact_eng_lastname: '',
								fill_watercontact_eng_firstname: '',
								fill_watercontact_phone: '',
								fill_watercontact_email: '',
								fill_watercontact_address: '',
								fill_electricitycontact_title: '',
								fill_electricitycontact_chi_lastname: '',
								fill_electricitycontact_chi_firstname: '',
								fill_electricitycontact_eng_lastname: '',
								fill_electricitycontact_eng_firstname: '',
								fill_electricitycontact_phone: '',
								fill_electricitycontact_email: '',
								fill_electricitycontact_address: '',
							};

							if(data.electricalSupplyFormContact_1 == 'purchaser1') {
								pdfData.fill_electricitycontact_title = data.ownerTitle;
								pdfData.fill_electricitycontact_chi_lastname = data.ownerTitle2;
								pdfData.fill_electricitycontact_chi_firstname = data.firstName_zh;
								pdfData.fill_electricitycontact_eng_lastname = data.lastName_en;
								pdfData.fill_electricitycontact_eng_firstname = data.firstName_en;
								pdfData.fill_electricitycontact_phone =  data.phone;
								pdfData.fill_electricitycontact_email = data.email;
								pdfData.fill_electricitycontact_address = data.address;
							} else if (data.electricalSupplyFormContact_1 == 'purchaser2') {
								pdfData.fill_electricitycontact_title = data.lastName_zh;
								pdfData.fill_electricitycontact_chi_lastname = data.lastName2_zh;
								pdfData.fill_electricitycontact_chi_firstname = data.firstName2_zh;
								pdfData.fill_electricitycontact_eng_lastname = data.lastName2_en;
								pdfData.fill_electricitycontact_eng_firstname = data.firstName2_en;
								pdfData.fill_electricitycontact_phone =  data.phone2;
								pdfData.fill_electricitycontact_email = data.email2;
								pdfData.fill_electricitycontact_address = data.address2;
							}
	
							if(data.waterSupplyFormContact_1 == 'purchaser1') {
								pdfData.fill_watercontact_title = data.ownerTitle;
								pdfData.fill_watercontact_chi_lastname = data.lastName_zh;
								pdfData.fill_watercontact_chi_firstname = data.firstName_zh;
								pdfData.fill_watercontact_eng_lastname = data.lastName_en;
								pdfData.fill_watercontact_eng_firstname = data.firstName_en;
								pdfData.fill_watercontact_phone =  data.phone;
								pdfData.fill_watercontact_email = data.email;
								pdfData.fill_watercontact_address = data.address;
							} else if (data.waterSupplyFormContact_1 == 'purchaser2') {
								pdfData.fill_watercontact_title = data.ownerTitle2;
								pdfData.fill_watercontact_chi_lastname = data.lastName2_zh;
								pdfData.fill_watercontact_chi_firstname = data.firstName2_zh;
								pdfData.fill_watercontact_eng_lastname = data.lastName2_en;
								pdfData.fill_watercontact_eng_firstname = data.firstName2_en;
								pdfData.fill_watercontact_phone =  data.phone2;
								pdfData.fill_watercontact_email = data.email2;
								pdfData.fill_watercontact_address = data.address2;
							}
	



							let headers = _getHeaders();
							let pdfDict = {
								pdfPath: path,
								accessToken : headers["x-pm-access-token"],
								apiKey: headers["x-pm-api-key"]
							};
							PdfModule.openPdf(pdfDict, pdfData, this.handleSavedPdfData.bind(this));
						}}>  
							<View style={[styles.buttonContainer, {borderColor: '#707070', backgroundColor: '#707070'}]}>
								<Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.update}</Text>
							</View>
						</TouchableOpacity> 
						<TouchableOpacity style={{marginLeft: 7}} onPress={() => {
							let path = this.props.formObj.originalPath;
							let data = this.props.formObj.data;
							let pdfDict = {
								pdfPath: path,
								cancelButtonLbl: i18.Cancel,
								saveButtonLbl: i18.Save
							};
							PdfModule.signPdf(pdfDict, data, this.handleSavedPdfPath.bind(this));
						}}>  
							<View style={[styles.buttonContainer, {borderColor: '#707070', backgroundColor: '#707070'}]}>
								<Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.signOff}</Text>
							</View>
						</TouchableOpacity> 
					</View> 
				);
			}

			return (
				<View style={styles.container}>
					<View style={styles.pageButton}>
						<View style={{flex: 3}}>
							<Text style={{ fontWeight: 'bold' }}>{this.props.formObj.title}</Text>
						</View> 
	
						{ buttons }
					</View>
				</View>
			);
		} else {
			return (
				<View style={styles.container}>
					<View style={styles.pageButton}>
						<View style={{flex: 3}}>
							<Text style={{ fontWeight: 'bold' }}>{this.props.formObj.title}</Text>
						</View>  
					</View>
				</View>
			);
		}
	}
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
        minHeight: 70
	},
    pageButton: {
        width: '97%',
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Theme.item.bg,
        padding: 9,
        marginHorizontal: 5,
        marginTop: 5,
        minHeight: 43,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,
    },
    buttonContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 27,
        minWidth: 60,
        borderWidth: 1,
        borderRadius: 4,
    },
    buttonText: {
        textAlign: 'center',
        minWidth: 55,
        fontSize: 14,
        fontWeight: 'bold',
    },
});

const mapStateToProps = (state, ownProps) => {
	return {
		projectDetail: state.project.projectDetail,
		language: state.setting.language,
		timestamp: new Date(),
		modifyCheckListId: state.checklists.modifyCheckList
	}
}

import { CheckLists as checkListsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';

let checkListsProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {...checkListsProps, ...projectProps};
})(FormItem);
