import React, { Component } from 'react';
import {
    StyleSheet,
    Text,
    View,
    Image,
    TouchableOpacity,
    FlatList,
    Dimensions,
    ScrollView, Alert
} from 'react-native';
import { connect } from 'react-redux'

import { taskList as Theme, nav } from 'Theme';
import { Icon, Nav, DropDownList } from 'Item';
import i18 from 'i18';
import moment from 'moment';

import TaskListItem from './TaskListItem';
import ActionSheet from 'react-native-actionsheet';


const CANCEL_INDEX = 0;

export default class TaskList extends Component {

	constructor(props) {
		super(props);

		this.state = {
			dropDownList: {},
			dropDownData: [],
			firstOptions: [],
			secondOptions: [],
			firstOption: "",
		};
	}

	componentWillReceiveProps(nextProps) {
		let { props } = this;
		let { language, checklists } = props;
		// console.log("componentWillReceiveProps this props", props);
		// console.log("componentWillReceiveProps nextProps", nextProps);
		// 11/10: WARNING & TODO -- do the checking to prevent too much loading, have to double check the behaviour is correct or not
		if((nextProps.checklists && nextProps.checklists.length != props.checklists.length) || nextProps.language != language){
			let checklists = nextProps.checklists;
			let language = nextProps.language;
			
			// update the count in the tab using the number of checklist
			props.updateChecklistsCount(checklists.length);

			let dropDownData = props.getTaskListDropDownData(this.state.dropDownList, checklists, language);
			this.setState({dropDownData});
		}

		if(nextProps.language) {
			this.firstOption();
		}
	}
	
	componentWillMount() {
		let { props } = this;
		props.fetchLocalChecklists();
		this.firstOption();
		console.log("componentWillMount");
		// props.fetchLocalAdhocOptions();
	}

	firstOption() {
		let options = [];
		options.push(i18.Cancel);
		options.push(i18.CheckTaskList)
		options.push(i18.OtherList)
		this.setState({firstOptions: options});
	}

	secondOption() {
		let options = [];
		options.push(i18.Cancel);
		options.push(i18.Internal)
		options.push(i18.Customer)
		this.setState({secondOptions: options});
	}
	
	onAdd(c) {
        if (c == 'none') {
            Alert.alert(
                i18.noInternetConnection,
                '',
                [
                    {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                ]
            )
            return
        }
		this.showActionSheet();
	}

	showActionSheet() {
		this.ActionSheet.show()
	}

	onDownload(c) {
		if (c == 'none') {
            Alert.alert(
                i18.noInternetConnection,
                '',
                [
                    {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                ]
            )
			return
		}
		this.props.navigation.navigate("TaskDownload");
	}

	renderItem = (item) => {

		let { props } = this;
		let { unit_address } = props;

		let itemDetail = item.item;
		let total = 0, unfill = 0;
		if(!itemDetail.source){
			itemDetail.questions.forEach((section)=>{
				let newSection = {...section};
				total += newSection.data.length;
				newSection.data = newSection.data.filter(x => x.status == 'Unfill');
				unfill += newSection.data.length;
			});
		}else {
			total += itemDetail.questions.length;
		}

		
		let translatedUnitAddress;
		translatedUnitAddress = unit_address[itemDetail.type] || "----";
				
		if(itemDetail.tower) {
			translatedUnitAddress = translatedUnitAddress.replace("{tower}", itemDetail.tower);
		}

		if(itemDetail.flat) {
			translatedUnitAddress = translatedUnitAddress.replace("{flat}", itemDetail.flat);
		}

		if(itemDetail.floor) {
			translatedUnitAddress = translatedUnitAddress.replace("{floor}", itemDetail.floor);
		}

		var date = itemDetail._created_at;
		date = moment.utc(date).format('YYYY-MM-DD HH:mm');
		date = moment.utc(date).toDate();
		date = moment(date).local().format('YYYY-MM-DD HH:mm');


		return (
			<TaskListItem 
				itemDetail={itemDetail}
				total={total}
				unfill={unfill}
				date={date}
				translatedUnitAddress={translatedUnitAddress}
				onPress={()=>{
					props.getUnitFloorPlan(itemDetail.unit);
					if(itemDetail.source == 'customer' || itemDetail.source == 'adhoc'){
						props.modifyAdhocCheckList(itemDetail._id);
						props.navigation.navigate("AdhocList")
					} else {
						props.modifyCheckList(itemDetail._id);
						props.navigation.navigate("CheckList", {itemDetail: itemDetail, translatedUnitAddress: translatedUnitAddress})
					}
				}}  />
			);
	}

	onShowDropDownList(data) {
		this.setState({
			dropDownList: data,
		});
	}

	onFilterList(title, list, filter) {
		let { props } = this;
		let { checklists, language } = props;

		var newDropDownData = this.state.dropDownData;
		let findIndexOfTitle = 0;

		for(var key in newDropDownData)
		{
			if(newDropDownData[key].title==title){
				findIndexOfTitle = key;
			}
		}
		let dropDownList = this.state.dropDownList;
		newDropDownData.forEach((item, i) => {
			if(item.title == title) {
				let array;
				let compareValueExist = false;
				// filter.forEach((a)=>{
				// 	if(item.list.includes(a)){
				// 		compareValueExist = true;
				// 	}
				// });

				compareValueExist = filter.some((a) => {
					return item.list.includes(a);
				});

				// console.log("compareValueExist", compareValueExist);

				if(!compareValueExist){
					array = [];
				}else {
					array = filter;
				}
				
				if(!item.filter.includes(list)) {
					array.push(list);
				}else {
					var index = array.indexOf(list);
					array.splice(index, 1);
				}
				item.filter = array;
				dropDownList.filter = array;
			}else {
				if( i > findIndexOfTitle) {
					item.filter = [];
				}
			}
		});


		let dropDownData = props.getTaskListDropDownData(newDropDownData, checklists, language);

		this.setState({
			dropDownData: dropDownData,
			dropDownList: dropDownList
		});

	}

	cleanDropDownList(title) {
		let { props } = this;
		let { checklists, language } = props;

		for(var key in this.state.dropDownData)
		{
			if(this.state.dropDownData[key].title==title){
				findIndexOfTitle = key;
			}
		}

		let newDropDownData = this.state.dropDownData.map((data, i)=>{
			if(data.title == title){
				data.filter = [];
			}else {
				if( i > findIndexOfTitle) {
					data.filter = [];
				}
			}
			return data;
		});

		let dropDownData = props.getTaskListDropDownData(newDropDownData, checklists, language);

		let dropDownList = this.state.dropDownList;
		
		if(dropDownList.title == title) {
			dropDownList.filter = [];

		}

		this.setState({
			dropDownData: dropDownData,
			dropDownList: dropDownList,
		});
	}


	closeDropDownList() {
		this.setState({
			dropDownList: {},
		});
	}


	handlePress(i) {
		if(i != 0){
			if(this.state.secondOptions.length > 0) {
				this.props.resetChecklist();
				this.props.navigation.navigate("AddHocForm", {firstOption: this.state.firstOption, secondOption: this.state.secondOptions[i]})
				this.setState({
					secondOptions: []
				});
			} else {
				this.setState({firstOption: this.state.firstOptions[i]});
				this.secondOption();
				this.showActionSheet();
			}
		}

		if(i == 0 && this.state.firstOption != "") {
			this.setState({
				secondOptions: []
			});
		}
	}

	render() {

		let { props } = this;
		let { checklists, connectionInfo} = props;

		// console.log("first page", checklists);
		
		let showDropDownList = Object.keys(this.state.dropDownList).length > 0  ? (
			<DropDownList 
				closeDropDownList={()=> this.closeDropDownList()}
				dropDownData={this.state.dropDownData}
				dropDownList={this.state.dropDownList}
				cleanDropDownList={(title) => this.cleanDropDownList(title)}
				onFilterList={(title, list, filter)=> this.onFilterList(title, list, filter)}
				/>
		) : null;

		let filteredCheckList;
		let dropDownData = this.state.dropDownData;
		let filter = {};

		if(dropDownData.length > 0){
			if(dropDownData[0].filter.length > 0) {
				filter.name = dropDownData[0].filter;
			}
			if(dropDownData[1].filter.length > 0) {
				filter.tower = dropDownData[1].filter;
			}
			if(dropDownData[2].filter.length > 0) {
				filter.floor = dropDownData[2].filter;
			}
			if(dropDownData[3].filter.length > 0) {
				filter.flat = dropDownData[3].filter;
			}


			filteredCheckList = checklists.filter((item) => {
				for(let key in filter) {
					if(item[key] === undefined || !filter[key].includes(item[key])){
						return false;
					}
				}
				return true;
			});
            filteredCheckList.map(data => {
                data.sortKey = (data.tower + ' ' + data.floor + ' ' + data.flat).toLowerCase();
            });
            filteredCheckList = filteredCheckList.sort(function(a, b) {
                return naturalCompare(a.sortKey, b.sortKey);
            });
		}


		let showTask = checklists.length > 0 ? (
			<FlatList style={styles.list} data={filteredCheckList} renderItem={this.renderItem} />
		) : (
			<View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
				<Icon style={{
					fontSize: 100,
					backgroundColor:'transparent',
					color: '#A8A8A8'}} icon={'no-report'}/>
				<Text style={{marginTop: 10, textAlign: 'center', width: 148, fontSize: 16, color: '#B9B9B9', lineHeight: 22, fontWeight: '600'}}>{i18.NoTask}</Text>
			</View>
		);


		return (
			<View style={styles.container}>
				<Nav onLeftPress={() => {this.onAdd(connectionInfo)}}
					onRightPress={() => {this.onDownload(connectionInfo)}}
					left="ADD" 
					right="DOWNLOAD" 
					showDropDown={(filteredCheckList && filteredCheckList.length > 0)}
					onShowDropDownList={(val)=>this.onShowDropDownList(val)} 
					dropDownData={this.state.dropDownData}>
					{i18.TaskList}
				</Nav>
				{showTask}
				{showDropDownList}
				<ActionSheet
					ref={o => this.ActionSheet = o}
					title={this.state.secondOptions.length > 0 ? i18.OtherListType : i18.AddNewChecklistForm}
					options={this.state.secondOptions.length > 0 ? this.state.secondOptions : this.state.firstOptions}
					cancelButtonIndex={CANCEL_INDEX}
					onPress={(i) => this.handlePress(i)}
       			/>
			</View>
		);
	}
}

const itemStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: Theme.item.bg,
		justifyContent:'space-between',
		padding: 9,
		marginHorizontal: 5,
		marginTop: 8,
		shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,

	},
	icon: {
		color: '#484848',
        width: 35,
		backgroundColor:'transparent',
		textAlign:'center',
		marginBottom: 5,
	},
	detail: {
		flex:1,
		justifyContent:'space-between',	
	},
	address: {
		flex:1,
		flexDirection: 'row',
		alignItems:'center',		
	},
	info: {
		flex:1,		
		flexDirection: 'row',
		alignItems:'center',
	},
	text: {
		paddingHorizontal: 3,
		color: Theme.item.text,
		fontSize:14,
	},
	bubble: {
		marginHorizontal: 2,
		paddingHorizontal: 6,
		paddingVertical: 3,
		borderRadius: 10,
		alignItems: 'center',
		justifyContent: 'center'
	},
	bubbleText: {
		fontSize:12,
		color: Theme.bubble.text,
	},
	draft: {
		backgroundColor: Theme.bubble.draft,
	},
	extraList: {
		backgroundColor: Theme.bubble.extra
	},
	dateContainer: {
		alignItems: 'flex-end',
		justifyContent: 'space-around',
	},
	date: {	
		fontSize:14,
		color: Theme.item.date,
	},
	state:{
		fontSize:14,
		color: Theme.item.state,
	},
});

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	list: {
		flex: 1,
		width: '100%'
	},
});

const mapStateToProps = (state, ownProps) => {
	let unit_address;
	if(state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0){
		if(state.setting.language == 'en') {
			unit_address = state.project.projectDetail.unit_address_en;
		}else if(state.setting.language == 'zh_hk') {
			unit_address = state.project.projectDetail.unit_address_zh;
		}else {
			unit_address = state.project.projectDetail.unit_address_cn;
		}
	}

	// console.log(state.checklists.downloadedChecklists)
	let filteredDownloadChecklist = state.checklists.downloadedChecklists.filter((item)=> {
		return item.downloadFromChecklist || item.checklist_resp != null || item.source == 'adhoc'
	})

	// console.log(filteredDownloadChecklist)
	// console.log(state.auth.user._id)
	
	return {
		projectDetail: state.project.projectDetail,
		language: state.setting.language,
        connectionInfo: state.setting.connectionInfo,
		checklists: filteredDownloadChecklist,
		unit_address: unit_address,	
	}
}

import { CheckLists as checkListsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';
import naturalCompare from "string-natural-compare";

let checkListsProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {...checkListsProps, ...projectProps};
})(TaskList);
