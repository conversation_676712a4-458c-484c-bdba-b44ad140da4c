import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Animated,
	ScrollView,
	TextInput,
	Dimensions,
	Keyboard,
	Platform,
	NetInfo,
	Alert,
} from 'react-native';
import { connect } from 'react-redux'

import { checkList as Theme} from 'Theme';
import { Icon, Nav, DropDownList, Button } from 'Item';
import i18 from 'i18';
import moment from 'moment';

import CachedImage  from 'react-native-cached-image';
const {
    ImageCacheProvider
} = CachedImage;


import { NavigationActions } from 'react-navigation'

const resetMainAction = NavigationActions.reset({
	index: 0,
	actions: [
	  NavigationActions.navigate({ routeName: 'Main'})
	]
})

export default class AdhocList extends Component {

	constructor(props) {
		super(props);
		this.state = {
            showSelectionButtons: false,
			selectedData:[],
		};
    }
    
    componentWillReceiveProps(nextProps) {
		let { props } = this;
		let { checklist } = props;

		if(!nextProps.checklist.hasOwnProperty('questions')){
			this.props.navigation.goBack();
		}
	}

    renderItem = (item, idx) => {

        let { props } = this;
		let { checklist, language } = props;

        const styles = itemStyles;

        // console.log(item)

        if(this.state.showSelectionButtons) {
			additionalStyleForChangeStatus = -50;
		} else {
			additionalStyleForChangeStatus = 0;
		}

        let showCheckBox;
		let selected = false;
		if (this.state.selectedData.indexOf(item._id) > -1) {
			selected = true;
		}

        if(this.state.showSelectionButtons){
			showCheckBox = (
				<TouchableOpacity style={{
					left: 0, top: 0,  position:'absolute', height: '100%', alignItems: 'center', 
					justifyContent: 'center'
				}}onPress={() => {
					
					let selectedData = this.state.selectedData;

					if (selectedData.indexOf(item._id) > -1) {
						var index = selectedData.indexOf(item._id);
						selectedData.splice(index, 1);
					}else {
						selectedData.push(item._id);
					}

					this.setState({
						selectedData: selectedData,
					});

					let sectionData = checklist.questions.map((section, i) => {
						if(i == 0) {
							section.fake = section.fake ? false : true;
						}
						return section;   
					});
                    
                    props.fakeUpdateAdhocData(sectionData);
	
                }}>
					<View style={{ alignItems: 'center', justifyContent: 'center'}}>
						<Icon icon={selected ? 'checked' : 'unchecked'} style={styles.checkIcon} />
					</View>	
				</TouchableOpacity>
			)
		}else {
			showCheckBox = <View/>;
		}

                
        return (
            <View key={idx}>
                <TouchableOpacity style={[styles.container, , {right: additionalStyleForChangeStatus }]} onPress={() => {
                        this.props.navigation.navigate("AddHocForm", {modifyItem: true, question: item})
                    }}>
                    <View style={styles.detail}>
                        <View style={styles.address}>
                            {
                                (item.location) ? (
                                    <Text style={[styles.text, {fontWeight: 'bold'}]}>{item.location}</Text>
                                ) : null
                            }
                            {
                                (item.level1) ? (
                                <Text style={[styles.text, {marginLeft: '3.5%'}]}>{i18.level1 + " : " + item.level1}</Text>
                                ) : null
                            }
                            {
                                (item.level2) ? (
                                    <Text style={[styles.text, {marginLeft: '3.5%'}]}>{i18.level2 + " : " + item.level2}</Text>
                                ) : null
                            }
                            {
                                (item.critical) ? (
                                    <View style={[styles.bubble, styles.extraList, {marginLeft: '3.5%'}]}>
                                        <Text style={styles.bubbleText}>{i18.importantItem}</Text>
                                    </View>
                                ) : null
                            }
                        </View>

                        <View style={styles.info}>
                            <Text style={[styles.text, {color: '#C7C7C7'}]}>{i18.remarks + " : " + item.description}</Text>
                        </View>
                    </View>
                </TouchableOpacity>
                {showCheckBox}
            </View>
        );
    }

    addNewQuestion () {
        let { props } = this;
        let { checklist } = props;
        this.props.navigation.navigate("AddHocForm", {addNew: true})
    }

    updateSelectionButtons(val) {
		this.setState({
			showSelectionButtons: val,
			selectedData: [],
		});
	}

	render() {
        let { props } = this;
        let { unit_address, checklist } = props;   
        // console.log("checklist", checklist) 
        
        let translatedUnitAddress;
        translatedUnitAddress = unit_address[checklist.type] || "----";
                
        if(checklist.tower) {
            translatedUnitAddress = translatedUnitAddress.replace("{tower}", checklist.tower);
        }

        if(checklist.flat) {
            translatedUnitAddress = translatedUnitAddress.replace("{flat}", checklist.flat);
        }

        if(checklist.floor) {
            translatedUnitAddress = translatedUnitAddress.replace("{floor}", checklist.floor);
        }
        
        let deleteButton = this.state.showSelectionButtons ? (
            <View style={[styles.center, {flex: 1}]}>
                <TouchableOpacity onPress={() => {
                    this.setState({selectedData: []});
                    let sectionData = checklist.questions.map((section, i) => {
						if(i == 0) {
							section.fake = section.fake ? false : true;
						}
						return section;   
					});
                    props.deleteAdhocData(checklist._id, this.state.selectedData);
                }}>
                <View style={[styles.bottomButtonContainer, {borderColor: '#E1E1E1'}]}>
                    <Text style={[styles.bottomButtonText, {color: '#FF8136'}]}>{i18.delete}</Text>
                </View>
                </TouchableOpacity>
            </View>    
        ) : <View style={{flex: 1}}/>;

        let submitButton = !this.state.showSelectionButtons ? (
            <View style={[styles.center, {flex: 1}]}>
                <TouchableOpacity onPress={() => {
                    Alert.alert(
                    i18.AskForSubmitCheckList,
                    '',
                    [
                        {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                        {text: i18.OK, onPress: () => {
                            props.pendingCheckLists(checklist);
                            props.clearChecklists();
                            this.props.navigation.dispatch(resetMainAction);
                        }},
                    ]
                    )
                }}>  
                    <View style={[styles.bottomButtonContainer, {borderColor: '#FF8034', backgroundColor: '#FF8034'}]}>
                    <Text style={[styles.bottomButtonText, {color: '#FFFFFF'}]}>{i18.Submit}</Text>
                </View>
                </TouchableOpacity> 
            </View>          
        ) : <View style={{flex: 1}}/>;

		return (
			<View style={styles.container}>	
				<Nav 
					onRightPress={()=>this.onSubmit()} 
					right="ADD"
                    onRightPress={()=>this.addNewQuestion()} 
					onLeftPress={()=>{
                        props.clearChecklists();
                        this.props.navigation.dispatch(resetMainAction);
                    }}
					left="BACK">
                    {translatedUnitAddress}
				</Nav>

                <ScrollView showsVerticalScrollIndicator={false}>
                    {checklist.hasOwnProperty('questions') ? (checklist.questions.map(this.renderItem)) : null}
                </ScrollView>
                <View style={[styles.bottomContainer, {justifyContent: 'space-between'}]}>
                    {deleteButton}
                    <View style={[styles.center, {flex: 1}]}>
                        <TouchableOpacity onPress={() => this.updateSelectionButtons(!this.state.showSelectionButtons)}>
                        <View style={[styles.bottomButtonContainer, {borderColor: Theme.list.popUp.button}]}>
                        <Text style={[styles.bottomButtonText]}>{!this.state.showSelectionButtons ? i18.choose : i18.Cancel}</Text>
                        </View>
                        </TouchableOpacity>
                    </View>     
                    {submitButton}    
                </View> 
			</View>
		);
	}
}

const itemStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: Theme.item.bg,
		justifyContent:'space-between',
		padding: 9,
		marginHorizontal: 5,
		marginTop: 8,
		shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,

	},
	detail: {
		flex:1,
		justifyContent:'space-between',	
	},
	address: {
		flex:1,
		flexDirection: 'row',
        alignItems:'center',
        minHeight: 28,		
	},
	info: {
		flex:1,		
		flexDirection: 'row',
        alignItems:'center',
        minHeight: 28,
	},
	text: {
		paddingHorizontal: 3,
		color: Theme.item.text,
		fontSize:14,
    },
    bubble: {
		marginHorizontal: 2,
		paddingHorizontal: 6,
		paddingVertical: 3,
		borderRadius: 10,
		alignItems: 'center',
		justifyContent: 'center'
	},
	bubbleText: {
        fontSize:12,
        color: '#FFFFFF',
	},
	extraList: {
        backgroundColor: '#CB7474',
    },
    checkIcon: {
        color: '#FF8238',
        width: 52,
		fontSize: 26,
		backgroundColor:'transparent',
		textAlign:'center',
    },
});

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
    },
    center: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    bottomContainer: {
        padding: 5,  
        minHeight: 44,
        backgroundColor: '#FFF',
        borderTopWidth: 1,
        borderColor: '#AEAEAE' ,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    bottomButtonContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 31,
        minWidth: 95,
        borderWidth: 1,
        borderRadius: 4,
    },
    bottomButtonText: {
        textAlign: 'center',
        minWidth: 90,
        fontSize: 16,
        fontWeight: 'bold',
    },
});

const mapStateToProps = (state, ownProps) => {
	let unit_address;
	if(state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0){
		if(state.setting.language == 'en') {
			unit_address = state.project.projectDetail.unit_address_en;
		}else if(state.setting.language == 'zh_hk') {
			unit_address = state.project.projectDetail.unit_address_zh;
		}else {
			unit_address = state.project.projectDetail.unit_address_cn;
		}
    }
    
	return {
		projectDetail: state.project.projectDetail,
        user: state.auth.user,
        unit_address: unit_address,
        language: state.setting.language,
        checklist: state.checklists.checklist,
	}
}


import { CheckLists as checkListsMapDispatchToProps, Project as projectMapDispatchToProps, Others as  othersMapDispatchToProps} from 'Controller';

let checkListsProps;
let projectProps;
let othersProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
    checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
    projectProps = projectMapDispatchToProps(dispatch, ownProps)
    othersProps = othersMapDispatchToProps(dispatch, ownProps)
	return {...checkListsProps, ...projectProps, ...othersProps};
})(AdhocList);
