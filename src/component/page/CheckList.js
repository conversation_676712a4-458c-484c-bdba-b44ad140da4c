import React, { Component } from 'react';
import {
	StyleSheet,
	View,
	Image,
	TouchableOpacity,
	ListView,
	SectionList,
	Dimensions,
	ScrollView,
	Animated,
	Alert,
} from 'react-native';
import { connect } from 'react-redux'
import ScrollableTabView, { DefaultTabBar, } from 'react-native-scrollable-tab-view';
import Text from 'react-native-text';
import * as Animatable from 'react-native-animatable';

import { Icon, Button, Nav, DropDownList } from 'Item';
import { checkList as Theme } from 'Theme';
import i18 from 'i18';
import moment from 'moment';

const Actions = require('Redux').Action.root;

class TabBar extends Component {

	renderTab = (item, page) => {
		const styles = tabStyles;
		
		let { activeTab, tabs, goToPage, showChangeStatus } = this.props;		
		var isTabActive = activeTab === page;

		var labelColor;

		if(item.label == 'Unfill') {
			labelColor = Theme.header.unfill;
		}else if(item.label == 'Satisfy') {
			labelColor = Theme.header.satisfy;
		}else {
			labelColor = Theme.header.followUp;
		}

		return (
			<TouchableOpacity key={item.label} onPress={() => {
					if(!showChangeStatus){
						goToPage(page);
					}
				}} style={styles.tab}>
				<Text style={[styles.text, { color: labelColor}]}>
					{ i18[item.label] }
				</Text>
				<Text style={[styles.text, { color: labelColor }]}>
					{ `${item.unfill}/${item.total}` }
				</Text>
			</TouchableOpacity>
		);
	}

	render() {
		const styles = tabStyles;
		
		let { containerWidth, tabs, scrollValue } = this.props;
		let left = scrollValue.interpolate({ inputRange: [0, 1], outputRange: [0, containerWidth / tabs.length ] });

		return (
			<View style={styles.container}>
				<View style={styles.tabs}>
					{ tabs.map(this.renderTab) }
				</View>
				<Animated.View style={[styles.line, { width:containerWidth / tabs.length, left }]} />
			</View>
		);
	}
}

const tabStyles = StyleSheet.create({
	container:{
		height: 56,
		borderBottomWidth:1,
		borderColor:'#888',
	},
	tab: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
	},
	tabs: {
		flex:1,
		flexDirection: 'row',
		justifyContent: 'center',
		backgroundColor:'#fff',
	},
	text:{
		fontWeight: '500', 
		fontSize: 14
	},
	line: {
		position: 'absolute',
		height: 4,
		backgroundColor: '#000',
		bottom: 0,
	}
});

export default class CheckList extends Component {
	constructor(props) {
		super(props);

		this.state = {
			dropDownList: {},
			dropDownData: [],
			showChangeStatus: false,
			selectedData:[],
			unitFirstRow: "",
			unitSecondRow: "",
			unitDropDownFocus: 0,
			changeFilterData: false,
			isUnitEachRecord: this.props.navigation.state.params.isUnitEachRecord ? this.props.navigation.state.params.isUnitEachRecord : false,
		};
	}
	
	getDisplayName(userId) {
		if (userId in this.props.users) {
			return this.props.users[userId];
		}

		return userId;
	}

	componentWillMount() {
		let { props } = this;
		let { language, sectionData, unitCheckList, modifyCheckListId } = props;

		let dropDownData = props.getCheckListDropDownData(this.state.dropDownList, sectionData, language);

		this.setState({dropDownData});

		if(this.props.navigation.state.params.isUnit) {
			unitCheckList.forEach((item) => {
				if(item._id == modifyCheckListId)	{
					// console.log("item._id", item._id)
					if(!item.resp_items){
						this.setState({
							unitFirstRow: i18.consolidatedView,
							unitSecondRow: "",
							unitDropDownFocus: item._id
						});
					}else {
						let date = item._updated_at;
						date = moment.utc(date).format('YYYY-MM-DD');
						let assignee = (item.assignee) ? item.assignee : item._created_by;
						this.setState({
							unitFirstRow: date,
							unitSecondRow: this.getDisplayName(assignee),
							unitDropDownFocus: item._id
						});
					}
				}
			});
		}

		this.changeSectionDataFormat();
	}

	componentWillReceiveProps(nextProps) {
		let { props } = this;
		let { sectionData, language, filteredChecklist, checklist, checkUpdatedinDetail } = props;
		if (nextProps.sectionData && this.state.changeFilterData) {
			let dropDownData = props.getCheckListDropDownData({}, nextProps.sectionData, language);
			this.setState({
				dropDownData: dropDownData,
				changeFilterData: false,
			});
		}

		// if(nextProps.filterCheckList && nextProps.checkUpdatedinDetail != checkUpdatedinDetail ){
		// 	let dropDownData = this.state.dropDownData.map((item)=>{
		// 		item.filter = [];
		// 	})

		// 	this.setState({
		// 		dropDownData: dropDownData
		// 	});
		// }
		
	}

	changeSectionDataFormat() {
		let { props } = this;
		let { checklist } = props;
		let itemDetail = checklist.questions;
		
		// let unfillData = this.separateForDifferenceLabel(itemDetail, 'Unfill', true);
		// let satisfyData = this.separateForDifferenceLabel(itemDetail, 'Satisfy', true);
		// let followUpData = this.separateForDifferenceLabel(itemDetail, 'FollowUp', true);

		this.separateForDifferenceLabel(itemDetail, '', true);
	}

	separateForDifferenceLabel(sectionData, label, firstRender){
		let { props } = this;
		let { labelData } = props;
		
		let total = 0;
		// let lengthForEachSection = 0;
		let lengthForUnfill = 0;
		let lengthForSatisfy = 0;
		let lengthForFollowUp = 0;

		let unfillSection = [];
		let satisfySection = [];
		let followUpSection = [];
		
		// sectionData = sectionData.map( section => {
		// 	let newSection = {...section};
		// 	total += newSection.data.length;
		// 	newSection.data = newSection.data.filter(item => item.status == label).map((y)=>{
		// 		return y;
		// 	});;
		// 	lengthForEachSection += newSection.data.length;
		// 	return newSection;
		// }).filter( section => section.data.length > 0);

		sectionData.forEach((section) => {
			total += section.data.length;
			let newUnfill = {...section, data: []};
			let newSatisfy = {...section, data: []};
			let newFollowUp = {...section, data: []};
			section.data.forEach((item) => {
				if (item.status == 'Unfill') {
					lengthForUnfill++; 
					if (!firstRender) {
						newUnfill.data.push(item);
					}
				} else if (item.status == 'Satisfy') { 
					lengthForSatisfy++;
					if (!firstRender) {
						newSatisfy.data.push(item);
					}
				} else if (item.status == 'FollowUp') { 
					lengthForFollowUp++;
					if (!firstRender) {
						newFollowUp.data.push(item);
					}
				}
			});
			if (!firstRender) {
				unfillSection.push(newUnfill);
				satisfySection.push(newSatisfy);
				followUpSection.push(newFollowUp);
			}
		});

		if(firstRender){
			labelData.total = total;
			// if(label == 'Unfill'){
			// 	labelData.Unfill = lengthForEachSection;
			// }
			// if(label == 'Satisfy'){
			// 	labelData.Satisfy = lengthForEachSection;
			// }
			// if(label == 'FollowUp'){
			// 	labelData.FollowUp = lengthForFollowUp;
			// }
			labelData.Unfill = lengthForUnfill;
			labelData.Satisfy = lengthForSatisfy;
			labelData.FollowUp = lengthForFollowUp;
			props.updateLabelData(labelData);
		}	

		return {
			unfillData: unfillSection,
			satisfyData: satisfySection,
			followUpData: followUpSection
		};
	}

	onBack = ()=>{
		this.props.navigation.goBack();
	}

	clickItem = (item) => {
		let { props } = this;
		let { checklist, filteredChecklistTotal, filteredChecklist } = props;
		let translatedUnitAddress = this.props.navigation.state.params.translatedUnitAddress;
		let total = 0;

		// checklist.questions.map( section => {
		// 	let newSection = {...section};
		// 	total += newSection.data.length;
		// 	newSection.data = newSection.data.filter(x => x.status == item.status);
		// 	return newSection;
		// }).filter( section => section.data.length > 0);

		let isUnit = false;
		if(this.props.navigation.state.params.isUnit) {
			isUnit = this.props.navigation.state.params.isUnit;
		}

		let question;
		total = filteredChecklistTotal;
		filteredChecklist.questions.find((section)=> {
			let newSection = {...section};
			newSection.data = newSection.data.map((eachItem)=>{
				if(eachItem._id == item._id){
					question = eachItem;
					return true;
				}
				return false;
			})
			return false;   
		})
		// console.log("question", question)

		// if(question == undefined) {
		// 	total = props.labelData.total
		// 	checklist.questions.find((section)=> {
		// 		let newSection = {...section};
		// 		newSection.data = newSection.data.map((eachItem)=>{
		// 			if(eachItem._id == item._id){
		// 				question = eachItem;
		// 				return true;
		// 			}
		// 			return false;
		// 		})
		// 		return false;   
		// 	})

		// }

		// console.log(filteredChecklist)
		this.props.navigation.navigate("CheckListItem", {
			question: question,
			translatedUnitAddress: translatedUnitAddress,
			label: item.status,
			total: filteredChecklistTotal,
			isUnit: isUnit,
		})		
	}

	renderHeader = ({ section }) => {
		const styles = headerStyles;

		if (!section || !section.location)
			return <View style={styles.empty}/>;

		return (
			<View style={styles.container}>
				<Text style={styles.text}>{ section.locationID + ". "+ (section && section.location || "")}</Text>
			</View>
		)
	}

	renderItem = ({ item }) => {

		const styles = itemStyles;
		let containerStyle = [styles.container];
		let statusStyle = [styles.statusContainer];
		let { props } = this;
		let { language, unfillData } = props;
		
		let translatedStatus;
		translatedStatus = i18.Unfill;

		if (item.status === 'Satisfy'){
			translatedStatus = i18.Satisfy;
			containerStyle.push({borderBottomColor: Theme.item.status.satisfy});
			statusStyle.push({backgroundColor:Theme.item.status.satisfy});
		}
		else if (item.status === 'FollowUp'){
			translatedStatus = i18.FollowUp;
			containerStyle.push({borderBottomColor: Theme.item.status.followUp});
			statusStyle.push({backgroundColor:Theme.item.status.followUp});
		}

		let additionalStyleForChangeStatus;

		if(item.status === 'Unfill' && this.state.showChangeStatus) {
			additionalStyleForChangeStatus = -50;
		} else {
			additionalStyleForChangeStatus = 0;
		}

		let showCheckBox;
		let selected = false;
		if (this.state.selectedData.indexOf(item._id) > -1) {
			selected = true;
		}

		if(this.state.showChangeStatus && item.status === 'Unfill'){
			showCheckBox = (
				<TouchableOpacity style={{
					left: 0, top: 0,  position:'absolute', height: '100%', alignItems: 'center', 
					justifyContent: 'center'
				}}onPress={() => {
					
					let selectedData = this.state.selectedData;

					if (selectedData.indexOf(item._id) > -1) {
						var index = selectedData.indexOf(item._id);
						selectedData.splice(index, 1);
					}else {
						selectedData.push(item._id);
					}

					// let sectionData = unfillData.map((section) => {
					// 	let newSection = {...section};
					// 	newSection.data = newSection.data.map((item, i)=>{
					// 		let newItem = {...item};
					// 		if(i == 0) {
					// 			newItem.selected = this.state.showChangeStatus ? false : true;
					// 		}
					// 		return newItem;
					// 	})
					// 	return newSection;   
					// });

					let sectionData = unfillData.slice();
					for (var i = 0; i < sectionData.length; i++) {
						var section = sectionData[i];
						
						if(section.data.length > 0){
							section.data[0].selected = this.state.showChangeStatus ? false : true;
						}

					}
                    
					props.fakeUpdateUnfillData(sectionData);
					this.setState({
						selectedData: selectedData,
					});
	
                }}>
					<View style={{ alignItems: 'center', justifyContent: 'center'}}>
						<Icon icon={selected ? 'checked' : 'unchecked'} style={styles.checkIcon} />
					</View>	
				</TouchableOpacity>
			)
		}else {
			showCheckBox = <View/>;
		}

		return (
			<View>
				<TouchableOpacity  disabled={(this.state.showChangeStatus && item.status === 'Unfill')  ? true : false} key={item._id} style={[containerStyle, {right: additionalStyleForChangeStatus }]} onPress={()=>this.clickItem(item)}>
					
					<Text numberOfLines={2} style={styles.question}>{item.description}</Text>

					<Text style={styles.level}>{item.level1 ? item.level1 : ""}</Text>
					<Text style={styles.level}>{item.level2 ? item.level2 : ""}</Text>

					<View style={statusStyle}>
						<Text style={styles.status}>{translatedStatus}</Text>
					</View>

				</TouchableOpacity>
				{showCheckBox}
			</View>
		)
	}

	renderList(label) {

		const styles = listStyles;

		let { props } = this;
		let { checklist, unfillData, satisfyData, followUpData, labelData, language, sectionData} = props;
		let itemDetail = checklist.questions;
	
		let eachSectionData;

		if(label == 'Unfill') {
			eachSectionData = unfillData;
			count = labelData.Unfill;
		}
		if(label == 'Satisfy') {
			eachSectionData = satisfyData;
			count = labelData.Satisfy;
		}
		if(label == 'FollowUp') {
			eachSectionData = followUpData;
			count = labelData.FollowUp;
		}
		
		let showFollowUpButton = checklist.can_batch_failed && this.state.showChangeStatus && this.state.selectedData.length > 0 ? (
			<View style={{flex: 1}}>
				<Button 
					style={[styles.button, {backgroundColor: Theme.item.status.followUp}]} 
					textStyle={[styles.buttonText,{color: '#FFFFFF'}]}
					onPress={()=>{
						let option = [];
						option.push('需跟進');
						props.updateSelectOptions(this.state.selectedData, 'FollowUp', checklist._id, option, 'checklist');
						labelData.FollowUp = labelData.FollowUp + this.state.selectedData.length;
						labelData.Unfill =  labelData.Unfill - this.state.selectedData.length;
						this.setState({
							selectedData: []
						});

						// let dropDownData = props.getCheckListDropDownData({}, sectionData, language);
						// this.setState({
						// 	dropDownData: dropDownData,
						// 	changeFilterData: false,
						// });

						props.updateLabelData(labelData);
					}}
				>
					{i18.FollowUp}
				</Button>
			</View>		
		) : (<View style={{flex: 1}}/>);

		let showSatisfyButton = checklist.has_option_statisfy && this.state.showChangeStatus && this.state.selectedData.length > 0  ? (
			<View style={{flex: 1}}>
				<Button 
					style={[styles.button, {backgroundColor: Theme.item.status.satisfy}]}
					textStyle={[styles.buttonText,{color: '#FFFFFF'}]}
					onPress={()=>{
						let option = [];
						option.push('滿意');
						props.updateSelectOptions(this.state.selectedData, 'Satisfy', checklist._id, option, 'checklist');
						labelData.Satisfy = labelData.Satisfy + this.state.selectedData.length;
						labelData.Unfill =  labelData.Unfill - this.state.selectedData.length;
						this.setState({
							selectedData: []
						});

						{/* let dropDownData = props.getCheckListDropDownData({}, sectionData, language);
						this.setState({
							dropDownData: dropDownData,
							changeFilterData: false,
						}); */}

						props.updateLabelData(labelData);
					}}
					>
					{i18.Satisfy}
				</Button>
			</View>
		) : (<View style={{flex: 1}}/>);


		let isUnit = false;
		if(this.props.navigation.state.params.isUnit) {
			isUnit = this.props.navigation.state.params.isUnit;
		}

		return (
			<View style={styles.container} tabLabel={{label, unfill: count, satisfy: count, followUp: count, total: labelData.total}}>

				<View style={styles.header}>
					<Text style={[styles.headerField, { flex: 5, paddingHorizontal:10,  textAlign:'left' }]}>{i18.ChecklistItem}</Text>
					<Text style={styles.headerField}>{i18.Level1}</Text>
					<Text style={styles.headerField}>{i18.Level2}</Text>
					<Text style={styles.headerField}>{i18.ChecklistStatus}</Text>
				</View>


				<SectionList
					style={styles.list}
					renderItem={this.renderItem}
					renderSectionHeader={this.renderHeader}
					sections={eachSectionData}
					initialNumToRender={10}
					removeClippedSubviews={true}
				/>

				{
					label == 'Unfill' && !isUnit?
						<Animatable.View style={styles.popUp} ref="select">
							{showFollowUpButton}
							<View style={{flex: 1.1}}>
								<Button style={[styles.button,{borderWidth: 1, borderColor: Theme.list.popUp.button}]} textStyle={styles.buttonText} 
									onPress={()=>{ 

										eachSectionData = eachSectionData.map((section) => {
											let newSection = {...section};
											newSection.data = newSection.data.map((item, i)=>{
												let newItem = {...item};
												if(i == 0){
													newItem.selected = this.state.showChangeStatus ? false : true;
												}
												return newItem;
											})
											return newSection;   
										});

										props.fakeUpdateUnfillData(eachSectionData);
										this.setState({
											selectedData: [],
											showChangeStatus: this.state.showChangeStatus ? false : true
										});
									}}>
									{this.state.showChangeStatus ? i18.Cancel : i18.ChangeStatus}
								</Button>
							</View>
							{showSatisfyButton}
						</Animatable.View> : null
				}

			</View>
		);
	}

	onShowDropDownList(data) {
		this.setState({
			dropDownList: data,
		});
	}

	onFilterList(title, list, filter) {
		let { props } = this;
		let { sectionData, language} = props;

		var newDropDownData = this.state.dropDownData;

		let findIndexOfTitle = 0;
		
		for(var key in newDropDownData)
		{
			if(newDropDownData[key].title==title){
				findIndexOfTitle = key;
			}
		}

		let dropDownList = this.state.dropDownList;
		newDropDownData.map((item, i) => {
			if(item.title == title) {
				let array;
				let compareValueExist = false;
				
				// filter.forEach((a)=>{
				// 	if(item.list.includes(a)){
				// 		compareValueExist = true;
				// 	}
				// });

				compareValueExist = filter.some((a)=>{
					return item.list.includes(a);
				});

				if(!compareValueExist){
					array = [];
				}else {
					array = filter;
				}
				
				if(!item.filter.includes(list)) {
					array.push(list);
				}else {
					var index = array.indexOf(list);
					array.splice(index, 1);
				}

				item.filter = array;
				dropDownList.filter = array;
			}else {
				if( i > findIndexOfTitle) {
					item.filter = [];
				}
			}
		});


		let dropDownData = props.getCheckListDropDownData(newDropDownData, sectionData, language);
		this.setState({
			dropDownData: dropDownData,
			dropDownList: dropDownList
		});

		// let unfillData = this.separateForDifferenceLabel(sectionData, 'Unfill', false);
		// let satisfyData = this.separateForDifferenceLabel(sectionData, 'Satisfy', false);
		// let followUpData = this.separateForDifferenceLabel(sectionData, 'FollowUp', false);
		let { unfillData, satisfyData, followUpData } = this.separateForDifferenceLabel(sectionData, '', false);

		this.filterData('Unfill', unfillData, dropDownData);
		this.filterData('Satisfy', satisfyData, dropDownData);
		this.filterData('FollowUp', followUpData, dropDownData);
	}

	filterData(label, sectionData, newDropDownData) {

		let { props } = this;
		let { checklist } = props;

		let filteredSectionData;
		let filteredChecklist = Object.assign({}, checklist);
		// console.log("checklist filteredChecklist", filteredChecklist);

		let dropDownData = newDropDownData;
		let filter = {};

		if(dropDownData.length > 0){
			if(dropDownData[0].filter.length > 0) {
				filter.location = dropDownData[0].filter;
			}
			if(dropDownData[1].filter.length > 0) {
				filter.level1 = dropDownData[1].filter;
			}
			if(dropDownData[2].filter.length > 0) {
				filter.level2 = dropDownData[2].filter;
			}
	
			filteredSectionData = sectionData.map((section) => {
				let newSection = {...section};
				newSection.data = newSection.data.filter((item)=>{
					let newItem = {...item};
					for(let key in filter) {
						if(newItem[key] === null || newItem[key] === undefined || !filter[key].includes(newItem[key])){
							return false;
						}
					}
					return true;
				})
				return newSection;   
			});

			filteredChecklist.questions = filteredChecklist.questions.map((section) => {
				let newSection = {...section};
				newSection.data = newSection.data.filter((item)=>{
					let newItem = {...item};
					for(let key in filter) {
						if(newItem[key] === null || newItem[key] === undefined || !filter[key].includes(newItem[key])){
							return false;
						}
					}
					return true;
				})
				return newSection;   
			});


			// console.log("filteredSectionData", filteredSectionData)
			// console.log("filteredChecklist", filteredChecklist)

			props.filterCheckList(label ,filteredSectionData, filteredChecklist);
		}
	}

	cleanDropDownList(title) {
		let { props } = this;
		let { sectionData, language} = props;

		for(var key in this.state.dropDownData)
		{
			if(this.state.dropDownData[key].title==title){
				findIndexOfTitle = key;
			}
		}
		
		let newDropDownData = this.state.dropDownData.map((data, i)=>{
			if(data.title == title){
				data.filter = [];
			}else {
				if( i > findIndexOfTitle) {
					data.filter = [];
				}
			}
			return data;
		});

		let dropDownData = props.getCheckListDropDownData(newDropDownData, sectionData, language);
		
		let dropDownList = this.state.dropDownList;
		
		if(dropDownList.title == title) {
			dropDownList.filter = [];
		}
		
		// let unfillData = this.separateForDifferenceLabel(sectionData, 'Unfill', false);
		// let satisfyData = this.separateForDifferenceLabel(sectionData, 'Satisfy', false);
		// let followUpData = this.separateForDifferenceLabel(sectionData, 'FollowUp', false);
		let { unfillData, satisfyData, followUpData } = this.separateForDifferenceLabel(sectionData, '', false);

		this.filterData('Unfill', unfillData, dropDownData);
		this.filterData('Satisfy', satisfyData, dropDownData);
		this.filterData('FollowUp', followUpData, dropDownData);

		this.setState({
			dropDownList: dropDownList,
			dropDownData: dropDownData
		});
	}

	closeDropDownList() {
		this.setState({
			dropDownList: {},
		});
	}

	renderUnitDropDownList() {
		let { props } = this;
		let { unitCheckList} = props;   
        
        if(Object.keys(unitCheckList).length > 0){
            return unitCheckList.map((item, i) => {
				let showChecked = item._id == this.state.unitDropDownFocus ? (<Icon icon={'checked'} style={styles.checkIcon} />) : null;    
				let date = item._updated_at;
				date = moment.utc(date).format('YYYY-MM-DD');   
				let assignee = (item.assignee) ? item.assignee : item._created_by;
                return(
                    <TouchableOpacity key={i} onPress={() => {
                       this.props.modifyUnitCheckList(unitCheckList, item._id);
					   this.setState({
							unitFirstRow: item.resp_items ? date : i18.consolidatedView,
							unitSecondRow: item.resp_items ? this.getDisplayName(assignee) : "",
							unitDropDownFocus: item._id,
							isUnitEachRecord: item.resp_items ? true : false,
							showUnitDropDown: false
						});
					   
                    }}>
                        <View style={{ width: '100%',height:60, borderBottomWidth: 1, alignItems: 'center', justifyContent: 'center', flexDirection: 'row'}}>
                            <View style={{flex: 1}}/>
                            <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                                <Text style={{textAlign: 'center'}}>{!item.resp_items ? i18.consolidatedView : date + " " + this.getDisplayName(assignee) }</Text>
                            </View>	   
                            <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                                {showChecked}
                            </View>    
                        </View>	
                    </TouchableOpacity>
                );
            });
        }else {
            return null;
        }
    }


	render() {
		let { props } = this;
		let { checklist, unfillData, labelData, satisfyData, followUpData, sectionData } = props;
		
		
		let translatedUnitAddress = props.navigation.state.params.translatedUnitAddress;

		var showDropDownList = Object.keys(this.state.dropDownList).length > 0  ? (
			<DropDownList 
				closeDropDownList={()=> this.closeDropDownList()}
				dropDownData={this.state.dropDownData}
				dropDownList={this.state.dropDownList}
				cleanDropDownList={(title) => this.cleanDropDownList(title)}
				onFilterList={(title, list, filter)=> this.onFilterList(title, list, filter)}
				/>
		) : null;

		var showUnitDropDown =  this.state.showUnitDropDown ? (
			<View style={{
                position: 'absolute',
                width: Dimensions.get('window').width,
                height: Dimensions.get('window').height,
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                <View style={styles.dropDownListContainer}>
                    <View style={{height: 50, alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: 1, backgroundColor: "#131913", flexDirection: 'row'}}>
                        <View style={{flex: 1}}/>
                        <View style={{flex: 1}} />
                    </View>	
                    <ScrollView>
						{this.renderUnitDropDownList()}
                    </ScrollView>
                    <TouchableOpacity style={{height: 50, alignItems: 'center', justifyContent: 'center'}} onPress={() => {
                        this.setState({showUnitDropDown: false})
                    }}>
                        <Text>{i18.Close}</Text>
                    </TouchableOpacity>

                </View>
            </View>  
		) : null;

		let showAllButton = this.state.showChangeStatus ? (
			<Button
				onPress={()=>{
					let sectionData = unfillData;
					let selectedData = this.state.selectedData;

					sectionData = sectionData.map((section) => {
						let newSection = {...section};
						newSection.data = newSection.data.map((item, i)=>{
							let newItem = {...item};
							if(i == 0){
								newItem.selected = this.state.showChangeStatus ? false : true;
							}
							if (!(selectedData.indexOf(newItem._id) > -1)) {
								selectedData.push(newItem._id);
							}
							return newItem;
						})
						return newSection;   
					});

					props.fakeUpdateUnfillData(sectionData);
					this.setState({
						selectedData: selectedData,
					});

					
				}} 
				style={[styles.button,{backgroundColor: '#FFFFFF', borderColor: '#B7B7B7', borderWidth: 1}]} 
				textStyle={[styles.buttonText, {color: '#FF8136'}]}>
					{i18.chooseAll}
				</Button>
		) : <View/>;

		let submitButton = (labelData.FollowUp + labelData.Satisfy) > 0  && !this.state.showChangeStatus ? (
			<Button
			onPress={()=> {
				let showInputFollowUpImageAlert = false;
				let showInputSatisfyImageAlert = false;

				if(checklist.require_photo_for_failed){
					followUpData.every((item) => {
						item.data.every((eachData) => {
							if(eachData.images == undefined || eachData.images.length < 1) {
								showInputFollowUpImageAlert = true
								return false
							}
							return true
						})
					})
				}

				if(checklist.require_photo_for_statisfy) {
					satisfyData.every((item) => {
						item.data.every((eachData) => {
							if(eachData.images == undefined || eachData.images.length < 1) {
								showInputSatisfyImageAlert = true
								return false
							}
							return true
						})
					})
				}


				if(showInputFollowUpImageAlert) {
					Alert.alert(
						i18.shouldInputPhotoForFailed,
						'',
						[
							{text: i18.OK, onPress: () => console.log('pressed')},
						]
					)
				}else if (showInputSatisfyImageAlert) {
					Alert.alert(
						i18.shouldInputPhotoForSatisfy,
						'',
						[
							{text: i18.OK, onPress: () => console.log('pressed')},
						]
					)
				}else if(checklist.require_skip_function == false && labelData.Unfill > 0 ){
					Alert.alert(
						i18.shouldFillAll,
						'',
						[
							{text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
						]
					)
					
				}else {
					Alert.alert(
						i18.AskForSubmitCheckList,
						'',
						[
							{text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
							{text: i18.OK, onPress: () => {
								props.pendingCheckLists(checklist);
								this.props.navigation.goBack();
							}},
						]
					)
				}

			}} 
			style={[styles.button, {backgroundColor: Theme.footer.button}]} 
			textStyle={styles.buttonText}>{i18.Submit}</Button>
		) : <View/>; 

		let isUnit = false;
		if(this.props.navigation.state.params.isUnit) {
			isUnit = this.props.navigation.state.params.isUnit;
		}


		let showFooter = isUnit ? null : (
			<View style={styles.footer}>
				{showAllButton}
				<View/> 
				{submitButton}
			</View>
		) 

		return (
			<View style={styles.container}>

				<Nav 
					showDropDown={true}
					left='BACK'
					dropDownData={this.state.dropDownData}
					isUnit={isUnit}
					showUnitDropDown={()=>{
						this.setState({
							showUnitDropDown: true,
							dropDownList: {},
							changeFilterData: true,
						})
					}}
					unitFirstRow={this.state.unitFirstRow}
					unitSecondRow={this.state.unitSecondRow}
					onShowDropDownList={(val)=>this.onShowDropDownList(val)} 
					onLeftPress={this.onBack}>{`${translatedUnitAddress}\n檢查清單`}</Nav>

				<ScrollableTabView
					tabBarPosition={'top'}
					style={styles.list}
					prerenderingSiblingsNumber={3}
					initialPage={0}
					showDropDown={true}
					locked={true}
					renderTabBar={() => <TabBar showChangeStatus={this.state.showChangeStatus}/>}>
					{this.state.isUnitEachRecord ? null : this.renderList('Unfill')}
					{this.renderList('Satisfy')}
					{this.renderList('FollowUp')}
				</ScrollableTabView>
				{showFooter}
				{showDropDownList}
				{showUnitDropDown}
			</View>
		);
	}
}

const headerStyles = StyleSheet.create({
	container:{
		padding: 12,
		backgroundColor: Theme.section.bg, 
		borderBottomColor: Theme.section.border, 
		borderBottomWidth: 0.5,
	},
	text:{
		fontSize: 18,
		fontWeight: 'bold'
	},
	empty:{
		height:40,
	},
});

const itemStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		marginBottom: 3,
		alignItems: 'center',
		borderBottomWidth: 3,
		borderBottomColor: Theme.item.status.unfill,
		backgroundColor: Theme.item.bg,
	},
	question:{
		flex: 5,
		fontSize: 14,
		fontWeight: '700',
		textAlign: 'left',
		paddingVertical: 12,
		paddingHorizontal: 10,
	},
	level:{
		flex: 2,
		fontSize: 14,
		textAlign: 'center',
		color: Theme.item.level,
	},
	statusContainer:{
		flex: 2, 
		alignItems: 'center', 
		justifyContent: 'center',
		backgroundColor: Theme.item.status.unfill, 
		alignSelf: 'stretch',
	},
	status:{
		backgroundColor:'transparent',
		color: Theme.item.status.text,
		fontSize: 13
	},
	checkIcon: {
        color: '#FF8238',
        width: 52,
		fontSize: 26,
		backgroundColor:'transparent',
		textAlign:'center',
    },
});

const listStyles = StyleSheet.create({
	container: {
		flex: 1,
	},
	header: {
		flexDirection: 'row',
		alignItems: 'center',
		backgroundColor: Theme.list.header.bg,
		paddingVertical:18,
	},
	headerField: {
		flex: 2,
		fontSize: 14,
		textAlign: 'center',
	},
	list: {
		flex: 1,
	},
	popUp: {
		flexDirection: 'row',
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,
		backgroundColor: Theme.list.popUp.bg,
		borderTopColor: 'black',
		borderTopWidth: 1,
		justifyContent: 'space-between',
		alignItems: 'center',
		minHeight: 44,
	},
	button: {
		borderRadius: 5,
		marginHorizontal: 10,
		paddingVertical: 8,
		backgroundColor: 'transparent',
	},
	buttonText: {
		color: Theme.list.popUp.button,
		fontSize: 14,
		fontWeight: '500',
	},
});

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	list: {
		flex: 1,
	},
	footer: {
		flexDirection: 'row',	
		alignItems: 'center',
		justifyContent: 'space-between',
		backgroundColor: Theme.footer.bg,
		borderTopColor: Theme.footer.border,
		borderTopWidth: 1,
		minHeight: 44,
	},
	button: {
		borderRadius: 5,
		marginHorizontal: 10,
		paddingVertical: 8,
	},
	buttonText: {
		fontSize: 14,
		fontWeight: '500',
	},
	icon: {
		width: 80,
		height: 80,
	},
	dropDownListContainer: {
		backgroundColor: '#fff',
		width: Dimensions.get('window').width * 0.85,
		height: Dimensions.get('window').height * 0.8,
		marginBottom: Dimensions.get('window').height * 0.05,
		borderWidth: 1,
	},
	checkIcon: {
        color: '#FF8238',
        width: 52,
		fontSize: 32,
		backgroundColor:'transparent',
		textAlign:'center',
    },
});

const mapStateToProps = (state, ownProps) => {
	
	return {
		language: state.setting.language,
		modifyCheckListId: state.checklists.modifyCheckList,
		unitCheckList: state.checklists.unitCheckList,
		checklist: state.checklists.checklist,
		sectionData: state.checklists.sectionData,
		unfillData: state.checklists.unfillData,
		satisfyData: state.checklists.satisfyData,
		followUpData: state.checklists.followUpData,	
		labelData: state.checklists.labelData,
		filteredChecklistTotal: state.checklists.filteredChecklistTotal,
		filteredChecklist: state.checklists.filteredChecklist,
		checkUpdatedinDetail: state.checklists.checkUpdatedinDetail,
		users: state.project.users
	}
}



import { CheckLists as checkListsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';

let checkListsProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {...checkListsProps, ...projectProps};
})(CheckList);
