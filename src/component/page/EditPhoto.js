import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	ImageEditor,
	ScrollView,
	Platform,
	Alert,
	NativeModules,
	Linking
} from 'react-native';
const TextInput = require('CustTextInput');
import { connect } from 'react-redux'

import Server from 'Server';

const Button = require('Item').Button;
const Actions = require('Redux').Action.root;

import ImagePicker from 'react-native-image-picker';
import SignatureCapture from 'cust/react-native-signature-capture';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import KeyboardSpacer from 'react-native-keyboard-spacer';

import i18 from 'i18';

const SCALING_MODE = 1;
const DRAWING_MODE = 2;

import CachedImage from 'react-native-cached-image';
const ImageCacheProvider = CachedImage.ImageCacheProvider;

import { _getServerUrl } from 'Request';

export default class EditPhoto extends Component {

	constructor(props) {
		super(props);
		let currentItem = (this.props.navigation.state.params.questionId) ? this.getCurrentQuestion(this.props.navigation.state.params.questionId) : this.props.navigation.state.params.item;
		let images = this.getImagesAndCaptions(currentItem.images);
		this.state = {
			editMode: SCALING_MODE,
			isErase: false,
			scaling: true,
			isDrawing: (Platform.OS === 'ios'),
			strokeColor: "",
			isFloorPlan: false,
			images: images.imagePaths,
			captions: images.captions,
			imageIndex: (this.props.navigation.state.params.currentIndex) ? this.props.navigation.state.params.currentIndex : 0,
			questionId: (this.props.navigation.state.params.questionId) ? this.props.navigation.state.params.questionId : this.props.navigation.state.params.item._id,
			isUnit: this.props.navigation.state.params.isUnit,
			isNew: true
		};

		this.actionDone = this.actionDone.bind(this);
		this.drawImgRef = [];
		this.saved = false	// used to prevent the multiple call from "onSaveEvent" from iOS native
	}

	getCurrentQuestion(questionId) {
		let newItem;
		this.props.checklist.questions.some((item) => {
			item.data.some((i) => {
				if (i._id === questionId) {
					newItem = i;
				}
				return (newItem);
			});
			return (newItem);
		});

		return newItem;
	}

	getImagesAndCaptions(images) {
		let imagePaths = (images) ? images.map((imgObj) => {
			return imgObj.path;
		}) : [];
		let captions = (images) ? images.map((imgObj) => {
			return imgObj.caption;
		}) : [];

		return { imagePaths, captions };
	}

	componentWillReceiveProps(nextProps) {

	}

	componentWillMount() {
		let { props } = this;
		props.otherActions.cameraStatus(true);
	}

	componentWillUnmount() {
		if (this.drawImgRef) {
			this.drawImgRef.some((ref) => {
				if (ref) {
					ref.clearImage();
				}
				return (ref);
			})
		}
	}

	componentDidMount() {
		if (this.state.images.length === 0 || this.props.navigation.state.params.forceAdd) {
			this.setState({
				isNew: true
			});
			this.launchImagePicker(true);
		} else {
            this.setState({
                isNew: false
        	});
			this.state.images.forEach((fp, idx) => {

				let path = fp;
				if (path && !path.startsWith("http")) {
					path = _getServerUrl() + "blob_image/" + path;
				}
				ImageCacheProvider.getCachedImagePath(path).then((imagePath) => {
					let images = this.state.images;
					images[idx] = imagePath;
					this.setState({
						images: images
					});
				});
			})
		}
	}

	isEraserToolSelected() {
		return this.state.toolSelected === sketchViewConstants.toolType.eraser.id;
	}

	toolChangeClick() {
		this.setState({ toolSelected: tools[this.state.toolSelected].nextId });
	}

	switchToScale() {
		this.setState({ scaling: !this.state.scaling });
	}

	getToolName() {
		return tools[this.state.toolSelected].name;
	}

	onSketchSave(saveEvent) {
		this.props.onSave && this.props.onSave(saveEvent);
	}

	actionDone() {
		let { props } = this;
		if (this.state.images.length > 0) {
			if (Platform.OS === 'ios') {
				// iOS: trigger the native view manager to handle all views
				this.drawImgRef[0].saveImage();
			} else {
				// android:
				this.drawImgRef[0].saveImage();
			}
		} else {
			if (this.props.navigation.state.params.item) {
				if (this.props.navigation.state.params.callback) {
					// for ad hoc item
					this.props.navigation.state.params.callback([]);
				} else {
					// for defect item
					this.props.itemActions.updateItemImages(this.state.questionId, []);
				}
			} else {
				// for checklist response item
				this.props.checklistActions.updateItemImages(this.state.questionId, []);
			}
			this.props.navigation.goBack();
			props.otherActions.cameraStatus(false);
		}
	}

	actionBack() {
		let { props } = this;
		if (this.state.images.length > 0 && !this.state.isUnit) {
			Alert.alert(
				'',
				i18.drawingUnsaved,
				[
					{
						text: 'Cancel', onPress: () => {
							props.otherActions.cameraStatus(false)
						}, style: 'cancel'
					},
					{
						text: 'OK', onPress: () => {
							this.props.navigation.goBack();
							props.otherActions.cameraStatus(false);
						}
					},
				]
			)
		} else {
			this.props.navigation.goBack();
			props.otherActions.cameraStatus(false);
		}
	}

  async launchImagePicker (forceBack) {
    let {props} = this

    const options = {
			title: i18.SelectAction,
			cancelButtonTitle: i18.Cancel,
			takePhotoButtonTitle: i18.TakePhoto,
			chooseFromLibraryButtonTitle: i18.ChoosePhoto,
			quality: 0.5,
			maxWidth: 1024,
			maxHeight: 1024,
			noData: true,
			storageOptions: {
				skipBackup: true,
				path: 'images'
			}
    }
		ImagePicker.showImagePicker(options, async (response) => {
			if (response.didCancel) {
				if (forceBack) {
					  this.props.navigation.goBack()
					  props.otherActions.cameraStatus(false)
				}
			} else if (response.error) {
				console.log('ImagePicker Error: ', response.error)
				if (Platform.OS === 'ios') {
					if (forceBack) {
							this.props.navigation.goBack()
							props.otherActions.cameraStatus(false)
					}
					NativeModules.CameraManager.checkDeviceAuthorizationStatus().then((isAuthorized) => {
						if (!isAuthorized) {
							Alert.alert(
								i18.Cancel,
								i18.pleaseSetCameraPermission,
								[
									{
										text: i18.Cancel, onPress: () => {
										// cancel
										if (forceBack) {
											this.props.navigation.goBack()
											props.otherActions.cameraStatus(false)
						  					}
									}
					  }, {
						text: i18.Settings, onPress: () => {
						  Linking.openURL('app-settings:')
						}
					  }])
					}
				  })
				}
			} else {
				this.drawImgRef = []
				let path = (Platform.OS === 'ios') ? response.uri : response.path
				let imagePaths = this.state.images
				imagePaths.push(path)
				let imageCaptions = this.state.captions
				imageCaptions.push('')
				this.setState({
					images: imagePaths,
					captions: imageCaptions,
          			imageIndex: imagePaths.length - 1
				});
				this.setState({
					imageIndex: imagePaths.length - 1
				});
				if(Platform.OS === 'ios') {
                    setTimeout(()=> this.resetImageWithParam(imagePaths.length - 1), 500);
                }
			}
		});
	}

	render() {
		let { strokeColor, editMode, isDrawing, isErase, isFloorPlan } = this.state;
		var btnTextColor = 'white';

		let showButton = this.state.isUnit ? (
			<View style={{ flex: 1 }} />
		) : (
				<View style={{ flexDirection: "row", flex: 1 }}>
					<TouchableOpacity style={styles.buttonStyle}
						onPress={() => { this.deleteImage() }} >
						<Text style={{ color: btnTextColor }}>{i18.delete}</Text>
					</TouchableOpacity>

					<TouchableOpacity style={styles.buttonStyle}
						onPress={() => { this.resetImage() }} >
						<Text style={{ color: btnTextColor }}>{i18.reset}</Text>
					</TouchableOpacity>

					<TouchableOpacity style={[styles.buttonStyle, { backgroundColor: (this.state.isDrawing ? 'rgba(255,255,255,1)' : 'gray') }]}
						onPress={() => { this.drawing() }} >
						<Text style={{ color: (this.state.isDrawing ? 'gray' : 'rgba(255,255,255,1)') }}>{i18.drawing}</Text>
					</TouchableOpacity>

					<TouchableOpacity style={[styles.buttonStyle, { backgroundColor: (this.state.isErase ? 'rgba(255,255,255,1)' : 'gray') }]}
						onPress={() => { this.erase() }} >
						<Text style={{ color: (this.state.isErase ? 'gray' : 'rgba(255,255,255,1)') }}>{i18.erase}</Text>
					</TouchableOpacity>
				</View>
			)


		return (
			<View style={{ flex: 1, flexDirection: "column", backgroundColor: "black" }}>

				{showButton}

				<ScrollableTabView
					ref="tabView"
					style={{ flex: 10 }}
					locked={true}
					page={this.state.imageIndex}
					prerenderingSiblingsNumber={this.state.images.length}
					renderTabBar={() => <View style={{ backgroundColor: '#fff0ff' }} />}>
					{
						(this.state.images.length > 0) ? this.state.images.map((path, idx) => {
							if (Platform.OS === 'ios') {
								if (path && !path.startsWith("file") && !path.startsWith("http") && !path.startsWith("/")) {
									return null;
								}
								if (path && path.indexOf('file://') == -1) {
									path = 'file://' + path;
								}
							}
							return (
								<View key={path} style={{ flex: 10 }}>
									<SignatureCapture
										style={{ flex: 10}}
										ref={(slide) => {return this.drawImgRef[idx] = slide}}
										onSaveEvent={this._onSaveEvent.bind(this)}
										saveImageFileInExtStorage={true}
										imageFilePath={path}
										strokeColor={strokeColor}
										editMode={this.state.isUnit ? false : editMode}
										isDrawing={this.state.isUnit ? false : isDrawing}
										isErase={this.state.isUnit ? false : isErase}
										floorPlanMode={isFloorPlan}
									/>
									<View style={{ height: 40, padding: 10, paddingBottom: 0, backgroundColor: 'black' }}>
										<TextInput
											editable={!this.state.isUnit}
											underlineColorAndroid='transparent'
											style={{
												borderRadius: 5,
												backgroundColor: 'white',
												height: 30,
												fontSize: 14,
												padding: 5,
											}}
											value={this.state.captions[idx]}
											onChangeText={(text) => {
												{/* console.log("image comment", text); */ }
												let captions = this.state.captions;
												captions[idx] = text;
												this.setState({
													captions: captions
												});
											}} />
									</View>
									<KeyboardSpacer topSpacing={-70} />
								</View>
							);
						}) : <View style={{ flex: 10 }} />
					}
				</ScrollableTabView>

        <View style={{height: 80}}>
          <View style={{flexDirection: 'row', flex: 1, justifyContent: 'center', alignItems: 'center'}}>
            <TouchableOpacity style={styles.bottomButtonStyle}
                              onPress={() => { this.actionBack() }}>
              <Text style={{color: btnTextColor}}>{i18.chooseBack}</Text>
            </TouchableOpacity>
            <ScrollView
              style={{flex: 1, height: 50, margin: 10}}
              horizontal={true}
              contentContainerStyle={{alignItems: 'center', flexDirection: 'row'}}>
              {
                this.state.images.map((path, idx) => {
                  {/* console.log("thumbnail path", path); */ }
                  if (path && !path.startsWith('file') && !path.startsWith('http') && !path.startsWith('/')) {
                    return null
                  }
                  if (path && path.indexOf('file://') === -1) {
                    path = 'file://' + path
                  }
                  return (
                    <TouchableOpacity
                      key={idx}
                      style={styles.imgButton}
                      onPress={() => {
						{/* this.refs.tabView.goToPage(idx); */ }
						this.setState({
						  imageIndex: idx
						})
						if (Platform.OS == 'ios' && !this.state.isNew) {
						  	// Reset after index changing
                            this.resetImageWithParam(idx);
						}
                      }}>
                      <Image
                        style={styles.imgButton}
                        resizeMode={'cover'}
                        source={{uri: path}}/>
                    </TouchableOpacity>
                  )
                })
              }
              {
                (this.state.isUnit || this.props.navigation.state.params.disable || this.state.images.length > 99) ? null : (
                  <TouchableOpacity
                    style={styles.imgButton}
                    onPress={() => {
                        this.launchImagePicker(false);
					}}>
                    <Text>{'+'}</Text>
                  </TouchableOpacity>
                )
              }
            </ScrollView>
            {
              (this.state.isUnit) ? null : (
                <TouchableOpacity style={styles.bottomButtonStyle}
                                  onPress={() => { this.actionDone() }}>
                  <Text style={{color: btnTextColor}}>{i18.done}</Text>
                </TouchableOpacity>
              )
            }
          </View>
        </View>
      </View>
    );
  }

	resetImage() {
	    if(!this.state.imageIndex) {
	        return;
        }
        this.resetImageWithParam(this.state.imageIndex);
	}

    resetImageWithParam(index) {
		if (index < 0) {
			return
		}
		this.drawImgRef[index].resetImage();
    }

	deleteImage() {
    if (this.state.imageIndex < 0) {
      return
    }
		this.drawImgRef[this.state.imageIndex].removeImage();
		let refs = this.drawImgRef.filter((ref) => {
			return (ref != null);
		});
		this.drawImgRef = [];
		let imagePaths = this.state.images;
		imagePaths.splice(this.state.imageIndex, 1);
		let imageCaptions = this.state.captions;
		imageCaptions.splice(this.state.imageIndex, 1);
		this.setState({
			images: imagePaths,
			captions: imageCaptions,
      		imageIndex: imagePaths.length - 1
		});
	}

	drawing() {
		let { editMode, isErase, isDrawing } = this.state;
		if (Platform.OS == 'ios') {
			this.setState({
				isDrawing: true,
				isErase: false
			});
		} else {
			this.setState({
				isDrawing: !isDrawing,
				isErase: false
			});
		}
	}

	erase() {
		let { editMode, isErase, isDrawing } = this.state;
		if (Platform.OS == 'ios') {
			this.setState({
				isDrawing: false,
				isErase: true
			});
		} else {
			this.setState({
				isDrawing: false,
				isErase: !isErase
			});
		}
	}

	_onSaveEvent(result) {
		// console.log("_onSaveEvent", result);
		if (!this.saved) {
			if (result.paths) {
				this.saved = true;
				let images = this.state.captions.map((caption, idx) => {
					return {
						path: result.paths[idx],
						caption: (caption) ? caption : ""
					}
				});
				if (this.props.navigation.state.params.item) {
					if (this.props.navigation.state.params.callback) {
						// for ad hoc item
						this.props.navigation.state.params.callback(images);
					} else {
						// for defect item
						this.props.itemActions.updateItemImages(this.state.questionId, images);
					}
				} else {
					// for checklist response item
					this.props.checklistActions.updateItemImages(this.state.questionId, images);
				}
				this.props.navigation.goBack();
			}
		}
	}
}

const styles = StyleSheet.create({
	signature: {
		flex: 1,
		borderColor: '#000033',
		borderWidth: 1,
	},
	buttonStyle: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		height: 30,
		backgroundColor: "gray",
		margin: 10,
		borderRadius: 5
	},
	bottomButtonStyle: {
		flex: 0,
		justifyContent: "center",
		alignItems: "center",
		height: 30,
		backgroundColor: "gray",
		margin: 10,
		paddingLeft: 10,
		paddingRight: 10,
		borderRadius: 5
	},
	imgButton: {
		margin: 5,
		height: 40,
		width: 40,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'gray'
	},
	imgInBtn: {
		height: 40,
		width: 40,
	}
});

const mapStateToProps = (state, ownProps) => {
	return {
		checklist: state.checklists.checklist
	}
}

import { CheckLists as checkListsMapDispatchToProps, Items as itemsMapDispatchToProps, Others as othersMapDispatchToProps } from 'Controller';

const mapDispatchToProps = (dispatch, ownProps) => {
	let checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps);
	let itemsProps = itemsMapDispatchToProps(dispatch, ownProps);
	let othersProps = othersMapDispatchToProps(dispatch, ownProps);
	return {
		checklistActions: checkListsProps,
		itemActions: itemsProps,
		otherActions: othersProps,
	};
}

module.exports = connect(mapStateToProps, mapDispatchToProps)(EditPhoto);
