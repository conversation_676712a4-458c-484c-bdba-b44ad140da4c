import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	ImageEditor,
	TextInput,
	ScrollView,
	Platform,
	Alert,
	Dimensions
} from 'react-native';
import { connect } from 'react-redux'

import Server from 'Server';

const Button = require('Item').Button;
const Actions = require('Redux').Action.root;

import SignatureCapture from 'cust/react-native-signature-capture';
import ScrollableTabView from 'react-native-scrollable-tab-view';

import i18 from 'i18';

const SCALING_MODE = 1;
const DRAWING_MODE = 2;

import CachedImage  from 'react-native-cached-image';
const ImageCacheProvider = CachedImage.ImageCacheProvider;

import { _getServerUrl } from 'Request';

export default class FloorPlan extends Component {

	constructor(props) {
		super(props);
		let currentItem = (typeof this.props.navigation.state.params.questionId == "string") ? this.getCurrentQuestion(this.props.navigation.state.params.questionId) : this.props.navigation.state.params.item;
		this.state = {
			editMode: SCALING_MODE,
			isErase: false,
			scaling: true,
			isDrawing: (Platform.OS == 'ios'),
			strokeColor: "",
			isFloorPlan: true,
			images: this.props.navigation.state.params.floor_plans,
			screenHeight: Dimensions.get('window').height,
			imageIndex: 0,
			annotations: (currentItem.annotations) ? currentItem.annotations : null,
			questionId: this.props.navigation.state.params.questionId,
			isUnit: this.props.navigation.state.params.isUnit,

			needUpdate:false,
		};

		this.actionDone = this.actionDone.bind(this);
		this.drawImgRef = [];
		this.saved = false	// used to prevent the multiple call from "onSaveEvent" from iOS native
	}

	getCurrentQuestion(questionId) {
		let newItem;
		this.props.checklist.questions.some((item)=>{
			item.data.some((i) => {
				if(i._id == questionId) {
					newItem = i;
				}
				return (newItem);
			});
			return (newItem);
		});

		return newItem;
	}

	componentWillReceiveProps(nextProps) {
		
	}

	componentWillUnmount() {
		
	}

	componentWillMount() {
		let { props } = this;
		props.otherActions.cameraStatus(true);
	}

	componentDidMount() {
		this.state.images.forEach((fp, idx) => {
			let path = fp;
			if (!path.startsWith("http")) {
				path = _getServerUrl() + "blob_image/" + path;
			}
			ImageCacheProvider.getCachedImagePath(path).then((imagePath) => {
				let images = this.state.images;
				images[idx] = imagePath;
				this.setState({
					images: images
				});
			});
		})
	}

	componentDidUpdate(){

		if(this.state.needUpdate){
			this.setState({needUpdate:false})
		}
		
	}

	actionDone() {
		// this.props.navigation.goBack();
		this.drawImgRef[0].saveAnnotations();
	}

	actionBack() {
		let { props } = this;
		this.props.navigation.goBack();
		props.otherActions.cameraStatus(false);
	}

	render() {
		let { strokeColor, editMode, isDrawing, isErase, isFloorPlan } = this.state;
		var btnTextColor = 'white';
		
		let showButton = (Platform.OS == 'ios') ? (
			<View style={{ flex: 1 }} />
		) : (
			<View style={{ flexDirection: "row", flex: 1 }}>

				<TouchableOpacity style={[styles.buttonStyle, { backgroundColor: (this.state.isDrawing ? 'rgba(255,255,255,1)' : 'gray') }]}
					onPress={() => { this.drawing() }} >
					<Text style={{ color: (this.state.isDrawing ? 'gray' : 'rgba(255,255,255,1)') }}>{i18.marking}</Text>
				</TouchableOpacity>
			</View>
		)

		return (
			<View style={{ flex: 1, flexDirection: "column", backgroundColor: "black" }}>
				
				{showButton}

				<ScrollableTabView
					ref="tabView"
					style={{ flex: 10, backgroundColor: "lightgray" }}
					locked={true}
					page={this.state.imageIndex}
					prerenderingSiblingsNumber={this.state.images.length}
					renderTabBar={() => <View style={{backgroundColor: '#fff0ff'}} />}>
					{
						(this.state.images.length > 0 && !this.state.needUpdate) ? this.state.images.map((path, idx) => {
							let coordinates;
							if (this.state.annotations) {
								coordinates = this.state.annotations[idx];
							}
							return (
								<View key={path} style={{ flex: 10 }}>
									<SignatureCapture
										style={{ flex: 10 }}
										ref={(slide) => this.drawImgRef[idx] = slide}
										onSaveEvent={this._onSaveEvent.bind(this)}
										saveImageFileInExtStorage={true}
										imageFilePath={path}
										strokeColor={strokeColor}
										editMode={editMode}
										isDrawing={isDrawing}
										isErase={isErase}
										floorPlanMode={isFloorPlan}
										annotations={coordinates}
									/>
									{
										this.state.isUnit ? (
										<View 
											style={{ position: 'absolute', zIndex: 100, backgroundColor: 'transparent',
											width: Dimensions.get('window').width, height: Dimensions.get('window').height * 0.8  }} />
										)  : null
									}

								</View>
							);
						}) : <View style={{ flex: 10 }}/>
					}
				</ScrollableTabView>

				<View style={{ height: 80 }}>
					<View style={{ flexDirection: "row", flex: 1, justifyContent: "center", alignItems: "center" }}>
						<TouchableOpacity style={styles.bottomButtonStyle}
							onPress={() => { this.actionBack() }} >
							<Text style={{ color: btnTextColor }}>{i18.chooseBack}</Text>
						</TouchableOpacity>

						<View style={{flex: 1}} />

						{
							((this.state.questionId || this.props.navigation.state.params.callback) && !this.state.isUnit) ? (
								<TouchableOpacity style={styles.bottomButtonStyle}
									onPress={() => { this.resetImage() }} >
									<Text style={{ color: btnTextColor }}>{i18.reset}</Text>
								</TouchableOpacity>
							) : null
						}

						{
							((this.state.questionId || this.props.navigation.state.params.callback) && !this.state.isUnit) ? (
								<TouchableOpacity style={styles.bottomButtonStyle}
									onPress={() => { this.actionDone() }} >
									<Text style={{ color: btnTextColor }}>{i18.done}</Text>
								</TouchableOpacity>
							) : null
						}

					</View>
				</View>

				<TouchableOpacity 
					onPress={() => {
						let idx = this.state.imageIndex;
						if (idx > 0) {
							idx--;
						}
						this.setState({
							imageIndex: idx
						});
					}}
					style={{
						width: 50,
						height: 50,
						backgroundColor: 'rgba(0,0,0,0.4)',
						borderRadius: 25,
						position: 'absolute',
						left: 0,
						top: (this.state.screenHeight - 130) / 2,
						alignItems: "center", 
						justifyContent: "center"
					}}>
					<View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
						<Text style={{fontSize: 20, color: "white"}}>{"<"}</Text>
					</View>
				</TouchableOpacity>
				
				<TouchableOpacity 
					onPress={() => {
						let idx = this.state.imageIndex;
						if (idx < this.state.images.length - 1) {
							idx++;
						}
						this.setState({
							imageIndex: idx
						});
					}}
					style={{
						width: 50,
						height: 50,
						backgroundColor: 'rgba(0,0,0,0.4)',
						borderRadius: 25,
						position: 'absolute',
						right: 0,
						top: (this.state.screenHeight - 130) / 2,
						alignItems: "center", 
						justifyContent: "center"
					}}>
					<View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
						<Text style={{fontSize: 20, color: "white"}}>{">"}</Text>
					</View>
				</TouchableOpacity>

			</View>
		);
	}

	resetImage() {
		
		if(this.state.annotations 
			&& this.state.annotations[this.state.imageIndex]
			){
				this.drawImgRef[this.state.imageIndex].resetImage();
				this.state.annotations[this.state.imageIndex] = [];	
			}
			this.setState({annotations:this.state.annotations, needUpdate:true})
		
	}

	deleteImage() {
		this.drawImgRef[this.state.imageIndex].removeImage();
		let refs = this.drawImgRef.filter((ref) => {
			return (ref != null);
		});
		this.drawImgRef = [];
		let imagePaths = this.state.images;
		imagePaths.splice(this.state.imageIndex, 1);
		let imageCaptions = this.state.captions;
		imageCaptions.splice(this.state.imageIndex, 1);
		this.setState({
			images: imagePaths,
			captions: imageCaptions
		});
		setTimeout(() => {
				this.setState({
					imageIndex: imagePaths.length - 1
				});
		}, 500);
	}

	drawing() {
		let { editMode, isErase, isDrawing } = this.state;
		if (Platform.OS == 'ios') {
			this.setState({
				isDrawing: true,
				isErase: false
			});
		} else {
			this.setState({
				isDrawing: !isDrawing,
				isErase: false
			});
		}
	}

	erase() {
		let { editMode, isErase, isDrawing } = this.state;
		if (Platform.OS == 'ios') {
			this.setState({
				isDrawing: false,
				isErase: true
			});
		} else {
			this.setState({
				isDrawing: false,
				isErase: !isErase
			});
		}
	}

	_onSaveEvent(result) {
		if (!this.saved) {
			// console.log("_onSaveEvent", result);
			if (result.paths) {
				this.saved = true;
				if (this.state.questionId)
					this.props.checklistActions.updateFloorPlanAnnotations(this.state.questionId, result.paths);
				else if (this.props.navigation.state.params.callback) {
					this.props.navigation.state.params.callback(result.paths);
				}
				this.props.otherActions.cameraStatus(false);
				this.props.navigation.goBack();
			}
		}
	}
}

const styles = StyleSheet.create({
	signature: {
		flex: 1,
		borderColor: '#000033',
		borderWidth: 1,
	},
	buttonStyle: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		height: 30,
		backgroundColor: "gray",
		margin: 10,
		borderRadius: 5
	},
	bottomButtonStyle: {
		flex: 0,
		justifyContent: "center",
		alignItems: "center",
		height: 30,
		backgroundColor: "gray",
		margin: 10,
		paddingLeft: 10,
		paddingRight: 10,
		borderRadius: 5
	},
	imgButton: {
		margin: 5,
		height: 40,
		width: 40,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'gray'
	},
	imgInBtn: {
		height: 40,
		width: 40,
	}
});

const mapStateToProps = (state, ownProps) => {
	return {
		checklist: state.checklists.checklist
  }
}

import { CheckLists as checkListsMapDispatchToProps, Items as itemsMapDispatchToProps,  Others as othersMapDispatchToProps } from 'Controller';

const mapDispatchToProps = (dispatch, ownProps) => {
	let checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps);
	let itemsProps = itemsMapDispatchToProps(dispatch, ownProps);
	let othersProps = othersMapDispatchToProps(dispatch, ownProps);
	return { 
		checklistActions: checkListsProps,
		itemActions: itemsProps,
		otherActions: othersProps,
	};
}

module.exports = connect(mapStateToProps, mapDispatchToProps)(FloorPlan);