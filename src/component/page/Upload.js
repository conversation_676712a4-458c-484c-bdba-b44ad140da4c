import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Dimensions,
	ScrollView,
	NetInfo,
	Alert
} from 'react-native';
import { connect } from 'react-redux'

import { taskList as Theme } from 'Theme';
import { Icon, Nav, Button } from 'Item';
import i18 from 'i18';

import moment from 'moment';

export default class Upload extends Component {

	constructor(props) {
		super(props);
		this.state = {

		};
	}

	componentWillMount() {
		let { props } = this;
		let { unit_address } = props;
		props.fetchLocalPendingChecklists(unit_address);
		props.fetchLocalPendingItems();
		props.fetchLocalPendingUnits();
		props.fetchLocalPendingKey();
	}

	onDownload = ()=>{
		
	}

	render() {
		let { props } = this;
		let { pendingCheckList, pendingItem, unit_address, pendingUnit, pendingKey, connectionInfo } = props;

		// console.log("pendingUnit",pendingUnit)

		let showNoTask;
		let showCheckList;
		let showItem;
		let showUnit;
		let showKey;


		if(pendingCheckList.length < 1 && pendingItem.length < 1 && pendingUnit.length < 1 && pendingKey.length < 1){
			showNoTask = (
				<View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
					<Icon style={{
						fontSize: 100,
						backgroundColor:'transparent',
						color: '#A8A8A8'}} icon={'no-report'}/>
					<Text style={{marginTop: 10, textAlign: 'center', width: 148, fontSize: 16, color: '#B9B9B9', lineHeight: 22, fontWeight: '600'}}>{i18.NoTaskUpload}</Text>
				</View>
			)
		}else{
			if(pendingCheckList.length  > 0) {
				const styles = checkListStyles;
				showCheckList = pendingCheckList.map((item, i)=>{
					let itemDetail = item;

					let translatedUnitAddress;
					translatedUnitAddress = unit_address[itemDetail.type] || "";
							
					if(itemDetail.tower) {
						translatedUnitAddress = translatedUnitAddress.replace("{tower}", itemDetail.tower);
					}
			
					if(itemDetail.flat) {
						translatedUnitAddress = translatedUnitAddress.replace("{flat}", itemDetail.flat);
					}
			
					if(itemDetail.floor) {
						translatedUnitAddress = translatedUnitAddress.replace("{floor}", itemDetail.floor);
					}
			
					var date = (itemDetail.saved_at) ? itemDetail.saved_at : itemDetail._created_at;
					date = moment.utc(date).format('YYYY-MM-DD HH:mm');
					date = moment.utc(date).toDate();
					date = moment(date).local().format('YYYY-MM-DD HH:mm');

					return (
						<View style={styles.container} key={i}>
							<View style={styles.detail}>
								<View style={styles.address}>
									<Icon icon={'home'}  style={[styles.icon,{fontSize: 20}]} />
									<Text style={[styles.text, {fontWeight: 'bold'}]}>{translatedUnitAddress}</Text>
								</View>
			
								<View style={styles.info}>
									<Icon icon={'list-alt'} style={[styles.icon,{fontSize: 22}]} />
									<Text style={[styles.text, {fontSize: 12 }]}>{itemDetail.name}</Text>
									<View />
									<View />
								</View>
							</View>
			
							<View style={styles.dateContainer}>
								<Text style={styles.date}>{date}</Text>
								<Text style={[styles.state, {fontSize: 12 }]}>{i18.Satisfy + ": " + itemDetail.labelData.Satisfy + "/" + (itemDetail.labelData.Satisfy + itemDetail.labelData.FollowUp + itemDetail.labelData.Unfill) } {i18.FollowUp + ": " + itemDetail.labelData.FollowUp + "/" + (itemDetail.labelData.Satisfy + itemDetail.labelData.FollowUp + itemDetail.labelData.Unfill) }</Text>
							</View>
						</View>
					)

				});

			}
			if(pendingItem.length  > 0) {
				const styles = itemStyles;
				showItem = pendingItem.map((item, i)=>{

					let itemDetail = item;
					
					let translatedUnitAddress;
					translatedUnitAddress = unit_address[itemDetail.type] || "";
							
					if(itemDetail.tower) {
						translatedUnitAddress = translatedUnitAddress.replace("{tower}", itemDetail.tower);
					}
			
					if(itemDetail.flat) {
						translatedUnitAddress = translatedUnitAddress.replace("{flat}", itemDetail.flat);
					}
			
					if(itemDetail.floor) {
						translatedUnitAddress = translatedUnitAddress.replace("{floor}", itemDetail.floor);
					}
			
					var date = (itemDetail.saved_at) ? itemDetail.saved_at : itemDetail._updated_at;
					date = moment.utc(date).format('YYYY-MM-DD HH:mm');
					date = moment.utc(date).toDate();
					date = moment(date).local().format('YYYY-MM-DD HH:mm');

					let option;
					
					if (itemDetail.option.length > 16) {
						option = itemDetail.option.substring(0, 13) + '...'
					} else {
						option = itemDetail.option
					}

					return (
						<View style={styles.container} key={i}>
							<View style={styles.detail}>
								<View style={styles.address}>
									<View style={{flex: 1.8, flexDirection: 'row', alignItems:'center'}}>
										<Icon icon={'home'}  style={[styles.icon,{fontSize: 20}]} />
										<Text style={[styles.text, {fontWeight: 'bold', maxWidth: 200}]}>{translatedUnitAddress + " - " + itemDetail.location}</Text>
									</View>	
									<View style={{flex: 1, flexDirection: 'row', justifyContent:'space-between', alignItems:'center'}}>
										<Text style={styles.date}>{date}</Text>
									</View>	
								</View>

                                <View style={[styles.info, option === '滿意'? styles.infoBorderSatisfied: styles.infoBorderFollowup]}>
									<View style={{flex: 1.8}}>
                                        <Text style={[styles.text, option === '滿意'? styles.optionsSatisfied: styles.optionsFollowup]}>{option}</Text>
									</View>
									<View style={{flex: 1, flexDirection: 'row', justifyContent:'space-between', alignItems:'center'}}>	
										<Text style={[styles.text, {fontWeight: 'bold', color: '#767676'}]}>{itemDetail.level1}</Text>
										<Text style={[styles.text, {fontWeight: 'bold', color: '#767676', marginRight: 5}]}>{itemDetail.level2}</Text>
									</View>	
								</View>
							</View>
	
						</View>
					)
				});
			}
			if(pendingUnit.length  > 0) {
				const styles = checkListStyles;
				showUnit = pendingUnit.map((item, i)=>{
					let itemDetail = item;

					let translatedUnitAddress;
					translatedUnitAddress = unit_address[itemDetail.type] || "";
							
					if(itemDetail.tower) {
						translatedUnitAddress = translatedUnitAddress.replace("{tower}", itemDetail.tower);
					}
			
					if(itemDetail.flat) {
						translatedUnitAddress = translatedUnitAddress.replace("{flat}", itemDetail.flat);
					}
			
					if(itemDetail.floor) {
						translatedUnitAddress = translatedUnitAddress.replace("{floor}", itemDetail.floor);
					}
			
					var date = (itemDetail.saved_at) ? itemDetail.saved_at : itemDetail._created_at;
					date = moment.utc(date).format('YYYY-MM-DD HH:mm');
					date = moment.utc(date).toDate();
					date = moment(date).local().format('YYYY-MM-DD HH:mm');

					return (
						<View style={styles.container} key={i}>
							<View style={styles.detail}>
								<View style={styles.address}>
									<Icon icon={'home'}  style={[styles.icon,{fontSize: 20}]} />
									<Text style={[styles.text, {fontWeight: 'bold'}]}>{translatedUnitAddress}</Text>
								</View>
			
								<View style={styles.info}>
									<Icon icon={'list-alt'} style={[styles.icon,{fontSize: 22}]} />
									<Text style={styles.text}>{itemDetail.stage}</Text>
									<View />
									<View />
								</View>
							</View>
			
							<View style={styles.dateContainer}>
								<Text style={styles.date}>{date}</Text>
								<Text style={styles.state}></Text>
							</View>
						</View>
					)

				});

			}
			
		}

		if(pendingKey.length > 0){
			const styles = checkListStyles;
			showKey = pendingKey.map((item, i)=>{
				let itemDetail = item;

				let translatedUnitAddress;
				translatedUnitAddress = unit_address[itemDetail.type] || "";
					
				if(itemDetail.tower) {
				  translatedUnitAddress = translatedUnitAddress.replace("{tower}", itemDetail.tower);
				}
			
				if(itemDetail.flat) {
				  translatedUnitAddress = translatedUnitAddress.replace("{flat}", itemDetail.flat);
				}
			
				if(itemDetail.floor) {
				  translatedUnitAddress = translatedUnitAddress.replace("{floor}", itemDetail.floor);
				}
				
		
				var date = (itemDetail.saved_at) ? itemDetail.saved_at : itemDetail._created_at;
				date = moment.utc(date).format('YYYY-MM-DD HH:mm');
				date = moment.utc(date).toDate();
				date = moment(date).local().format('YYYY-MM-DD HH:mm');

				return (
					<View style={styles.container} key={i}>
						<View style={styles.detail}>
							<View style={styles.address}>
								<Icon icon={'home'}  style={[styles.icon,{fontSize: 20}]} />
								<Text style={[styles.text, {fontWeight: 'bold'}]}>{translatedUnitAddress}</Text>
							</View>
		
							<View style={styles.info}>
								<Icon icon={'list-alt'} style={[styles.icon,{fontSize: 22}]} />
								<Text style={styles.text}>{i18.keyManagement}</Text>
								<View />
								<View />
							</View>
						</View>
		
						<View style={styles.dateContainer}>
							<Text style={styles.date}>{date}</Text>
							<Text style={styles.state}></Text>
						</View>
					</View>
				)

			});

		}

		let showUplaodButton = pendingCheckList.length > 0 ||  pendingItem.length > 0 ||  pendingUnit.length > 0 || pendingKey.length > 0 ? (
			<View style={{height: 50, alignItems: 'center', justifyContent: 'center', borderTopColor: '#AEAEAE', borderTopWidth: 1}}>
			<Button 
				style={styles.button} 
				textStyle={styles.buttonText}
				onPress={()=>{
					let combineArray = [];
					let checklistObject = {
						data: pendingCheckList,
						type: 'checklist',
					};
					let itemObject = {
						data: pendingItem,
						type: 'item',
					};
					let unitObject = {
						data: pendingUnit,
						type: 'unit',
					};
					let keyObject = {
						data: pendingKey,
						type: 'key',
					};
					combineArray.push(checklistObject);
					combineArray.push(itemObject);
					combineArray.push(unitObject);
					combineArray.push(keyObject);

					if(connectionInfo == 'wifi' || connectionInfo == 'wimax' || connectionInfo == 'ethernet') {
						props.uploadTask(combineArray);
					} else if(connectionInfo == 'none'){
						Alert.alert(
						i18.noInternetConnection,
							'',
							[
								{text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
							]
						)
					} else {
						Alert.alert(
						i18.noWifiUpload,
							'',
							[
								{text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
								{text: i18.WaittingForUpload , onPress: () => {
									props.uploadTask(combineArray);
								}},
							]
						)
					}
					
				}}>
				{i18.WaittingForUpload}
			</Button>
		</View>
		) : null;
		
		let showUploadList;
		if(pendingCheckList.length  > 0 || pendingItem.length > 0 || pendingUnit.length > 0 || pendingKey.length > 0) {
			showUploadList = (
				<ScrollView>
					{showCheckList}
					{showItem}
					{showUnit}
					{showKey}
				</ScrollView>
			)
		}

		// const styles = styles;
		return (
			<View style={styles.container}>
				<Nav onRightPress={this.onDownload}>
					{i18.Upload}({pendingCheckList.length + pendingItem.length + pendingUnit.length + pendingKey.length})
				</Nav>
				{showNoTask}
				{showUploadList}
				{showUplaodButton}
			</View>
		);
	}
}

const checkListStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: Theme.item.bg,
		justifyContent:'space-between',
		padding: 9,
		marginHorizontal: 5,
		marginTop: 8,
		shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,

	},
	icon: {
		color: '#484848',
		width: 35,
		marginBottom: 5,
		backgroundColor:'transparent',
		textAlign:'center',
	},
	detail: {
		flex:1,
		justifyContent:'space-between',	
	},
	address: {
		flex:1,
		flexDirection: 'row',
		alignItems:'center',		
	},
	info: {
		flex:1,		
		flexDirection: 'row',
		alignItems:'center',
	},
	text: {
		paddingHorizontal: 3,
		color: Theme.item.text,
		fontSize:14,
	},
	bubble: {
		marginHorizontal: 2,
		paddingHorizontal: 6,
		paddingVertical: 3,
		borderRadius: 10,
		alignItems: 'center',
		justifyContent: 'center'
	},
	bubbleText: {
		fontSize:12,
		color: Theme.bubble.text,
	},
	draft: {
		backgroundColor: Theme.bubble.draft,
	},
	extraList: {
		backgroundColor: Theme.bubble.extra
	},
	dateContainer: {
		alignItems: 'flex-end',
		justifyContent: 'space-around',
	},
	date: {	
		fontSize:14,
		color: Theme.item.date,
	},
	state:{
		fontSize:14,
		color: Theme.item.state,
	},
});

const itemStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: Theme.item.bg,
		justifyContent:'space-between',
		marginHorizontal: 5,
		marginTop: 6,
		shadowColor: '#9f9f9f',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.9,
		shadowRadius: 2,
		minHeight: 84,

	},
	icon: {
		color: '#484848',
        width: 35,
		backgroundColor:'transparent',
		textAlign:'center',
	},
	detail: {
		flex: 4.5,
	},
	address: {
		flex:1,
		flexDirection: 'row',
		alignItems:'center',
		paddingLeft: 5,	
	},
	info: {
		flex:1,		
		flexDirection: 'row',
		alignItems:'center',
		backgroundColor: '#F5F5F5',
		borderRightWidth: 1,
		borderRightColor: '#FFFFFF',
		borderBottomWidth: 3,
		// borderBottomColor: '#C77777',
		paddingLeft: 5,	
	},
    infoBorderSatisfied: {
        borderBottomColor: '#69b29d',
    },
    infoBorderFollowup: {
        borderBottomColor: '#C77777',
    },
	text: {
		paddingHorizontal: 3,
		color: Theme.item.text,
		fontSize:14,
	},
	date: {	
		fontSize:14,
		color: Theme.item.date,
	},
    optionsSatisfied: {
        color: '#69b29d'
    },
    optionsFollowup: {
        color: '#C77777'
    },
});


const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	list: {
		flex: 1,
	},
	button: {
		borderRadius: 5,
		marginHorizontal: 8,
		paddingVertical: 6,
		backgroundColor: '#FD8040',
	},
	buttonText: {
		fontSize: 16,
		fontWeight: '500',
		color: '#FFFFFF',
	},
});

const mapStateToProps = (state, ownProps) => {
	let unit_address;
	if(state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0){
		if(state.setting.language == 'en') {
			unit_address = state.project.projectDetail.unit_address_en;
		}else if(state.setting.language == 'zh_hk') {
			unit_address = state.project.projectDetail.unit_address_zh;
		}else {
			unit_address = state.project.projectDetail.unit_address_cn;
		}
	}

	return {
		language: state.setting.language,
		pendingCheckList: state.checklists.pendingCheckLists,
		pendingItem: state.items.pendingItems,
		pendingUnit: state.units.pendingUnits,
		pendingKey: state.project.pendingKey,
		unit_address: unit_address,	
		connectionInfo: state.setting.connectionInfo,			
	}
}

import { 
	CheckLists as checkListsMapDispatchToProps,
	Items as itemsMapDispatchToProps,
	Project as projectMapDispatchToProps, 
	Upload as uploadMapDispatchToProps,
	Units as unitsMapDispatchToProps,
} from 'Controller';

let checkListsProps;
let itemsProps;
let projectProps;
let uploadProps;
let unitsProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
	itemsProps = itemsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	uploadProps = uploadMapDispatchToProps(dispatch, ownProps)
	unitsProps = unitsMapDispatchToProps(dispatch, ownProps)
	return {...checkListsProps, ...projectProps, ...uploadProps, ...itemsProps, ...unitsProps};
})(Upload);
