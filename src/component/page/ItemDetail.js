import React, { Component } from 'react';
import {
	StyleSheet,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Animated,
	ScrollView,
	Dimensions,
	Keyboard,
	Platform,
	NetInfo,
	Alert,
} from 'react-native';
const TextInput = require('CustTextInput');
import { connect } from 'react-redux'
import Text from 'react-native-text';

import { taskList as Theme, checkList as checkListTheme } from 'Theme';
import { Icon, Nav, DropDownList, Button } from 'Item';
import i18 from 'i18';
import ScrollableTabView, { DefaultTabBar, } from 'react-native-scrollable-tab-view';
import ActionSheet from 'react-native-actionsheet';
import moment from 'moment';

import CachedImage  from 'react-native-cached-image';
// const {
//     ImageCacheProvider
// } = CachedImage;
import { _getServerUrl } from 'Request';

const CANCEL_INDEX = 0;
const DESTRUCTIVE_INDEX = 4;

class TabBar extends Component {

	renderTab = (item, page) => {
		const styles = tabStyles;

		let { activeTab, tabs, goToPage } = this.props;		
		var isTabActive = activeTab === page;

		return (
			<TouchableOpacity key={item.label} onPress={() => goToPage(page)} style={styles.tab}>
				<Text style={[styles.text]}>
					{ i18[item.label] }
				</Text>
			</TouchableOpacity>
		);
	}

	render() {
		const styles = tabStyles;
		
		let { containerWidth, tabs, scrollValue } = this.props;
		let left = scrollValue.interpolate({ inputRange: [0, 1], outputRange: [0, containerWidth / tabs.length ] });

		return (
			<View style={styles.container}>
				<View style={styles.tabs}>
					{ tabs.map(this.renderTab) }
				</View>
				<Animated.View style={[styles.line, { width:containerWidth / tabs.length, left }]} />
			</View>
		);
	}
}

const tabStyles = StyleSheet.create({
	container:{
		height:50,
		borderBottomWidth:1,
		borderColor:'#888',
	},
	tab: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
	},
	tabs: {
		flex:1,
		flexDirection: 'row',
		justifyContent: 'center',
		backgroundColor:'#fff',
	},
	text:{
		fontSize: 14
	},
	line: {
		position: 'absolute',
		height: 3,
		backgroundColor: '#000',
		bottom: 0,
	}
});


export default class ItemDetail extends Component {

	constructor(props) {
		super(props);
		this.state = {
			keyboardSpace: 0,
			isKeyboardOpened: false,
			text: "",
			status: false,
			options: [ i18.Cancel ]
		};
		this._listeners = null;
		this.updateKeyboardSpace = this.updateKeyboardSpace.bind(this);
		this.resetKeyboardSpace = this.resetKeyboardSpace.bind(this);
	}

	componentWillMount(){
		let { props } = this;
		let { projectDetail, user } = props;
		let tempOptions = [];
        let disabledOptions = [];
        tempOptions.push(i18.Cancel);
		let secondArray = user.roles;

        var result = Promise.all(projectDetail.defect_item_status.map((item)=>{
			let firstArray = item.acl;
			let found = firstArray.some(r=> secondArray.includes(r));
            if(found){
                tempOptions.push(item.name);
            }
            if(item.name === this.props.item.status){
                disabledOptions = item.disabledOptions || [];
            }
		})).then(()=> {
            return tempOptions.filter(word => (disabledOptions.indexOf(word) === -1)) || [];
		}).then(options => {
            this.setState({options});
        })

		result.then();
	}

	componentDidMount() {
		const updateListener = Platform.OS === 'android' ? 'keyboardDidShow' : 'keyboardWillShow';
		const resetListener = Platform.OS === 'android' ? 'keyboardDidHide' : 'keyboardWillHide';
		this._listeners = [
			Keyboard.addListener(updateListener, this.updateKeyboardSpace),
			Keyboard.addListener(resetListener, this.resetKeyboardSpace)
		];
		NetInfo.isConnected.addEventListener('change', this.handleConnectionChange);
		
		NetInfo.isConnected.fetch().done(
			(isConnected) => { this.setState({ status: isConnected }); }
		);
	}

    getDisplayName(userId) {
        if (userId in this.props.users) {
            return this.props.users[userId];
        }

        return userId;
    }

	handleConnectionChange = (isConnected) => {
		let { props } = this;
		let { item, user } = props;

		this.setState({ status: isConnected });
		
		if(isConnected){
			item.messages.forEach((message)=>{
				if(message.offline) {
					props.addMessage(message.content, item._id, isConnected, user);
				}
			})
		}
        console.log(`is connected: ${this.state.status}`);
	}

	componentWillUnmount() {
		this._listeners.forEach(listener => listener.remove());
		NetInfo.isConnected.removeEventListener('change', this.handleConnectionChange);
	}

	updateKeyboardSpace(event) {
		if (!event.endCoordinates) {
			return;
		}
		// get updated on rotation
		const screenHeight = Dimensions.get('window').height;
		// when external physical keyboard is connected
		// event.endCoordinates.height still equals virtual keyboard height
		// however only the keyboard toolbar is showing if there should be one
		const keyboardSpace = screenHeight - event.endCoordinates.screenY ;
		this.setState({
			keyboardSpace,
			isKeyboardOpened: true
		});
	}
	
	resetKeyboardSpace(event) {

		this.setState({
			keyboardSpace: 0,
			isKeyboardOpened: false
		});
	}

	renderCheckResult(label) {
		const styles = listStyles;
		let { props } = this;
		let { item, user } = props;
		
		let option = item.option.split('/').map(function(item) {
			return item.trim();
		});

		var date = item._updated_at;
		date = moment.utc(date).format('YYYY-MM-DD HH:mm');
		date = moment.utc(date).toDate();
		date = moment(date).local().format('YYYY-MM-DD HH:mm');

		var comments = item.comments ? (
			<View style={{marginTop: 5, padding: 5}}>
				<Text>
					評語: {item.comments}
				</Text>	
			</View>
		) : null;
		
		return (
			<View style={styles.container} tabLabel={{label}}>
				<ScrollView style={[styles.list]}>
					{option.map(this.renderOption)}
				</ScrollView>
				<View style={{marginTop: 5, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', padding: 5}}>
					<Text style={{color: '#9D9D9D', fontSize: 14}}>{i18.lastModfiy}</Text>
					<Text style={{color: '#9D9D9D', fontSize: 14}}>{date}</Text>
					<Button 
						style={styles.iconButton} 
						textStyle={styles.iconButtonText}
						onPress={() => {
							// console.log(this.props.item);
							this.props.navigation.navigate('FloorPlan', { floor_plans: this.props.floor_plans, item: this.props.item });
						}}>{i18.FloorPlan}</Button>
				</View>	
				{comments}
			</View>	
		);
	}

	renderComment(label) {
		const styles = listStyles;
		let { props } = this;
		let { item, user } = props;

		let showMessage;
		if(item.messages && item.messages.length > 0){
			showMessage = (
				<ScrollView>
					{item.messages.map((val)=>this.renderComments(val))}
					<View style={{height: 10}}/>
				</ScrollView>
			)
		}else {
			showMessage = (
				<View style={{flex: 1}} />
			)
		}

		return (
			<View style={[styles.container, {flex: 1}]} tabLabel={{label}}>
				{showMessage}
				<View style={{ 
				borderTopWidth: 1, height: 51, width: '100%', backgroundColor: Theme.bg,
				borderColor: '#AEAEAE', alignItems: 'center', justifyContent: 'center'}}>
					<View style={{flexDirection: 'row', alignItems: 'center', width: '95%'}}>
						<TextInput
							underlineColorAndroid='transparent'
							style={{
								width: "78%",
								height: 31,
								borderColor: 'gray', borderWidth: 0.5, borderRadius: 3, 
								marginLeft: 11, backgroundColor: "#FFFFFF", fontSize: 16,
								padding: 5 }}
							blurOnSubmit={false} 
							autoFocus={false} 
							autoCorrect={false} 
							onChangeText={(text) => this.setState({text})}
							value={this.state.text}
							multiline={true}
						/>
						<TouchableOpacity 
							onPress={()=>{
								if(this.state.text){
									props.addMessage(this.state.text, item._id, this.state.status, user);
									this.setState({text: ""});
								}
							}}
							style={{width: "22%", alignItems: 'center', justifyContent: 'center'}}>
							<View>
								<Text>Send</Text>
							</View>	
						</TouchableOpacity>	
					</View>
				</View>	
				{Platform.OS === 'android' ? null : <View style={{height: this.state.keyboardSpace}}/> }
			</View>	
		)
	}

	renderPhoto(label) {
		const styles = listStyles;
		let { props } = this;
		let { item } = props;

		let containerWidth = Dimensions.get('window').width / 3 - 2;
		let oldphoto;
		if(item.pictures.length > 0) {

			let formatResult = [], map = {}, idx = 0;
			for(let element of item.pictures){
				let curr = 0;
				if(map[element.date] !== undefined){
					curr = map[element.date];
				}
				else{
					curr = idx;
					map[element.date] = idx++;
					formatResult.push({data : [], date : element.date});
				}
				formatResult[curr].data.push({
					path: element.path,
					caption: element.caption,
					user: element.user,
				});
			}

			oldphoto = formatResult.map((pic, i)=>{

				let eachItem = pic.data.map((p, index)=> {
					let image = p.path;
					if (!image.startsWith("http")) {
						image = _getServerUrl() + "blob_image/" + image;
					}
					let caption = p.caption;
					return (
						<TouchableOpacity 
							key={index} 
							style={{width: containerWidth, height: containerWidth, alignItems: 'center', justifyContent: 'center'}}
							onPress={() => {
								this.props.navigation.navigate('PhotoView', {source: image, caption: caption});
							}}>
							<CachedImage
								source={{uri: image}}
								style={{width: '90%', height: '90%'}} >
								<View style={styles.photoLabel}>
									<Text style={{fontSize: 12, marginLeft: 3}}>{i18.photo + " " + index + 1}</Text>
								</View>
							</CachedImage>
						</TouchableOpacity>
					)
				});

				var date = pic.date;
				date = moment.utc(date).format('YYYY-MM-DD HH:mm');
				date = moment.utc(date).toDate();
				date = moment(date).local().format('YYYY-MM-DD HH:mm');

				return (
					<View key={i} style={{marginTop: 6}}>
						<Text style={{color: '#A8A8A8', fontSize: 14}}>{date}</Text>
						<View style={{marginTop: 5, flexDirection: 'row', flexWrap: 'wrap', alignItems: 'flex-start'}}>
							{eachItem}
						</View>
					</View>	
				)
			})
		}

		return (
			<View style={styles.container} tabLabel={{label}}>
				<ScrollView>
					<View style={{marginTop: 6}}>
						<View style={{marginTop: 5, flexDirection: 'row', flexWrap: 'wrap', alignItems: 'flex-start'}}>
							{
								(item.images && item.images.length > 0) ? (
									item.images.map((image, idx) => {
										if(!image || !image.path) {
											return null;
										} else {
                                            let path = image.path;
                                            if (path.indexOf('file://') == -1) {
                                                path = 'file://' + path;
                                            }
                                            let title = i18.Photo + " " + (idx + 1);
                                            return (
                                                <TouchableOpacity
                                                    key={idx}
                                                    style={{
                                                        width: containerWidth,
                                                        height: containerWidth,
                                                        backgroundColor: 'transparent',
                                                        alignItems: 'center',
                                                        justifyContent: 'center'
                                                    }}
                                                    onPress={() => {
                                                        this.props.navigation.navigate('EditPhoto', {
                                                            item: this.props.item,
                                                            currentIndex: idx
                                                        })
                                                    }}>
                                                    <Image
                                                        style={{width: '90%', height: '90%'}}
                                                        source={{uri: path}}>
                                                        <View style={styles.photoLabel}>
                                                            <Text style={{fontSize: 12, marginLeft: 3}}>{title}</Text>
                                                        </View>
                                                    </Image>
                                                </TouchableOpacity>
                                            );
                                        }
									})
								) : null
							}
							<TouchableOpacity 
								style={{width: containerWidth, height: containerWidth, alignItems: 'center', justifyContent: 'center'}}
								onPress={() => {
									this.props.navigation.navigate('EditPhoto', {item: this.props.item, forceAdd: true})
								}}>
								<View style={{backgroundColor: '#D8D8D8', width: '90%', height: '90%', alignItems: 'center', justifyContent: 'center'}}>
									<Icon icon={'TakePhoto'} style={{
										width: 30,
										fontSize: 26,
										backgroundColor:'transparent',
										color: '#3C3C3C',
										textAlign:'center',
									}} />
									<Text style={{fontSize: 12, marginLeft: 3, marginTop: 5}}>{i18.NewPhoto}</Text>
								</View>
							</TouchableOpacity>	
						</View>
					</View>

					{oldphoto}

				</ScrollView>	
			</View>	
		);
	}

	renderList(label) {
		
		const styles = listStyles;
		let { props } = this;
		let { item, user } = props;

		let option = item.option.split('/').map(function(item) {
			return item.trim();
		});

		var date = item._updated_at;
		date = moment.utc(date).format('YYYY-MM-DD HH:mm');
		date = moment.utc(date).toDate();
		date = moment(date).local().format('YYYY-MM-DD HH:mm');

		if(label == 'CheckResult'){
			return this.renderCheckResult(label);
		}else if(label == 'Comment') {
			return this.renderComment(label);
		} else if(label == 'Photo') {
			return this.renderPhoto(label);
		} else {
			return (
				<View style={styles.container} tabLabel={{label}}>
					<ScrollView contentContainerStyle={{paddingVertical: 10}}>
						{item.status_history.map((val, idx)=>this.renderStatusHistory(val, idx))}
					</ScrollView>
				</View>	
			)
		}
	}


	showActionSheet() {
		this.ActionSheet.show()
	}
	
	handlePress(i) {
		let { props } = this;
		let { item, filterData } = props;
		if(i != 0){
			let option = this.state.options[i];

			let newArray = [];
			newArray.push(item._id);

			props.updateSelectOptions(newArray, option);

			let sectionData = filterData.map((section, i) => {
				if(i == 0) {
					section.fake = section.fake ? false : true;
				}
				return section;   
			});
			props.fakeUpdateItemData(sectionData);
		}
	}


	onSubmit() {
		let { props } = this;	
		let { item } = props;
		Alert.alert(
			i18.AskForSubmitItem,
			'',
			[
				{text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
				{text: i18.OK, onPress: () => {
					props.pendingItems(item);
					this.props.navigation.goBack();
				}},
			]
		)
	}

	renderStatusHistory = (val, idx) => {
		let { props } = this;	
		let { item } = props;
		let oldStatus = <Text style={{fontSize: 14}}> - </Text>;
		if(item.status_history.length > 1 && typeof item.status_history[idx + 1]  !== 'undefined'){
			oldStatus = <Text style={{fontSize: 14}}>{item.status_history[idx + 1].status}</Text>
		}

		return (
			<View key={idx} style={{flexDirection: 'row', height: 50, marginTop: 3}}>
				<View style={{flex: 2, 
					flexDirection: 'row', 
					}}>
					<View style={{
						flex: 1, 
						alignItems: 'center', 
						justifyContent: 'center',
						borderBottomWidth: 2, 
						borderColor: '#CACACA'}}>
						{oldStatus}
					</View>	
					<View style={{width: 1, backgroundColor: '#CACACA', 
						borderBottomWidth: 2, 
						borderColor: '#CACACA'}}/>
					<View style={{
						flex: 1, 
						alignItems: 'center', 
						justifyContent: 'center',
						borderBottomWidth: 2, 
						borderRadius: 4,
						borderColor: '#CACACA',
						borderRightWidth: 2, 
						}}>
						<Text style={{fontSize: 14}}>{val.status ? val.status : " - " }</Text>
					</View>
				</View>		
				<View style={{flex: 1.2, alignItems: 'center', justifyContent: 'center'}}>
					<View style={{width: "90%"}}>
					<Text style={{textAlign: 'right', fontSize: 14, color: '#A8A8A8'}}>{this.getDisplayName(val.user)}</Text>
					<Text style={{textAlign: 'right', fontSize: 14, color: '#A8A8A8'}}>{moment.utc(val.date).format('YYYY-MM-DD HH:mm')}</Text>
					</View>	
				</View>	
			</View>	
		);

	}

	renderComments = (val, idx)=>{
		var date = val.date;
		date = moment.utc(date).format('YYYY-MM-DD HH:mm');
		date = moment.utc(date).toDate();
		date = moment(date).local().format('YYYY-MM-DD HH:mm');

		let didNotUpload = val.offline ?  (
			<Text style={{padding: 5}}>未上傳</Text>
		) : <View/>;

		return (
			<View key={idx} style={listStyles.comments}>
				<View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
					<Text style={{padding: 5}}>{val.content}</Text>
					{didNotUpload}
				</View>
				<View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
					<Text style={{color: '#A8A8A8', padding: 5}}>{date}</Text>
					<Text style={{color: '#A8A8A8', padding: 5}}>{this.getDisplayName(val.user)}</Text>
				</View>
			</View>
		);
	}

	renderOption = (val, idx)=>{
        // const styles = styles;
		return (
			<View key={idx} style={[styles.option, val === '滿意'? styles.optionSatisfied: styles.optionFollowup]}>
				<Text style={[styles.optionText, {color: '#FFFFFF'}]}>{val}</Text>
			</View>
		);
	}

	render() {
		let { props } = this;

		let { projectDetail, item } = props;

		return (
			<View style={styles.container}>	
				<Nav 
					onRightPress={()=>this.onSubmit()} 
					right="SUBMIT"
					onLeftPress={()=>this.props.navigation.goBack()} 
					left="CLOSE">
					{i18.Details}
				</Nav>
				<View style={styles.header}>
					<Text style={{width: '60%', fontWeight: '600', fontSize: 14}}>{item.description}</Text>
					<View style={{width: 3, height: 32, backgroundColor: '#9C9C9C'}}/>
					<TouchableOpacity 
					onPress={()=> this.ActionSheet.show()}
					style={{width: "20%", flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
						<Text style={{fontWeight: '600', fontSize: 14, textAlign: 'center' }}>{item.status ? item.status : " --- "}</Text>
						<Icon style={{ fontSize: 8, color: '#333333', textAlign: 'center' }} icon={"DROPDOWN"}/>
					</TouchableOpacity>	
				</View>
				<ScrollableTabView
					tabBarPosition={'top'}
					style={styles.list}
					prerenderingSiblingsNumber={4}
					showDropDown={true}
					contentProps={{keyboardDismissMode: 'interactive', keyboardShouldPersistTaps: 'handled'}}
					renderTabBar={() => <TabBar />}>
					{this.renderList('CheckResult')}
					{this.renderList('Comment')}
					{this.renderList('Photo')}
					{this.renderList('StatusHistory')}
				</ScrollableTabView>
				

				<ActionSheet
					ref={o => this.ActionSheet = o}
					title={i18.ChangeStatus}
					options={this.state.options}
					cancelButtonIndex={CANCEL_INDEX}
					onPress={(i) => this.handlePress(i)}
       			/>
			</View>
		);
	}
}

const listStyles = StyleSheet.create({
	container: {
		padding: 3
	},
	iconButton:{
		paddingHorizontal: 0,
		paddingVertical: 0,
		borderColor: 'gray', 
		borderWidth: 0.5, 
		borderRadius: 3, 
		backgroundColor: 'white'
	},
	iconButtonText:{
		color: 'rgba(255, 127.5, 51, 1)', 
		padding: 5, 
		backgroundColor: 'transparent'
	},
	comments: {
		backgroundColor: '#FFFFFF',
		padding: 10,
		marginHorizontal: 5,
		marginTop: 6,
		shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,
	},
	photoLabel: {
		backgroundColor: '#rgba(255, 255, 255, 0.7)', 
		height: 23, 
		width: '90%', 
		justifyContent: 'center', 
		top: "5%", 
		position: 'absolute'
	}
});

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	header:{
		flexDirection: 'row',
		paddingHorizontal: 5,
		paddingVertical: 10,
		alignItems: 'center',
		justifyContent: 'space-around',
	},
	list: {
		flex: 1
	},
	option:{
		flex: 1,
		borderBottomWidth: 1,
	},
	optionText:{
		padding: 20,
	},
    optionSatisfied: {
        backgroundColor: '#69b29d',
        borderBottomColor: '#FFFFFF'
    },
    optionFollowup: {
        backgroundColor: '#C77777',
        borderBottomColor: '#FFFFFF'
    },
});

const mapStateToProps = (state, ownProps) => {
	let unit_address;
	if(state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0){
		if(state.setting.language == 'en') {
			unit_address = state.project.projectDetail.unit_address_en;
		}else if(state.setting.language == 'zh_hk') {
			unit_address = state.project.projectDetail.unit_address_zh;
		}else {
			unit_address = state.project.projectDetail.unit_address_cn;
		}
	}

	return {
		projectDetail: state.project.projectDetail,
		user: state.auth.user,
		language: state.setting.language,
		item: state.items.item,
		unit_address: unit_address,
		filterData: state.items.filterData.filter(item => item.status_history),
		floor_plans: state.project.floor_plans,
		users: state.project.users
	}
}


import { Items as itemsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';

let itemsProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	itemsProps = itemsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {...itemsProps, ...projectProps};
})(ItemDetail);
