import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	ScrollView
} from 'react-native';
import { connect } from 'react-redux'

import Server from 'Server';

import { Icon } from 'Item';
const Button = require('Item').Button;
const Actions = require('Redux').Action.root;

import CachedImage from 'react-native-cached-image';
const ImageCacheProvider = CachedImage.ImageCacheProvider;
import SignatureCapture from 'cust/react-native-signature-capture';

import { _getServerUrl } from 'Request';

export default class PhotoView extends Component {

	constructor(props) {
		super(props);
		this.state = { 
			showText: true,
			imgPath: null
		};
	}

	componentDidMount() {
		let path = this.props.navigation.state.params.source;
		if (!path.startsWith("http")) {
			path = _getServerUrl() + "blob_image/" + path;
		}
		ImageCacheProvider.getCachedImagePath(path).then((p) => {
			if (p.indexOf('file://') == -1) {
				p = 'file://' + p;
			}
			this.setState({
				imgPath: p,
				caption: this.props.navigation.state.params.caption
			});
		});
	}

	static navigationOptions = {
		tabBarLabel: 'Home',
		tabBarIcon: ({ tintColor }) => (
			<Image
				source={require('Asset/Image').icon}
				style={{ width: 40, height: 40 }}
			/>
		),
	}
	render() {
		let { props } = this;

		// let file = props.navigation.state.params.source
		return (
			<View style={styles.container}>
      			<View style={{flex:10, backgroundColor: 'transparent'}}>
					{/* <CachedImage
						resizeMode="contain"
						source={{ uri: this.props.navigation.state.params.source }}
						style={{ width: '100%', height: '100%', backgroundColor:'transparent' }}
					/> */}

					{
						(this.state.imgPath) ? (
							<SignatureCapture
								style={{ flex: 1 }}
								saveImageFileInExtStorage={true}
								imageFilePath={this.state.imgPath}
								editMode={false}
								isDrawing={false}
								isErase={false}
								floorPlanMode={false}
							/>
						) : <View style={{ flex: 1 }} />
					}
				</View>
				{
					(this.state.caption) ? (
						<View style={{ padding: 5, position: 'absolute', justifyContent: 'center', bottom: 0, left: 0, right: 0, backgroundColor:'rgba(0,0,0,0.5)', flex: 1, height: 30}}>
							<Text style={{color: "white"}}>{this.state.caption}</Text>
						</View>
					) : null
				}
				<View style={{ margin: 10, alignItems: 'center', justifyContent: 'center', position: 'absolute', top: 0, bottom: 0, left: 0, right: 0, backgroundColor:'rgba(255,255,255,0.5)', width: 40, height: 40}}>
					<TouchableOpacity onPress={() => {
							this.props.navigation.goBack();
						}}>
						{/* <Icon style={{fontSize: 25, color: 'black', width: 50, backgroundColor:'transparent', textAlign:'center'}} icon={'BACK'}/> */}
						<Text style={{fontSize: 25, color: 'black', fontWeight: 'bold'}}>{"<"}</Text>
					</TouchableOpacity>
				</View>
			</View>
		);
	}
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: 'black',
	},
	icon: {
		width: 30,
		height: 30,
	},
});

const mapStateToProps = (state, ownProps) => {
	return {
	}
}

const mapDispatchToProps = (dispatch, ownProps) => {
	return {
	}
}

module.exports = connect(mapStateToProps, mapDispatchToProps)(PhotoView);