import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Dimensions,
	ScrollView,
	Alert
} from 'react-native';
import { connect } from 'react-redux'

import { taskList as Theme, nav } from 'Theme';
import { Icon, Nav, DropDownList } from 'Item';
import i18 from 'i18';
import moment from 'moment';
import Camera from 'react-native-camera';

export default class ReadQRcode extends Component {

	constructor(props) {
		super(props);

		this.state = {
			scanned: false,
			firstScan : false,
			secondScan: false,
			tower: '',
			floor: '',
			flat: ''
		};
	}

	componentWillMount() {
		let { props } = this;
		props.cameraStatus(true);
	}


    onSkip() {
		let { props } = this;
		
		if(this.props.navigation.state.params.fromScreen == "unit"){
			props.updateQRcode("", true);
		}else if(this.props.navigation.state.params.fromScreen == "others") {
            let data = {
                tower: this.state.tower,
                floor: this.state.floor,
                flat: this.state.flat,
                current_owner: "",
                mobile: "",
                key_status: "",
                unit_status: "",
                dialing_code: "",
                reason: "",
                remark: "",
            };

			props.updateKeyQRcode(data, true);
		}
		props.cameraStatus(false);
		this.props.navigation.goBack();
    }

    _onBarCodeRead(result) { 
		let { props } = this;
		let { projectDetail } = props;

		if(!this.state.scanned && result.data && this.props.navigation.state.params.fromScreen == "unit"){
			props.updateQRcode(result.data, true);
			this.props.navigation.goBack();
			this.setState({scanned: true});
		}


		if(!this.state.firstScan && !this.state.secondScan && result.data && this.props.navigation.state.params.fromScreen == "others"){
			
			let splitData = result.data.split('_');

			if(splitData[0] == projectDetail.code){

				Alert.alert(
					i18.tips,
					'已掃描門匙二維碼，接下來是掃描二維碼',
					[
						{text: i18.OK, onPress: () => {
						
						}},
					]
				)
				this.setState({
					tower: splitData[1],
					floor: splitData[2],
					flat: splitData[3],
					firstScan: true,
				});
			}

		}

		if(this.state.firstScan && !this.state.secondScan && result.data && this.props.navigation.state.params.fromScreen == "others") {

			let phoneNumber;
			if(!result.data.includes('_')){
				let split_string = result.data.split(/(\d+)/);
				phoneNumber = result.data.replace(split_string[0], "");
                phoneNumber = phoneNumber.replace(" ", "");
                
                let data = {
                    tower: this.state.tower,
					floor: this.state.floor,
                    flat: this.state.flat,
                    current_owner: split_string[0],
                    mobile: phoneNumber,    
                    key_status: "",
                    unit_status: "",
                    dialing_code: "",
                    reason: "",
                    remark: "",
                };

                props.updateKeyQRcode(data, true);

				this.setState({
					secondScan: true,
				});
	
				this.props.navigation.goBack();
			}
		}
	}
	

	render() {

		let { props } = this;
		let { units } = props;

		return (
			<View style={styles.container}>
                <Camera onBarCodeRead={(result)=> this._onBarCodeRead(result)} style={styles.camera}>
                    <Nav 
                        onRightPress={()=>this.onSkip()} 
                        right={this.props.navigation.state.params.fromScreen == "unit" || this.state.firstScan ? "SKIP" : ""}
                        onLeftPress={() => {
							props.cameraStatus(false);
							this.props.navigation.goBack()
						}} 
                        left="BACK">
                        { this.props.navigation.state.params.fromScreen == "unit" ||  this.state.firstScan ? i18.scanQRcode : i18.scanKeyQRcode}
                    </Nav>

                </Camera>
			</View>
		);
	}
}


const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	list: {
		flex: 1,
		width: '100%'
	},
	camera: {
		flex: 1,
	}
});

const mapStateToProps = (state, ownProps) => {
	return {
		projectDetail: state.project.projectDetail,
        language: state.setting.language,
	}
}

import { Units as unitsMapDispatchToProps, Project as projectMapDispatchToProps,  Others as othersMapDispatchToProps } from 'Controller';

let projectProps;
let unitsProps;
let othersProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps) => {
	unitsProps = unitsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	othersProps = othersMapDispatchToProps(dispatch, ownProps)
	return { ...unitsProps, ...projectProps, ...othersProps };
})(ReadQRcode);
