import React, { Component } from 'react';
import {
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  Alert,
  Dimensions,
  ScrollView,
  FlatList
} from 'react-native';
const TextInput = require('CustTextInput');
import { connect } from 'react-redux'
import LocalDB from 'LocalDB';

const Button = require('Item').Button;
const i18 = require('i18');
import { taskList as Theme } from 'Theme';
import { Icon, Nav } from 'Item';

import CachedImage from 'react-native-cached-image';
const {
    ImageCacheProvider
} = CachedImage;
import Text from 'react-native-text';

import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import ModalDropdown from 'react-native-modal-dropdown';
import FormItem from './FormItem'
import Server from 'Server';

import { _getServerUrl } from 'Request';

const emptyData = {
    unitId: '',
    tower: '',
    floor: '',
    flat: '',
    lastName_zh: '',
    firstName_zh: '',
    lastName_en: '',
    firstName_en: '',
    email: '',
    phone: '852',
    address: '',
    lastName2_zh: '',
    firstName2_zh: '',
    lastName2_en: '',
    firstName2_en: '',
    email2: '',
    phone2: '852',
    address2: '',
    contactLastName: '',
    contactFirstName: '',
    contactPhone: '852',
    contactLastName2: '',
    contactFirstName2: '',
    contactPhone2: '852',
    meterNumber: '',
    meterReading: '',
    meterNumber2: '',
    meterReading2: '',
    electricalSupplyFormContact_1: 'purchaser1',
    waterMeterNumber: '',
    waterMeterReading: '',
    waterMeterNumber2: '',
    waterMeterReading2: '',
    waterSupplyFormContact_1: 'purchaser1',
    gasMeterNumber: '',
    gasMeterReading: '',
    primaryContact: 'owner',
    forms: [],
    filterForms: [],
    search: '',
    showStatement: false,
    
};

const extractNumberOnly = (text) => {
    var lastChar = text.substr(text.length - 1);
    var regex = /[0-9]|\./;
    if (regex.test(lastChar)) {
        return text;
    } else {
        return text.slice(0, -1);        
    }
};

export default class FormSubmission extends Component {

    constructor(props) {
		super(props);
		this.state = {
            unitId: '',
            tower: '',
            floor: '',
            flat: '',
            lastName_zh: '',
            firstName_zh: '',
            lastName_en: '',
            firstName_en: '',
            email: '',
            phone: '',
            areacode: '852',
            address: '',
            lastName2_zh: '',
            firstName2_zh: '',
            lastName2_en: '',
            firstName2_en: '',
            email2: '',
            phone2: '',
            areacode2: '852',
            address2: '',
            contactLastName: '',
            contactFirstName: '',
            contactPhone: '',
            contactAreacode: '852',
            contactLastName2: '',
            contactFirstName2: '',
            contactPhone2: '',
            contactAreacode2: '852',
            meterNumber: '',
            meterReading: '',
            meterNumber2: '',
            meterReading2: '',
            electricalSupplyFormContact_1: 'purchaser1',
            electricalSupplyFormContact_2: 'purchaser1',
            waterMeterNumber: '',
            waterMeterReading: '',
            waterMeterNumber2: '',
            waterMeterReading2: '',
            waterSupplyFormContact_1: 'purchaser1',
            waterSupplyFormContact_2: 'purchaser1',
            gasMeterNumber: '',
            gasMeterReading: '',
            primaryContact: 'owner',
            forms: [],
            filterForms: [],
            showDropDown: false,
            dropDownTitle: "",
            dropDownArray: [],
            dropDownChecked: "",
            dropDownType: '',
            ownerTitle: 'Mr',
            ownerTitle2: 'Mr',
            search: '',
            showStatement: false,
            terms_of_use: ""
		};
    }
    

    componentWillMount() {
        let { props } = this;
        let { checklist, projectDetail } = props;

        let tower, floor, flat;

        tower = this.state.tower;
        floor = this.state.floor;
        flat = this.state.flat;


        props.getFormSubmissionFilter(tower, floor, flat);
    }

    componentDidMount() {
        this.props.fetchHandoverForm(this.props.projectDetail._id);
        this.props.displayLoading();
        Server.getTermsOfUse()
            .then((terms_of_use) => {
                this.setState({terms_of_use});
            })
            .then(() => {
                this.props.removeLoading();
            });
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.handoverForm.length != this.state.forms.length && nextProps.handoverForm.length > 0) {
            this.props.displayLoading();
            let forms = nextProps.handoverForm.map((form) => {
                let path = form.path;
                if (!path.startsWith("http")) {
                    path = _getServerUrl() + "blob_pdf/" + form.path;
                }
                return {
                    title: form.title,
                    originalPath: path,
                    dataPath: "",
                    data: null,
                    unitId: this.state.unitId
                };
            });
            this.setState({
                forms: forms,
                filterForms: forms
            });
            setTimeout(()=> {
                this.props.removeLoading();
            }, 0);
        }

        if (nextProps.unitInfo) {
            if (nextProps.unitInfo._id != this.state.unitId) {
                // if floor and flat is empty for the selected unit, reset the state params
                this.setState({
                    unitId: nextProps.unitInfo._id,
                    unitInfo: nextProps.unitInfo,
                    tower: nextProps.unitInfo.tower,
                    floor: nextProps.unitInfo.floor,
                    flat: nextProps.unitInfo.flat
                });
            }
        } else {
            this.setState({
                unitInfo: nextProps.unitInfo
            });
        }
    }

    naturalCompare(a, b){
        var ax = [], bx = [];
        
        a.replace(/(\d+)|(\D+)/g, function(_, $1, $2) { ax.push([$1 || Infinity, $2 || ""]) });
        b.replace(/(\d+)|(\D+)/g, function(_, $1, $2) { bx.push([$1 || Infinity, $2 || ""]) });
        
        while(ax.length && bx.length) {
            var an = ax.shift();
            var bn = bx.shift();
            var nn = (an[0] - bn[0]) || an[1].localeCompare(bn[1]);
            if(nn) return nn;
        }
    
        return ax.length - bx.length;
    }

    renderDropDownList() {
		let { props } = this;
        let dropDownArray = this.state.dropDownArray
        if(Object.keys(dropDownArray).length > 0){
            dropDownArray = dropDownArray.sort((a, b) => this.naturalCompare(a, b));
            return dropDownArray.map((item, i) => {
				let showChecked = item == this.state.dropDownChecked ? (<Icon icon={'checked'} style={styles.checkIcon} />) : null;    
                return(
                    <TouchableOpacity key={i} onPress={() => {
                        if(this.state.dropDownType == 'tower'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                tower: item == this.state.dropDownChecked ? "" : item,
                            });
                        }
                        if(this.state.dropDownType == 'floor'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                floor: item == this.state.dropDownChecked ? "" : item,
                            });
                        }
                        if(this.state.dropDownType == 'flat'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                flat: item == this.state.dropDownChecked ? "" : item,
                            });
                        }			   				   
                    }}>
                        <View style={{ width: '100%',height:60, borderBottomWidth: 1, alignItems: 'center', justifyContent: 'center', flexDirection: 'row'}}>
                            <View style={{flex: 1}}/>
                            <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                                <Text style={{textAlign: 'center'}}>{item}</Text>
                            </View>	   
                            <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                                {showChecked}
                            </View>    
                        </View>	
                    </TouchableOpacity>
                );
            });
        }else {
            return null;
        }
    }

    phoneValidation(phone) {
        if (!phone) {
            return false;
        }
        if (phone && phone.length < 8) {
            return false;
        }
        if (phone) {
            if (isNaN(parseInt(phone))) {
                return false;
            } else {
                let numStr = parseInt(phone) + "";
                if (numStr.length < 8)
                    return false;
            }
        }
    
        return true;
    };

    inputValidation(data) {
        // owner 1 is required
        if (!data.lastName_zh || data.lastName_zh.length == 0) {
            this.lastNameInput.focus();
            return false;
        }
        if (!data.firstName_zh || data.firstName_zh.length == 0) {
            this.firstNameInput.focus();
            return false;
        }
        if (!data.phone || data.phone.length < 8) {
            this.phoneInput.focus();
            return false;
        } else if (!this.phoneValidation(data.phone)) {
            Alert.alert(
                '',
                i18.invalidPhoneFormat,
                [
    
                    {text: i18.OK, onPress: () => {
                        this.phoneInput.focus();
                    }},
                ]);
            return false;
        } 

        // validate owner 2 if the name is inputted
        if (((data.firstName2_zh && data.firstName2_zh.length > 0)
            || (data.lastName2_zh && data.lastName2_zh.length > 0)
            || (data.firstName2_en && data.firstName2_en.length > 0)
            || (data.lastName2_en && data.lastName2_en.length > 0))) {
            
            if ((!data.phone2 || data.phone2.length < 8)) {
                Alert.alert(
                    '',
                    i18.owner2MissingPhoneNumber,
                    [
        
                        {text: i18.OK, onPress: () => {
                            this.phone2.focus();
                        }},
                    ]);
                return false;
            } else if (!this.phoneValidation(data.phone2)) {
                Alert.alert(
                    '',
                    i18.invalidPhoneFormat,
                    [
        
                        {text: i18.OK, onPress: () => {
                            this.phone2.focus();
                        }},
                    ]);
                return false;
            }
        }

        return true;
    }

    resetData() {
        let forms = this.props.handoverForm.map((form) => {
            let path = form.path;
            if (!path.startsWith("http")) {
                path = _getServerUrl() + "blob_pdf/" + form.path;
            }
            return {
                title: form.title,
                originalPath: path,
                dataPath: "",
                data: null,
                unitId: this.state.unitId
            };
        });

                        
        this.setState({
            ...emptyData,
            forms,
        });
    }

    unitNotAvailableAlert() {
        Alert.alert(
            '',
            i18.unitNotAvailable,
            [

                {text: i18.OK, onPress: () => {

                }},
            ]
            )
    }

    onSubmit() {
        if (this.state.unitId == "") {
           this.unitNotAvailableAlert();
        } else {
            // console.log("onSubmit this.state.unitInfo", this.state.unitInfo);
            
            if (this.state.unitInfo && this.state.unitInfo._id == this.state.unitId) {
                let handoverData = Object.assign({}, this.state);
                handoverData.type = this.state.unitInfo.type;
                handoverData.staff = this.props.user._id;
                handoverData.forms.forEach((form) => {
                    form.unitId = handoverData.unitId;
                });
                if (this.inputValidation(handoverData)) {
                    this.setState({showStatement: true})
                }
            } else {
                this.unitNotAvailableAlert();
            }
        }
    }

    renderFormItem(item) {
        return <FormItem formObj={item.item} source={this.getDataSource.bind(this)} />;
    }

    getDataSource() {
        let data = Object.assign({}, this.state);
        delete data.showDropDown;
        return data;
    }

    render() {

    let { props } = this;
    let { language, handoverForm, unitsTree } = props;
    
    // console.log(handoverForm)
    
    let containerWidth = Dimensions.get('window').width / 3 - 2;

    var showDropDown =  this.state.showDropDown ? (
        <View style={{
            position: 'absolute',
            width: Dimensions.get('window').width,
            height: Dimensions.get('window').height,
            alignItems: 'center',
            justifyContent: 'center'
        }}>
            <View style={styles.dropDownListContainer}>
                <View style={{height: 50, alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: 1, backgroundColor: "#131913", flexDirection: 'row'}}>
                    <View style={{flex: 1}}/>
                    <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                        <Text style={{color: '#fff'}}>{this.state.dropDownTitle}</Text>
                    </View>
                    <View style={{flex: 1}}/>
                </View>	
                <ScrollView>
                    {this.renderDropDownList()}
                </ScrollView>
                <TouchableOpacity style={{height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'lightgray'}} onPress={() => {
                    this.setState({showDropDown: false})
                    let tower = this.state.tower;
                    let floor = this.state.floor;
                    let flat = this.state.flat;
                    props.getFormSubmissionFilter(tower, floor, flat);
                }}>
                    <Text>{i18.Close}</Text>
                </TouchableOpacity>

            </View>
        </View>  
    ) : null;

    var showStatement = this.state.showStatement ? (
        <View style={{
            position: 'absolute',
            width: Dimensions.get('window').width,
            height: Dimensions.get('window').height,
            alignItems: 'center',
            justifyContent: 'center'
        }}>
            <View style={styles.dropDownListContainer}>
                <View style={{height: 70, alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: 1, backgroundColor: "#131913", flexDirection: 'row'}}>
                    <View style={{flex: 1}}/>
                    <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                        <Text style={{color: '#fff'}}>{i18.termsofuse}</Text>
                    </View>
                    <View style={{flex: 1}}/>
                </View>	
                <ScrollView>
                    <View style={{padding: 5}}>    
                        <Text>{this.state.terms_of_use}</Text>
                    </View>    
                </ScrollView>
                <View  style={{height: 50, alignItems: 'center', justifyContent: 'center', flexDirection: 'row'}}>
                    <TouchableOpacity style={{height: 50, alignItems: 'center', justifyContent: 'center', width: '50%', backgroundColor: 'lightgray'}} onPress={() => {
                        this.setState({showStatement: false})
                    }}>
                        <Text>{i18.Close}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={{height: 50, alignItems: 'center', justifyContent: 'center', width: '50%', backgroundColor: '#131913'}} onPress={() => {
                        let handoverData = Object.assign({}, this.state);
                        handoverData.type = this.state.unitInfo.type;
                        handoverData.staff = this.props.user._id;
                        handoverData.forms.forEach((form) => {
                            form.unitId = handoverData.unitId;
                        });

                        this.props.updateHandover(handoverData).then((resp) => {
                            // console.log("handover form submission end", resp);
                            if (Array.isArray(resp) && resp.length > 0) {
                                Alert.alert(
                                i18.HandoverDataSubmitted,
                                '',
                                [
                                    {text: i18.OK, onPress: () => {
                                        this.resetData();
                                        this.setState({showStatement: false})
                                    }},
                                ]
                                )
                            }
                        });
                    }}>
                        <Text style={{color: 'white'}}>{i18.confirm}</Text>
                    </TouchableOpacity>
                </View>    
            </View>
        </View>  
    ) : null;

    let inputStyle = {height: 30, marginLeft: 5, marginRight: 5, fontSize: 14, color: 'black', padding: 5};    

    return (
        <View style={styles.container}>
            <Nav
            onRightPress={()=> this.onSubmit()} 
			right="SUBMIT"
            onLeftPress={()=>{
                Alert.alert(
                    i18.handoverFormBack,
                    '',
                    [
                        {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                        {text: i18.yes, onPress: () => {
                            this.props.navigation.goBack()
                        }},
                    ]
                )
            }}
			left="BACK">
            {i18.formSubmission}
            </Nav>
            <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
                <View style={[styles.center, {height: 80, flexDirection: 'row'}]}>
                    <TouchableOpacity style={{flex: 1}} onPress={() => {
                        this.setState({
                            dropDownTitle: i18.tower,
                            dropDownArray: (unitsTree) ? Object.keys(unitsTree) : [],
                            dropDownChecked: this.state.tower,
                            dropDownType: 'tower',
                            showDropDown: true,
                        });
                    }}>
                        <View style={[styles.center, {flex: 1}]}>
                            <View style={{height: 20, width: '70%', flexDirection: 'row'}}>
                                <Text style={{fontSize: 16, color: 'red'}}>*</Text><Text>{i18.tower}</Text>
                            </View>
                            <View style={{height: 39, width: '90%', padding: 5, backgroundColor: '#B2B2B2', flexDirection: 'row'}}>
                                <View style={{flex: 2, justifyContent: 'center'}}>
                                    <Text style={{marginLeft: 9, fontSize: 16, color: '#FFFFFF'}}>{this.state.tower}</Text>
                                </View>    
                                <View style={[styles.center, {flex: 1}]}>
                                    <Icon style={{ fontSize: 8, color: '#FFFFFF', textAlign: 'center' }} icon={"DROPDOWN"}/>    
                                </View>      
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity style={{flex: 1}} onPress={() => {
                        if(this.state.tower){
                            this.setState({
                                dropDownTitle: i18.floor,
                                dropDownArray: (this.state.tower && unitsTree && unitsTree[this.state.tower]) ? Object.keys(unitsTree[this.state.tower]) : [], 
                                dropDownChecked: this.state.floor,
                                dropDownType: 'floor',
                                showDropDown: true,
                            });
                        }

                    }}>
                        <View style={[styles.center, {flex: 1}]}>
                            <View style={{height: 20, width: '70%'}}>
                                <Text>{i18.floor}</Text>
                            </View>
                            <View style={{height: 39, width: '90%', padding: 5, backgroundColor: '#B2B2B2', flexDirection: 'row'}}>
                                <View style={{flex: 2, justifyContent: 'center'}}>
                                    <Text style={{marginLeft: 9, fontSize: 16, color: '#FFFFFF'}}>{this.state.floor}</Text>
                                </View>    
                                <View style={[styles.center, {flex: 1}]}>
                                    <Icon style={{ fontSize: 8, color: '#FFFFFF', textAlign: 'center' }} icon={"DROPDOWN"}/>    
                                </View>       
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity style={{flex: 1}} onPress={() => {
                        if(this.state.floor){
                            this.setState({
                                dropDownTitle: i18.flat,
                                dropDownArray: (this.state.floor && unitsTree && unitsTree[this.state.tower][this.state.floor]) ? Object.keys(unitsTree[this.state.tower][this.state.floor]) : [], 
                                dropDownChecked: this.state.flat,
                                dropDownType: 'flat',
                                showDropDown: true,
                            });
                        }
                    }}>
                        <View style={[styles.center, {flex: 1}]}>
                            <View style={{height: 20, width: '70%'}}>
                                <Text>{i18.flat}</Text>
                            </View>
                            <View style={{height: 39, width: '90%', padding: 5, backgroundColor: '#B2B2B2', flexDirection: 'row'}}>
                                <View style={{flex: 2, justifyContent: 'center'}}>
                                    <Text style={{marginLeft: 9, fontSize: 16, color: '#FFFFFF'}}>{this.state.flat}</Text>
                                </View>    
                                <View style={[styles.center, {flex: 1}]}>
                                    <Icon style={{ fontSize: 8, color: '#FFFFFF', textAlign: 'center' }} icon={"DROPDOWN"}/>    
                                </View>    
                            </View>
                        </View>
                    </TouchableOpacity>
                </View> 
                
                <View style={{height: 5}} />
                
                <View style={[styles.center, {height: 69}]}>
                    <View style={{height: 20, width: '92.5%'}}>
                        <Text>{i18.proprietor}</Text>
                    </View>
                    <View style={{height: 39, width: '95%', flexDirection: 'row', alignItems: 'center'}}>   
                        <View style={{ width: '70%', flexDirection: 'row', marginTop: 5}}>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({ownerTitle: 'Mr'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.ownerTitle == 'Mr' ? '#FF8034' : 'transparent', borderTopLeftRadius: 5, borderBottomLeftRadius: 5}]}>

                                <Text style={{ color: this.state.ownerTitle == 'Mr' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>Mr</Text>

                            </TouchableOpacity>
                            </View>  
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({ownerTitle: 'Mrs'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.ownerTitle == 'Mrs' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.ownerTitle == 'Mrs' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>Mrs</Text>

                            </TouchableOpacity>
                            </View>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({ownerTitle: 'Miss'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.ownerTitle == 'Miss' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.ownerTitle == 'Miss' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>Miss</Text>

                            </TouchableOpacity>
                            </View>  
                        </View>  
                    </View>    
                </View>
                <View style={{height: 5}} />    

                <View style={[styles.center, {height: 69,  flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%', flexDirection: 'row'}}>
                            <Text style={{fontSize: 16, color: 'red'}}>*</Text><Text>{i18.proprietor} {i18.chinese} {i18.lastName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    ref={ref => {
                                        if (ref)
                                            this.lastNameInput = ref.refs["input"];
                                    }}
                                    style={inputStyle}
                                    value={this.state.lastName_zh}
                                    onChangeText={(text) => this.setState({lastName_zh: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%', flexDirection: 'row'}}>
                            <Text style={{fontSize: 16, color: 'red'}}>*</Text><Text>{i18.proprietor} {i18.chinese} {i18.firstName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    ref={ref => {
                                        if (ref)
                                            this.firstNameInput = ref.refs["input"];
                                    }}
                                    style={inputStyle}
                                    value={this.state.firstName_zh}
                                    onChangeText={(text) => this.setState({firstName_zh: text})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>

                <View style={[styles.center, {height: 69,  flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} {i18.english} {i18.lastName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.lastName_en}
                                    onChangeText={(text) => this.setState({lastName_en: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} {i18.english} {i18.firstName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.firstName_en}
                                    onChangeText={(text) => this.setState({firstName_en: text})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>


                <View style={[styles.center, {height: 69,  flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} {i18.email}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    ref={ref => {
                                        if (ref)
                                            this.emailInput = ref.refs["input"];
                                    }}
                                    style={inputStyle}
                                    value={this.state.email}
                                    onChangeText={(text) => this.setState({email: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} {i18.phone}</Text>
                        </View>
                        <View style={{flexDirection: 'row'}}>
                            <ModalDropdown 
                                dropdownStyle={styles.dropdownStyle} 
                                animated={false} defaultIndex={0} options={this.props.areacodeLabels} 
                                renderRow={(row, idx, highlighted)=>{
                                    return (
                                        <TouchableOpacity style={{padding: 5}} >
                                            <Text style={styles.dropdownLabel} >{"+" + this.props.areacodeLabels[idx]}</Text>
                                        </TouchableOpacity>
                                    );
                                }} 
                                onSelect={selected=>{
                                    this.setState({
                                        areacode: this.props.areacodeValues[selected]
                                    });
                                }}>
                                <View style={styles.areacode}>
                                    <Text style={styles.areacodeText} >{"+" + this.state.areacode}</Text>
                                </View>
                            </ModalDropdown>
                            <View style={{height: 39, width: '60%', backgroundColor: '#FFFFFF',
                            borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                                <View style={{flex: 3, justifyContent: 'center'}}>
                                    <TextInput 
                                        ref={ref => {
                                            if (ref)
                                                this.phoneInput = ref.refs["input"];
                                        }}
                                        style={inputStyle}
                                        value={this.state.phone}
                                        keyboardType={'numeric'}
                                        onChangeText={(text) => {
                                            let str = parseInt(text) + "";
                                            if (str != "NaN")
                                                this.setState({phone: str});
                                            else
                                                this.setState({phone: ""});
                                        }}
                                    />
                                </View>
                            </View>  
                        </View>
                    </View>     
                </View>


                <View style={[styles.center, {height: 69}]}>
                    <View style={{height: 20, width: '92.5%'}}>
                        <Text>{i18.proprietor} {i18.address}</Text>
                    </View>
                    <View style={{height: 39, width: '95%', backgroundColor: '#FFFFFF',
                    borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>   
                        <TextInput 
                            style={{height: 35, marginLeft: 9, fontSize: 16, color: '#B2B2B2', width: '95%'}}
                            placeholderTextColor={'#B2B2B2'}
                            value={this.state.address}
                            onChangeText={(text) => this.setState({address: text})}
                        />
                    </View>    
                </View>


                <View style={{height: 5}} />
                
                <View style={[styles.center, {height: 69}]}>
                    <View style={{height: 20, width: '92.5%'}}>
                        <Text>{i18.proprietor} 2</Text>
                    </View>
                    <View style={{height: 39, width: '95%', flexDirection: 'row', alignItems: 'center'}}>   
                        <View style={{ width: '70%', flexDirection: 'row', marginTop: 5}}>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({ownerTitle2: 'Mr'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.ownerTitle2 == 'Mr' ? '#FF8034' : 'transparent', borderTopLeftRadius: 5, borderBottomLeftRadius: 5}]}>

                                <Text style={{ color: this.state.ownerTitle2 == 'Mr' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>Mr</Text>

                            </TouchableOpacity>
                            </View>  
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({ownerTitle2: 'Mrs'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.ownerTitle2 == 'Mrs' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.ownerTitle2 == 'Mrs' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>Mrs</Text>

                            </TouchableOpacity>
                            </View>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({ownerTitle2: 'Miss'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.ownerTitle2 == 'Miss' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.ownerTitle2 == 'Miss' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>Miss</Text>

                            </TouchableOpacity>
                            </View>  
                        </View>  
                    </View>    
                </View>
                <View style={{height: 5}} /> 


                <View style={[styles.center, {height: 69,  flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} 2 {i18.chinese} {i18.lastName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.lastName2_zh}
                                    onChangeText={(text) => this.setState({lastName2_zh: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} 2 {i18.chinese} {i18.firstName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.firstName2_zh}
                                    onChangeText={(text) => this.setState({firstName2_zh: text})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>

                <View style={[styles.center, {height: 69,  flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} 2 {i18.english} {i18.lastName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.lastName2_en}
                                    onChangeText={(text) => this.setState({lastName2_en: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} 2 {i18.english} {i18.firstName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.firstName2_en}
                                    onChangeText={(text) => this.setState({firstName2_en: text})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>


                <View style={[styles.center, {height: 69,  flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} 2 {i18.email}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.email2}
                                    onChangeText={(text) => this.setState({email2: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.proprietor} 2 {i18.phone}</Text>
                        </View>
                        <View style={{flexDirection: 'row'}}>
                            <ModalDropdown 
                                dropdownStyle={styles.dropdownStyle} 
                                animated={false} defaultIndex={0} options={this.props.areacodeLabels} 
                                renderRow={(row, idx, highlighted)=>{
                                    return (
                                        <TouchableOpacity style={{padding: 5}} >
                                            <Text style={styles.dropdownLabel} >{"+" + this.props.areacodeLabels[idx]}</Text>
                                        </TouchableOpacity>
                                    );
                                }} 
                                onSelect={selected=>{
                                    this.setState({
                                        areacode2: this.props.areacodeValues[selected]
                                    });
                                }}>
                                <View style={styles.areacode}>
                                    <Text style={styles.areacodeText} >{"+" + this.state.areacode2}</Text>
                                </View>
                            </ModalDropdown>
                            <View style={{height: 39, width: '60%', backgroundColor: '#FFFFFF',
                            borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                                <View style={{flex: 3, justifyContent: 'center'}}>
                                    <TextInput 
                                        ref={ref => {
                                            if (ref)
                                                this.phone2 = ref.refs["input"];
                                        }}
                                        style={inputStyle}
                                        value={this.state.phone2}
                                        keyboardType={'numeric'}
                                        onChangeText={(text) => {
                                            let str = parseInt(text) + "";
                                            if (str != "NaN")
                                                this.setState({phone2: str});
                                            else
                                                this.setState({phone2: ""});
                                        }}
                                    />
                                </View>
                            </View>  
                        </View>
                    </View>     
                </View>

                <View style={[styles.center, {height: 69}]}>
                    <View style={{height: 20, width: '92.5%'}}>
                        <Text>{i18.proprietor} 2 {i18.address}</Text>
                    </View>
                    <View style={{height: 39, width: '95%', backgroundColor: '#FFFFFF',
                    borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>   
                        <TextInput 
                            style={{height: 35, marginLeft: 9, fontSize: 16, color: '#B2B2B2', width: '95%'}}
                            placeholderTextColor={'#B2B2B2'}
                            value={this.state.address2}
                            onChangeText={(text) => this.setState({address2: text})}
                        />
                    </View>    
                </View>
                
                <View style={{height: 5}} />

                <View style={[styles.center, {height: 69}]}>
                    <View style={{height: 20, width: '92.5%'}}>
                        <Text>{i18.primaryContact}</Text>
                    </View>
                    <View style={{height: 39, width: '95%', flexDirection: 'row', alignItems: 'center'}}>   
                        <View style={{ width: '70%', flexDirection: 'row', marginTop: 5}}>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({primaryContact: 'owner'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.primaryContact == 'owner' ? '#FF8034' : 'transparent', borderTopLeftRadius: 5, borderBottomLeftRadius: 5}]}>

                                <Text style={{ color: this.state.primaryContact == 'owner' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>{i18.proprietor}</Text>

                            </TouchableOpacity>
                            </View>  
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({primaryContact: 'owner2'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.primaryContact == 'owner2' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.primaryContact == 'owner2' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>{i18.proprietor} 2</Text>

                            </TouchableOpacity>
                            </View> 
                        </View>  
                    </View>    
                </View>

                <View style={{height: 5}} />

                <View style={[styles.center, {height: 69,  flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{width: '85%'}}>
                            <Text>{i18.emergencyContact} {i18.lastName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.contactLastName}
                                    onChangeText={(text) => this.setState({contactLastName: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{width: '85%'}}>
                            <Text>{i18.emergencyContact} {i18.firstName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.contactFirstName}
                                    onChangeText={(text) => this.setState({contactFirstName: text})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>

                <View style={{height: 5}} />

                <View style={[styles.center, {height: 69}]}>
                    <View style={{width: '92.5%'}}>
                        <Text>{i18.emergencyContact} {i18.phone}</Text>
                    </View>
                    <View style={{flexDirection: 'row'}}>
                        <ModalDropdown 
                            dropdownStyle={styles.dropdownStyle} 
                            animated={false} defaultIndex={0} options={this.props.areacodeLabels} 
                            renderRow={(row, idx, highlighted)=>{
                                return (
                                    <TouchableOpacity style={{padding: 5}} >
                                        <Text style={styles.dropdownLabel} >{"+" + this.props.areacodeLabels[idx]}</Text>
                                    </TouchableOpacity>
                                );
                            }} 
                            onSelect={selected=>{
                                this.setState({
                                    contactAreacode: this.props.areacodeValues[selected]
                                });
                            }}>
                            <View style={styles.areacode}>
                                <Text style={styles.areacodeText} >{"+" + this.state.contactAreacode}</Text>
                            </View>
                        </ModalDropdown>
                        <View style={{height: 39, width: '80%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>   
                            <TextInput 
                                style={{height: 35, marginLeft: 9, fontSize: 16, color: '#B2B2B2', width: '95%'}}
                                placeholderTextColor={'#B2B2B2'}
                                value={this.state.contactPhone}
                                keyboardType={'numeric'}
                                onChangeText={(text) => {
                                    let str = parseInt(text) + "";
                                    if (str != "NaN")
                                        this.setState({contactPhone: str});
                                    else
                                        this.setState({contactPhone: ""});
                                }} />
                        </View>
                    </View>    
                </View>

                <View style={{height: 5}} />

                {/* <View style={[styles.center, {height: 69,  flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{width: '85%'}}>
                            <Text>{i18.emergencyContact} 2 {i18.lastName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.contactLastName2}
                                    onChangeText={(text) => this.setState({contactLastName2: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{ width: '85%'}}>
                            <Text>{i18.emergencyContact} 2 {i18.firstName}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.contactFirstName2}
                                    onChangeText={(text) => this.setState({contactFirstName2: text})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>

                <View style={{height: 5}} />

                <View style={[styles.center, {height: 69}]}>
                    <View style={{width: '92.5%'}}>
                        <Text>{i18.emergencyContact} 2 {i18.phone}</Text>
                    </View>
                    <View style={{height: 39, width: '95%', backgroundColor: '#FFFFFF',
                    borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>   
                        <TextInput 
                            style={{height: 35, marginLeft: 9, fontSize: 16, color: '#B2B2B2', width: '95%'}}
                            placeholderTextColor={'#B2B2B2'}
                            value={this.state.contactPhone2}
                            onChangeText={(text) => this.setState({contactPhone2: text})}
                        />
                    </View>    
                </View> */}

                <View style={{height: 5}} />

                <View style={[styles.center, {height: 69, flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.meterNumber}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.meterNumber}
                                    onChangeText={(text) => this.setState({meterNumber: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.meterReading}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.meterReading}
                                    keyboardType={"numeric"}
                                    onChangeText={(text) => {
                                        this.setState({meterReading: extractNumberOnly(text)});
                                    }}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>


                <View style={[styles.center, {height: 69, flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.waterMeterNumber}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.waterMeterNumber}
                                    onChangeText={(text) => this.setState({waterMeterNumber: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.waterMeterReading}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.waterMeterReading}
                                    keyboardType={"numeric"}
                                    onChangeText={(text) => this.setState({waterMeterReading: extractNumberOnly(text)})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>

                <View style={[styles.center, {height: 69, flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.gasMeterNumber}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.gasMeterNumber}
                                    onChangeText={(text) => this.setState({gasMeterNumber: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.gasMeterReading}</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.gasMeterReading}
                                    keyboardType={"numeric"}
                                    onChangeText={(text) => this.setState({gasMeterReading: extractNumberOnly(text)})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>


                {/* <View style={[styles.center, {height: 69, flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.meterNumber} 2</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.meterNumber2}
                                    onChangeText={(text) => this.setState({meterNumber2: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.meterReading} 2</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.meterReading2}
                                    keyboardType={"numeric"}
                                    onChangeText={(text) => this.setState({meterReading2: extractNumberOnly(text)})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View>

                <View style={[styles.center, {height: 69, flexDirection: 'row'}]}>
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.waterMeterNumber} 2</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.waterMeterNumber2}
                                    onChangeText={(text) => this.setState({waterMeterNumber2: text})}
                                />
                            </View>
                        </View>  
                    </View>   
                    <View style={[styles.center, {flex: 1}]}>
                        <View style={{height: 20, width: '85%'}}>
                            <Text>{i18.waterMeterReading} 2</Text>
                        </View>
                        <View style={{height: 39, width: '90%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                            <View style={{flex: 3, justifyContent: 'center'}}>
                                <TextInput 
                                    style={inputStyle}
                                    value={this.state.waterMeterReading2}
                                    keyboardType={"numeric"}
                                    onChangeText={(text) => this.setState({waterMeterReading2: extractNumberOnly(text)})}
                                />
                            </View>
                        </View>  
                    </View>     
                </View> */}

                <View style={{height: 5}} />
                
                <View style={[styles.center, {height: 69}]}>
                    <View style={{height: 20, width: '92.5%'}}>
                        <Text>{i18.electricalContact}</Text>
                    </View>
                    <View style={{height: 39, width: '95%', flexDirection: 'row', alignItems: 'center'}}>   
                        <View style={{ width: '70%', flexDirection: 'row', marginTop: 5}}>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({electricalSupplyFormContact_1: 'purchaser1'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.electricalSupplyFormContact_1 == 'purchaser1' ? '#FF8034' : 'transparent', borderTopLeftRadius: 5, borderBottomLeftRadius: 5}]}>

                                <Text style={{ color: this.state.electricalSupplyFormContact_1 == 'purchaser1' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>{i18.proprietor}</Text>

                            </TouchableOpacity>
                            </View>  
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({electricalSupplyFormContact_1: 'purchaser2'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.electricalSupplyFormContact_1 == 'purchaser2' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.electricalSupplyFormContact_1 == 'purchaser2' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>{i18.proprietor} 2</Text>

                            </TouchableOpacity>
                            </View>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({electricalSupplyFormContact_1: 'other'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.electricalSupplyFormContact_1 == 'other' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.electricalSupplyFormContact_1 == 'other' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>{i18.other}</Text>

                            </TouchableOpacity>
                            </View>  
                        </View>  
                    </View>    
                </View>
                <View style={{height: 5}} /> 

              
                <View style={{height: 5}} />
                
                <View style={[styles.center, {height: 69}]}>
                    <View style={{height: 20, width: '92.5%'}}>
                        <Text>{i18.waterContact}</Text>
                    </View>
                    <View style={{height: 39, width: '95%', flexDirection: 'row', alignItems: 'center'}}>   
                        <View style={{ width: '70%', flexDirection: 'row', marginTop: 5}}>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({waterSupplyFormContact_1: 'purchaser1'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.waterSupplyFormContact_1 == 'purchaser1' ? '#FF8034' : 'transparent', borderTopLeftRadius: 5, borderBottomLeftRadius: 5}]}>

                                <Text style={{ color: this.state.waterSupplyFormContact_1 == 'purchaser1' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>{i18.proprietor}</Text>

                            </TouchableOpacity>
                            </View>  
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({waterSupplyFormContact_1: 'purchaser2'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.waterSupplyFormContact_1 == 'purchaser2' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.waterSupplyFormContact_1 == 'purchaser2' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>{i18.proprietor} 2</Text>

                            </TouchableOpacity>
                            </View>
                            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                            <TouchableOpacity
                                onPress={() => this.setState({waterSupplyFormContact_1: 'other'})}
                                style={[styles.ownerButton, {backgroundColor: this.state.waterSupplyFormContact_1 == 'other' ? '#FF8034' : 'transparent', borderLeftWidth: 0}]}>

                                <Text style={{ color: this.state.waterSupplyFormContact_1 == 'other' ? '#FFFFFF' : '#FF8034', fontWeight: 'bold' }}>{i18.other}</Text>

                            </TouchableOpacity>
                            </View>  
                        </View>  
                    </View>    
                </View>
                <View style={{height: 5}} /> 


                <View style={{height: 10}} />
                <View style={{ marginLeft: '1.5%', width: '96%', backgroundColor: '#ACACAC', height: 1}}/>
                <View style={{height: 10}} />

                <View style={[styles.center, {height: 69}]}>
                    <View style={{height: 20, width: '92.5%'}}>
                        <Text>{i18.form}</Text>
                    </View>
                    <View style={{height: 39, width: '95%', backgroundColor: '#FFFFFF',
                    borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>   
                        <TextInput 
                            style={{height: 35, marginLeft: 9, fontSize: 16, color: '#B2B2B2', width: '95%'}}
                            placeholderTextColor={'#B2B2B2'}
                            placeholder={i18.search}
                            value={this.state.search}
                            onChangeText={(text) => {
                                var formData = this.state.forms.slice();
                                formData = formData.filter((item)=> {
                                    return item.title.includes(text)
                                })

                                this.setState({
                                    search: text,
                                    filterForms: formData
                                })
                            }}
                        />
                    </View>    
                </View>

                <View>
                    <FlatList
                        data={this.state.filterForms}
                        renderItem={this.renderFormItem.bind(this)} />
                    {/* <View
                        onPress={() => {}}
                        style={styles.pageButton}>
                        <View style={{flex: 3}}>
                            <Text style={{ fontWeight: 'bold' }}>{i18.form1}</Text>
                        </View> 

                        <View style={{flex: 2.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>   
                            <TouchableOpacity  onPress={() => {
                            
                            }}>  
                            <View style={[styles.buttonContainer, {borderColor: '#FF8034', backgroundColor: '#FF8034'}]}>
                                <Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.open}</Text>
                            </View>
                            </TouchableOpacity> 
                        </View>    

                    </View> */}
                    {/* <View
                        onPress={() => {}}
                        style={styles.pageButton}>
                        <View style={{flex: 3}}>
                            <Text style={{ fontWeight: 'bold' }}>{i18.form2}</Text>
                        </View> 

                        <View style={{flex: 2.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>   
                            <TouchableOpacity  onPress={() => {
                            
                            }}>  
                            <View style={[styles.buttonContainer, {borderColor: '#707070', backgroundColor: '#707070'}]}>
                                <Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.update}</Text>
                            </View>
                            </TouchableOpacity> 
                        </View>   

                    </View> */}
                    {/* <View
                        onPress={() => {}}
                        style={styles.pageButton}>
                        <View style={{flex: 3}}>
                            <Text style={{ fontWeight: 'bold' }}>{i18.form3}</Text>
                        </View> 

                        <View style={{flex: 2.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>   
                            <TouchableOpacity  onPress={() => {
                            
                            }}>  
                            <View style={[styles.buttonContainer, {borderColor: '#707070', backgroundColor: '#707070'}]}>
                                <Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.update}</Text>
                            </View>
                            </TouchableOpacity> 
                            <TouchableOpacity style={{marginLeft: 7}} onPress={() => {
                            
                            }}>  
                            <View style={[styles.buttonContainer, {borderColor: '#CB7474', backgroundColor: '#CB7474'}]}>
                                <Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.delete}</Text>
                            </View>
                            </TouchableOpacity> 
                        </View>   

                    </View> */}
                    {/* <View
                        onPress={() => {}}
                        style={styles.pageButton}>
                        <View style={{flex: 3}}>
                            <Text style={{ fontWeight: 'bold' }}>{i18.form4}</Text>
                        </View> 

                        <View style={{flex: 2.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>   
                            <TouchableOpacity  onPress={() => {
                            
                            }}>  
                            <View style={[styles.buttonContainer, {borderColor: '#FF8034', backgroundColor: '#FF8034'}]}>
                                <Text style={[styles.buttonText, {color: '#FFFFFF'}]}>{i18.open}</Text>
                            </View>
                            </TouchableOpacity> 
                        </View>

                    </View> */}
                </View>

                <View style={{height: 40}}/>
           
            </KeyboardAwareScrollView>

            {showDropDown}
            {showStatement}
        </View>
    );
  }
};


const styles = StyleSheet.create({
    container: {
        flex: 1,
		backgroundColor: Theme.bg,
    },
    center: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    checkIcon: {
        color: '#B2B2B2',
        width: 52,
        fontSize: 26,
        backgroundColor:'transparent',
        textAlign:'center',
    },
    dropDownListContainer: {
        backgroundColor: '#fff',
        width: Dimensions.get('window').width * 0.85,
        height: Dimensions.get('window').height * 0.8,
        marginBottom: Dimensions.get('window').height * 0.05,
        borderWidth: 1,
    },
    ownerButton: {
        width: '100%',
        height: 35,
        borderColor: '#FF8034',
        borderWidth: 2,
        justifyContent: 'center',
        alignItems: 'center',
    },
	areacode:{
        height: 39,
        padding: 5,
        width: 50,
        marginRight: 5,
        backgroundColor: 'white',			
        borderWidth: 1,
        borderColor: '#B2B2B2',
        justifyContent: 'center'
    },
    areacodeText: {
		fontSize: 14
    },
    dropdownStyle: {
        position: 'absolute',
        top: 0, bottom: 0, left: 30, right: 30
    },
    dropdownLabel: {
        
    }
});

const mapStateToProps = (state, ownProps) => {
  return {
    language: state.setting.language,
    loginDateStr: state.auth.lastLoginDateStr,
    user: state.auth.user,
    // formSubmission_options: state.project.adhoc_options,
    projectDetail: state.project.projectDetail,
    handoverForm: state.project.handoverForm,
    unitInfo: state.project.adhoc_current_unit,
    // filteredOptions: state.project.filteredOptions,
    unitsTree: state.project.unitsTree,
    areacodeLabels: state.setting.areacodeLabels,
    areacodeValues: state.setting.areacodeValues,
  }
}


import { CheckLists as checkListsMapDispatchToProps, Project as projectMapDispatchToProps, Loading as loadingMapDispatchToProps } from 'Controller';

let loadingProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
    projectProps = projectMapDispatchToProps(dispatch, ownProps);
    loadingProps = loadingMapDispatchToProps(dispatch, ownProps);
	return {...projectProps, ...loadingProps};
})(FormSubmission);