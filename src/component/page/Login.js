import React, { Component } from 'react';
import {
	StyleSheet,
	View,
	Image,
	TextInput,
	TouchableOpacity,
	Platform,
	Dimensions,
	ScrollView,
	WebView,
	Alert
} from 'react-native';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import Text from 'react-native-text';
import ScrollableTabView, { DefaultTabBar, } from 'react-native-scrollable-tab-view';
import KeyboardSpacer from 'react-native-keyboard-spacer';
import i18 from 'i18';
import Server from 'Server';
import { FloatTextInput, Icon, LoginButton as Button } from 'Item'

import { theme } from 'Theme';
import { Action } from 'Redux';
import { SERVER_URL } from 'Config';

export default class LoginPage extends Component {

	constructor(props) {
		super(props);
		this.state = {
			showProjectList: false,
			username: "",
			password: "",
			useAdLogin: true,
			adPath: "/saml/login",
			email: ""
		};
    }

	componentDidMount() {
		console.log("loginPage componentDidMount");
	}


	componentWillReceiveProps(nextProps) {
		let { props } = this;
		let { showAlertForgotPassword} = props;

		if (this.props._navigation.main == null) {
			this.props.setNavigation(this.props.navigation);
		}

		if(!showAlertForgotPassword && nextProps.showAlertForgotPassword) {
			Alert.alert(
				'Please check your email!',
					'',
					[
						{text: i18.OK, onPress: () => {
							props.changeForgotPasswordAlertStatus(false);
							this.onClick(1);
						}},
					]
				)
		}
	}

	login() {
		// Don't Use This !!! Use replace
		// this.props.navigation.navigate("Main");
		let { props } = this;
		if(this.state.email.includes('@test.com')){
			Alert.alert(
				i18.InputUsername,
				'',
				[
					{text: i18.OK, onPress: () => console.log("OK")
					},
				]
			)
		}else {
			props.login(this.state.username, this.state.password).then((result) => {
				if (result)
					this.onClick(2);	
			});
		}
	}

	loginWithAd (useAd) {
		this.setState({
			useAdLogin: useAd
		});
		if (!useAd){
			setTimeout(() => {
				this.onClick(1);
			}, 100);
		}
	}

	onClick(page) {
		if (page) this.setState({ page: page });
	}

	samlOnLoad(event) {
		var url = event.nativeEvent.url;
		console.log("samlOnLoad", url);
		if (url.indexOf('/saml/failure') > -1) {
			fetch(`${SERVER_URL}/saml/login`);
		} else {
			var components = url.split('?sessionID=');
			if (components.length > 1) {
				var session = components[1];
				this.setState({
					useAdLogin: false
				});
				setTimeout(() => {
					this.props.adLogin(session).then((result) => {
						if (result)
							this.onClick(2);
					})
				}, 0);
			}
		}
	}

	renderUserLogin() {
		return (
			<View style={{ flex: 3, alignItems: 'center' }}>

				<View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
					<Icon style={{ color: 'white', marginVertical: 5, width: 30, fontSize: 23 }} icon='ACCOUNT' />
					<FloatTextInput ref='username' value={this.state.username} placeholder={i18.username} onChangeText={(text) => this.setState({ username: text })}/>
				</View>

				<View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
					<Icon style={{ color: 'white', marginVertical: 5, width: 30, fontSize: 23 }} icon='PASSWORD' />
					<FloatTextInput ref='password' value={this.state.password} placeholder={i18.password} secureTextEntry onChangeText={(text) => this.setState({ password: text })}/>
				</View>

				<Button onPress={this.login.bind(this)} text={i18.Login} />

				<TouchableOpacity onPress={() => this.onClick(3)}>
					<Text style={{ color: 'white', textAlign: 'center' }}>{i18.ForgetPassword + '？'}</Text>
				</TouchableOpacity>

			</View>
		);
	}

	onBack(page) {
		if(page != 3){
			this.setState({
				page: page - 1,
				username: "",
				password: ""
			});
		}else {
			this.setState({
				page: page - 2,
				username: "",
				password: ""
			});
		}
	
	}

	renderSelectLoginMethod() {
		return (
			<View style={{ flex: 3, alignItems: 'center' }}>
				<Button onPress={() => this.loginWithAd(true)} text={i18.LoginFromAD} />
				<Button onPress={() => this.loginWithAd(false)} text={i18.LoginFromOther} />
			</View>
		);
	}

	renderResetPassword() {
		let { props } = this;
		return (
			<View style={{ flex: 3, alignItems: 'center' }}>
				<Text style={{ color: 'white', textAlign: 'center' }}>{i18.InputUsername}</Text>

				<View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
					<Icon style={{ color: 'white', marginVertical: 5, width: 30, fontSize: 23 }} icon='ACCOUNT' />
					<FloatTextInput ref='username' placeholder={i18.email}  value={this.state.email} onChangeText={(text) => this.setState({ email: text })} />
				</View>

				<Button onPress={()=>{
					if(this.state.email.includes('@test.com')){
						Alert.alert(
							i18.InputUsername,
							'',
							[
								{text: i18.OK, onPress: () => console.log("OK")
								},
							]
						)
					}else {
						props.forgetPassword(this.state.email);
					}
				}} text={i18.SendResetPasswordEmail} />
			</View>
		);
	}

	renderSelectProject() {

		let { props } = this;
		let { projectLists, selectedProject } = props;

		let name = '';
		let code = '';

		let currentLanguage = i18.getLanguage();
		let nameLang = 'name_en';
		if (currentLanguage == 'zh_hk') {
			nameLang = 'name_zh';
		} else if (currentLanguage == 'cn') {
			nameLang = 'name_cn';
		}

		if(selectedProject){
			name = selectedProject[nameLang];
			code = selectedProject._id;
		} else if (projectLists != null && projectLists.length > 0) {
			name = projectLists[0][nameLang];
			code = projectLists[0]._id;
		}


		var showButton = code ? (
			<Button onPress={() => props.fetchProjectDetail(code)} text={i18.OK} />
		) : null;

		return (
			<View style={{ flex: 3, alignItems: 'center' }}>
				<TouchableOpacity style={{flex: 1, alignItems: 'center'}} onPress={() => {
					props.fetchProjectLists();
					this.setState({showProjectList: true}) 
				}}> 
					<View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
						<Icon style={{ color: 'white',  width: 27, fontSize: 20, marginBottom: 5 }} icon='fillhome' />
						<View style={{width: '55%'}}>
							<View style={{flexDirection: 'row', justifyContent: 'space-between' }}>
								<Text style={{ color: 'white', fontSize: 16, marginLeft: 5, marginBottom: 5 }}>{name}</Text>
								<Icon style={{ color: 'white', width: 27, fontSize: 9 }} icon='DROPDOWN' />
							</View>	
							<View style={{backgroundColor: '#888', height: 1, width: '100%' }}/>
						</View>		
					</View>
				</TouchableOpacity>
				{showButton}
			</View>
		);
	}

	renderDropDownList() {
		let { props } = this;
		let { projectLists } = props;
		let currentLanguage = i18.getLanguage();
		let nameLang = 'name_en';
		if (currentLanguage == 'zh_hk') {
			nameLang = 'name_zh';
		} else if (currentLanguage == 'cn') {
			nameLang = 'name_cn';
		}
		if(projectLists.length > 0){
			return projectLists.map((item, i) => {							
				return(
					<TouchableOpacity key={i} onPress={() => {
						this.setState({
							showProjectList: false,
						})
						props.updateSelectedProject(item);
					}}>
						<View style={{ height:60, borderBottomWidth: 1, alignItems: 'center', justifyContent: 'center'}}>
							<Text>{item[nameLang]}</Text>
						</View>	
					</TouchableOpacity>
				);
			});
		}else {
			return null;
		}
    }

	render() {
		let { props } = this;
		let { someNum, user, projectLists, selectedProject } = props;
		let { page } = this.state || 0;

		var showDropDownList = this.state.showProjectList ? (
			<View style={{
				position: 'absolute',
				width: Dimensions.get('window').width,
				height: Dimensions.get('window').height,
				alignItems: 'center',
				justifyContent: 'center'
			}}>
				<View style={styles.dropDownListContainer}>
					<View style={{height: 50, alignItems: 'center', justifyContent: 'center', borderBottomWidth: 1, backgroundColor: "#131913"}}>
						<Text style={{color: '#fff'}}>{i18.Items}</Text>
					</View>	
					<ScrollView>
						{ this.renderDropDownList() }
					</ScrollView>
					<TouchableOpacity style={{height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'lightgray'}} onPress={() => {
						 this.setState({
							showProjectList: false
						}); 
					}}>
						<Text>{i18.Close}</Text>
					</TouchableOpacity>

				</View>
			</View>
		) : null;

		let showBackButton = page > 0 ? (
			<TouchableOpacity onPress={()=> this.onBack(page)} style={{position: 'absolute', top: 7, left: 5}}>
				<Icon style={[styles.icon,{fontSize: 25, color: theme}]} icon={'BACK'}/>
			</TouchableOpacity>
		) : null;

		if (this.state.useAdLogin)  {
			let path = `${SERVER_URL}/saml/login`;
			return (
				<View style={{ flex: 10 }}>
					<WebView
						style={{flex: 10}}
						source={{uri: path}}
						userAgent={"MobileApp/1.0"}
						automaticallyAdjustContentInsets={false}
						javaScriptEnabled={true}
						scalesPageToFit={true}
						onLoadStart={this.samlOnLoad.bind(this)}
						/>
					<View style={{ flex: 0, alignItems: 'center', backgroundColor: "#362F29" }}>
						<Button onPress={() => this.loginWithAd(false)} text={i18.LoginFromOther} />
					</View>
				</View>
			);
		} else {
			return (
				<View style={{ flex: 1, backgroundColor: "#362F29" }}>
					<View style={styles.iconTitle}>
						<Image resizeMode='center' style={styles.icon} source={require('Asset/Image').icon} />
						<Text style={{ color: 'white', textAlign: 'right', fontSize: 24, marginVertical: 10 }}> Group</Text>
						<Text style={{ color: 'white', textAlign: 'right', fontSize: 12, width: '75%' }}>e-Integrated Handover System</Text>
					</View>

					<ScrollableTabView
						style={{ flex: 1 }}
						renderTabBar={() => <View />}
						prerenderingSiblingsNumber={3}
						page={page}
						locked={true}
					>
						{this.renderSelectLoginMethod()}
						{this.renderUserLogin()}
						{this.renderSelectProject()}
						{this.renderResetPassword()}
					

					</ScrollableTabView>
					{Platform.OS === 'ios' ? <KeyboardSpacer /> : null}
					{showBackButton}
					{showDropDownList}
				</View>
			);
		}
	}
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: '#F5FCFF',
	},
	iconTitle: {
		alignItems: 'flex-end',
		justifyContent: 'center',
		flex: 1,
		padding: 30,
		width: '100%'

	},
	icon: {
		padding: 10,
		width: 80,
		height: 120,
		tintColor: 'white'
	},
	dropDownListContainer: {
		backgroundColor: '#fff',
		width: Dimensions.get('window').width * 0.85,
		height: Dimensions.get('window').height * 0.8,
		marginBottom: Dimensions.get('window').height * 0.05,
		borderWidth: 1,
    },
});

const mapStateToProps = (state, ownProps) => {
	return {
		language: state.setting.language,
		user: state.auth.user,
		projectLists: state.project.projectLists,
		selectedProject: state.project.selectedProject,
		projectDetail: state.project.projectDetail,
		_navigation: state.navigation,
		showAlertForgotPassword: state.auth.showAlertForgotPassword,
	}
}

import { Login as loginMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';

let loginProps;
let projectProps;
module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	loginProps = loginMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {
		...loginProps, 
		...projectProps,
		setNavigation: (navigation)=>dispatch(Action.navigation.setMainNavigation(navigation))};
})(LoginPage);