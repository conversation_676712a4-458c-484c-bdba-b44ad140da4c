import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Animated,
	ScrollView,
	Dimensions,
	Keyboard,
	Platform,
	NetInfo,
	Alert,
} from 'react-native';
const TextInput = require('CustTextInput');
import { connect } from 'react-redux'

import { checkList as Theme} from 'Theme';
import { Icon, Nav, DropDownList, Button } from 'Item';
import i18 from 'i18';
import moment from 'moment';

import CachedImage  from 'react-native-cached-image';
const {
    ImageCacheProvider
} = CachedImage;

import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'

export default class AddHocForm extends Component {

	constructor(props) {
        super(props);
        
        this.saveImagesCallback = this.saveImagesCallback.bind(this);

        let firstOption = (this.props.checklist && this.props.checklist.name) ? this.props.checklist.name : this.props.navigation.state.params.firstOption;
        let secondOption = (this.props.checklist && this.props.checklist.source) ? this.props.checklist.source : this.props.navigation.state.params.secondOption;
        
		this.state = {
            tower: '',
            floor: '',
            flat: '',
            location: '',
            level1: '',
            level2: '',
            remarks: '',
            isImportant: false,
            showDropDown: false,
            dropDownTitle: "",
            dropDownArray: [],
            dropDownChecked: "",
            dropDownType: '',
            firstOption: firstOption ? firstOption : "",
            secondOption: secondOption ?  secondOption : "",
            isInspection: (firstOption && (firstOption == "Inspection" || firstOption == i18.CheckTaskList)),
            id: '',
            hasChecklist: false,
            questionId: '',
            images: [],
            annotations: null,
            adhoc_options: null
		};
	}

    componentDidMount() {
        let { props } = this;
        let { checklist } = props;

        let tower, floor, flat, location, level1, level2, id;

        // console.log(checklist)

        let source = (this.state.isInspection) ? this.props.adhocTreeInspec : this.props.adhocTreeSpec;
        if(this.props.navigation.state.params.addNew) {
            tower = checklist.tower;
            floor = checklist.floor;
            flat =  checklist.flat;

            id = checklist._id;
            setTimeout(() => {
                this.setState({
                    tower: tower,
                    floor: floor,
                    flat: flat,
                    location: checklist.questions[checklist.questions.length - 1].location,
                    level1: checklist.questions[checklist.questions.length - 1].level1,
                    level2: checklist.questions[checklist.questions.length - 1].level2,
                    hasChecklist: true,
                    id: id,
                    adhoc_options: source
                });
            }, 0);    
        }else if(this.props.navigation.state.params.modifyItem){
            tower = checklist.tower;
            floor = checklist.floor;
            flat =  checklist.flat;
            remarks = this.props.navigation.state.params.question.description;
            critical = this.props.navigation.state.params.question.critical;
            questionId = this.props.navigation.state.params.question._id;

            id = checklist._id;

            setTimeout(() => {
                this.setState({
                    tower: tower,
                    floor: floor,
                    flat: flat,
                    location: this.props.navigation.state.params.question.location,
                    level1:  this.props.navigation.state.params.question.level1,
                    level2: this.props.navigation.state.params.question.level2,
                    hasChecklist: true,
                    id: id,
                    remarks: remarks,
                    isImportant: critical,
                    questionId: questionId,
                    images: this.props.navigation.state.params.question.images,
                    annotations: this.props.navigation.state.params.question.annotations,
                    adhoc_options: source
                });    
            }, 0);
           
        }else {
            tower = this.state.tower;
            floor = this.state.floor;
            flat = this.state.flat;
            location = this.state.location;
            level1 = this.state.level1;
            level2 = this.state.level2;
            setTimeout(() => {
                this.setState({
                    adhoc_options: source
                });    
            }, 0);
        }

		props.getAdHocFilter(tower, floor, flat, location, level1, level2, this.state.isInspection);
	}


	onSubmit() {
        let { props } = this;	
        let { checklist } = props;

        if (this.state.remarks.trim().length < 1) {
            Alert.alert(i18.remarksCannotBeEmpty, 
                '',
                [
                    {
                        text: i18.OK, 
                        onPress: () => console.log('OK Pressed')
                    }
                ]
            )
            return;

            
        }

        if (!this.props.unitInfo) {
            Alert.alert(i18.notValidUnit, 
                '',
                [
                    {
                        text: i18.OK, 
                        onPress: () => console.log('OK Pressed')
                    }
                ]
            )
            return;
        }
        let adhocObj = Object.assign({}, this.state);
        if(this.props.navigation.state.params.modifyItem) {
            props.modifyAddHocCheckList(adhocObj);
            this.props.navigation.goBack();
        }else if(this.props.navigation.state.params.addNew){
            adhocObj.type = (this.props.unitInfo) ? this.props.unitInfo.type : "";
            props.createAddHocCheckList(adhocObj);
            this.props.navigation.goBack();
            this.props.navigation.navigate("AdhocList")    
        }else{
            adhocObj.type = (this.props.unitInfo) ? this.props.unitInfo.type : "";
            adhocObj.unit = (this.props.unitInfo) ? this.props.unitInfo._id : "";
            props.createAddHocCheckList(adhocObj);
            this.props.navigation.goBack();
            this.props.navigation.navigate("AdhocList")
        }
    
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.unitInfo && nextProps.unitInfo != this.props.unitInfo) {
            let source;
            // if (nextProps.unitInfo.unitOptions)
            //     source = (this.state.isInspection) ? nextProps.unitInfo.unitOptions.inspectionOpts : nextProps.unitInfo.unitOptions.specialOpts;
            this.setState({
                tower: nextProps.unitInfo.tower,
                floor: nextProps.unitInfo.floor,
                flat: nextProps.unitInfo.flat,
                // adhoc_options: source
            });
        }

        // if (nextProps.adhoc_options) {
        //     let level1 = nextProps.adhoc_options.level1.find((lv1) => {
        //         return lv1 == this.state.level1;
        //     });
        //     let level2 = nextProps.adhoc_options.level2.find((lv2) => {
        //         return lv2 == this.state.level2;
        //     });
        //     this.setState({
        //         level1: level1,
        //         level2: level2
        //     });
        // }
    }

    saveImagesCallback(images) {
        // console.log("saveImagesCallback", images);
        this.setState({
            images: images
        });
    }

    naturalCompare(a, b){
        var ax = [], bx = [];
        
        a.replace(/(\d+)|(\D+)/g, function(_, $1, $2) { ax.push([$1 || Infinity, $2 || ""]) });
        b.replace(/(\d+)|(\D+)/g, function(_, $1, $2) { bx.push([$1 || Infinity, $2 || ""]) });
        
        while(ax.length && bx.length) {
            var an = ax.shift();
            var bn = bx.shift();
            var nn = (an[0] - bn[0]) || an[1].localeCompare(bn[1]);
            if(nn) return nn;
        }
    
        return ax.length - bx.length;
    }

    saveAnnotationCallback(annotations) {
        this.setState({
            annotations
        });
    }

    renderDropDownList() {
		let { props } = this;
        let dropDownArray = this.state.dropDownArray
        if(Object.keys(dropDownArray).length > 0){
            dropDownArray = dropDownArray.sort((a, b) => this.naturalCompare(a, b));
            return dropDownArray.map((item, i) => {
				let showChecked = item == this.state.dropDownChecked ? (<Icon icon={'checked'} style={styles.checkIcon} />) : null;    
                return(
                    <TouchableOpacity key={i} onPress={() => {
                        if(this.state.dropDownType == 'tower'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                tower: item == this.state.dropDownChecked ? "" : item,
                                floor: "",
                                flat: "",
                                // location: "",
                                // level1: "",
                                // level2: ""
                            });
                        }
                        if(this.state.dropDownType == 'floor'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                floor: item == this.state.dropDownChecked ? "" : item,
                                flat: "",
                                // location: "",
                                // level1: "",
                                // level2: ""
                            });
                        }
                        if(this.state.dropDownType == 'flat'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                flat: item == this.state.dropDownChecked ? "" : item,
                                // location: "",
                                // level1: "",
                                // level2: ""
                            });
                        }
                        if(this.state.dropDownType == 'location'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                location: item == this.state.dropDownChecked ? "" : item,
                                level1: "",
                                level2: ""
                            });
                        }
                        if(this.state.dropDownType == 'level1'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                level1: item == this.state.dropDownChecked ? "" : item,
                                level2: ""
                            });
                        }
                        if(this.state.dropDownType == 'level2'){
                            this.setState({
                                dropDownChecked: item == this.state.dropDownChecked ? "" : item,
                                level2: item == this.state.dropDownChecked ? "" : item,
                            });
                        }				   				   
                    }}>
                        <View style={{ width: '100%',height:60, borderBottomWidth: 1, alignItems: 'center', justifyContent: 'center', flexDirection: 'row'}}>
                            <View style={{flex: 1}}/>
                            <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                                <Text style={{textAlign: 'center'}}>{item}</Text>
                            </View>	   
                            <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                                {showChecked}
                            </View>    
                        </View>	
                    </TouchableOpacity>
                );
            });
        }else {
            return null;
        }
    }


	render() {
		let { props } = this;

        // let { adhoc_options, filteredOptions } = props;
        let { adhoc_options } = this.state;
        
        let containerWidth = Dimensions.get('window').width / 3 - 2;

        var showDropDown =  this.state.showDropDown ? (
			<View style={{
                position: 'absolute',
                width: Dimensions.get('window').width,
                height: Dimensions.get('window').height,
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                <View style={styles.dropDownListContainer}>
                    <View style={{height: 50, alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: 1, backgroundColor: "#131913", flexDirection: 'row'}}>
                        <View style={{flex: 1}}/>
                        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
                            <Text style={{color: '#fff'}}>{this.state.dropDownTitle}</Text>
                        </View>
                        <View style={{flex: 1}}/>
                    </View>	
                    <ScrollView>
						{this.renderDropDownList()}
                    </ScrollView>
                    <TouchableOpacity style={{height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'lightgray'}} onPress={() => {
                        this.setState({showDropDown: false})
                        let tower = this.state.tower;
                        let floor = this.state.floor;
                        let flat = this.state.flat;
                        let location = this.state.location;
                        let level1 = this.state.level1;
                        let level2 = this.state.level2;
                        props.getAdHocFilter(tower, floor, flat, location, level1, level2, this.state.isInspection);
                    }}>
                        <Text>{i18.Close}</Text>
                    </TouchableOpacity>

                </View>
            </View>  
        ) : null;
        

		return (
			<View style={styles.container}>	
				<Nav 
					onRightPress={()=>this.onSubmit()} 
					right="SAVE"
					onLeftPress={()=>this.props.navigation.goBack()} 
					left="BACK">
                    {i18.AddHocTitle}
				</Nav>
				<ScrollView>
                    <View style={[styles.center, {height: 80, flexDirection: 'row'}]}>
                        <TouchableOpacity style={{flex: 1}} onPress={() => {
                            if(!this.state.hasChecklist){
                                this.setState({
                                    dropDownTitle: i18.tower,
                                    dropDownArray: Object.keys(this.props.unitsTree),
                                    dropDownChecked: this.state.tower,
                                    dropDownType: 'tower',
                                    showDropDown: true,
                                });
                            }
                        }}>
                            <View style={[styles.center, {flex: 1}]}>
                                <View style={{height: 20, width: '70%'}}>
                                    <Text>{i18.tower}</Text>
                                </View>
                                <View style={{height: 39, width: '90%', padding: 5, backgroundColor: '#B2B2B2', flexDirection: 'row'}}>
                                    <View style={{flex: 2, justifyContent: 'center'}}>
                                        <Text style={{marginLeft: 9, fontSize: 16, color: '#FFFFFF'}}>{this.state.tower}</Text>
                                    </View>    
                                    <View style={[styles.center, {flex: 1}]}>
                                        { 
                                            !this.state.hasChecklist ?
                                            (
                                                <Icon style={{ fontSize: 8, color: '#FFFFFF', textAlign: 'center' }} icon={"DROPDOWN"}/>
                                            ): null
                                        }  
                                    </View>    
                                </View>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity style={{flex: 1}} onPress={() => {
                            if(!this.state.hasChecklist && this.state.tower){
                                this.setState({
                                    dropDownTitle: i18.floor,
                                    dropDownArray: (this.state.tower && this.props.unitsTree[this.state.tower]) ? Object.keys(this.props.unitsTree[this.state.tower]) : [] ,
                                    dropDownChecked: this.state.floor,
                                    dropDownType: 'floor',
                                    showDropDown: true,
                                });
                            }
                        }}>
                            <View style={[styles.center, {flex: 1}]}>
                                <View style={{height: 20, width: '70%'}}>
                                    <Text>{i18.floor}</Text>
                                </View>
                                <View style={{height: 39, width: '90%', padding: 5, backgroundColor: '#B2B2B2', flexDirection: 'row'}}>
                                    <View style={{flex: 2, justifyContent: 'center'}}>
                                        <Text style={{marginLeft: 9, fontSize: 16, color: '#FFFFFF'}}>{this.state.floor}</Text>
                                    </View>    
                                    <View style={[styles.center, {flex: 1}]}>
                                        { 
                                            !this.state.hasChecklist ?
                                            (
                                                <Icon style={{ fontSize: 8, color: '#FFFFFF', textAlign: 'center' }} icon={"DROPDOWN"}/>
                                            ): null
                                        }    
                                    </View>    
                                </View>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity style={{flex: 1}} onPress={() => {
                            if(!this.state.hasChecklist && this.state.floor){
                                this.setState({
                                    dropDownTitle: i18.flat,
                                    dropDownArray: (this.state.floor && this.props.unitsTree[this.state.tower][this.state.floor]) ? Object.keys(this.props.unitsTree[this.state.tower][this.state.floor]) : [] ,
                                    dropDownChecked: this.state.flat,
                                    dropDownType: 'flat',
                                    showDropDown: true,
                                });
                            }
                        }}>
                            <View style={[styles.center, {flex: 1}]}>
                                <View style={{height: 20, width: '70%'}}>
                                    <Text>{i18.flat}</Text>
                                </View>
                                <View style={{height: 39, width: '90%', padding: 5, backgroundColor: '#B2B2B2', flexDirection: 'row'}}>
                                    <View style={{flex: 2, justifyContent: 'center'}}>
                                        <Text style={{marginLeft: 9, fontSize: 16, color: '#FFFFFF'}}>{this.state.flat}</Text>
                                    </View>    
                                    <View style={[styles.center, {flex: 1}]}>
                                        { 
                                            !this.state.hasChecklist ?
                                            (
                                                <Icon style={{ fontSize: 8, color: '#FFFFFF', textAlign: 'center' }} icon={"DROPDOWN"}/>
                                            ): null
                                        }  
                                    </View>    
                                </View>
                            </View>
                        </TouchableOpacity>
                    </View> 

                    <View style={[styles.center, {height: 59, marginTop: 15}]}>
                        <View style={{height: 20, width: '90%'}}>
                            <Text>{i18.location}</Text>
                        </View>
                        <View style={{height: 45, width: '95%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                                <View style={{flex: 7.3, justifyContent: 'center'}}>
                                    <TextInput 
                                        style={{height: 40, marginLeft: 9, fontSize: 16, color: '#B2B2B2'}}
                                        value={this.state.location}
                                        onChangeText={(text) => this.setState({location: text})}
                                    />
                                </View>
                                <View style={{width: 1, backgroundColor: '#B2B2B2', height: '80%'}}/>
                                <TouchableOpacity style={{flex: 1}} onPress={() => {
                                    // if (this.props.unitInfo) {
                                        this.setState({
                                            dropDownTitle: i18.location,
                                            dropDownArray: (adhoc_options) ? Object.keys(adhoc_options) : [],
                                            dropDownChecked: this.state.location,
                                            dropDownType: 'location',
                                            showDropDown: true,
                                        });
                                    // }
                                }}>    
                                    <View style={[styles.center, {flex: 1}]}>
                                        <Icon style={{ fontSize: 8, color: '#B2B2B2', textAlign: 'center' }} icon={"DROPDOWN"}/>
                                    </View>    
                                </TouchableOpacity>
                        </View>    
                    </View> 

                    <View style={[styles.center, {height: 59, marginTop: 20, flexDirection: 'row'}]}>
                        <View style={[styles.center, {flex: 1}]}>
                            <View style={{height: 20, width: '85%'}}>
                                <Text>{i18.level1}</Text>
                            </View>
                            <View style={{height: 45, width: '90%', backgroundColor: '#FFFFFF',
                            borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                                    <View style={{flex: 3, justifyContent: 'center'}}>
                                        <TextInput 
                                            style={{height: 40, marginLeft: 9, fontSize: 16, color: '#B2B2B2'}}
                                            value={this.state.level1}
                                            onChangeText={(text) => this.setState({level1: text})}
                                        />
                                    </View>
                                    <View style={{width: 1, backgroundColor: '#B2B2B2', height: '80%'}}/> 
                                    <TouchableOpacity style={{flex: 1}} onPress={() => {
                                        // if (this.props.unitInfo) {
                                            let level1Opts = (
                                                adhoc_options 
                                                && this.state.location 
                                                && adhoc_options[this.state.location]) 
                                            ? Object.keys(adhoc_options[this.state.location]) : [];
                                            this.setState({
                                                dropDownTitle: i18.level1,
                                                dropDownArray: level1Opts,
                                                dropDownChecked: this.state.level1,
                                                dropDownType: 'level1',
                                                showDropDown: true,
                                            });
                                        // }
                                    }}>    
                                        <View style={[styles.center, {flex: 1}]}>
                                            <Icon style={{ fontSize: 8, color: '#B2B2B2', textAlign: 'center' }} icon={"DROPDOWN"}/>
                                        </View>  
                                    </TouchableOpacity>  
                            </View>  
                        </View>   
                        <View style={[styles.center, {flex: 1}]}>
                            <View style={{height: 20, width: '85%'}}>
                                <Text>{i18.level2}</Text>
                            </View>
                            <View style={{height: 45, width: '90%', backgroundColor: '#FFFFFF',
                            borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>
                                    <View style={{flex: 3, justifyContent: 'center'}}>
                                        <TextInput 
                                            style={{height: 40, marginLeft: 9, fontSize: 16, color: '#B2B2B2'}}
                                            value={this.state.level2}
                                            onChangeText={(text) => this.setState({level2: text})}
                                        />
                                    </View>
                                    <View style={{width: 1, backgroundColor: '#B2B2B2', height: '80%'}}/>
                                    <TouchableOpacity style={{flex: 1}} onPress={() => {
                                        // if (this.props.unitInfo) {
                                            let level2Opts = (
                                                adhoc_options 
                                                && this.state.location 
                                                && this.state.level1 
                                                && adhoc_options[this.state.location][this.state.level1]) 
                                            ? Object.keys(adhoc_options[this.state.location][this.state.level1]) : [];
                                            this.setState({
                                                dropDownTitle: i18.level2,
                                                dropDownArray: level2Opts,
                                                dropDownChecked: this.state.level2,
                                                dropDownType: 'level2',
                                                showDropDown: true,
                                            });
                                        // }
                                    }}>     
                                        <View style={[styles.center, {flex: 1}]}>
                                            <Icon style={{ fontSize: 8, color: '#B2B2B2', textAlign: 'center' }} icon={"DROPDOWN"}/>
                                        </View>  
                                    </TouchableOpacity>  
                            </View>  
                        </View>     
                    </View>

                    <View style={[styles.center, {height: 45, marginTop: 30}]}>
                        <View style={{height: 44, width: '95%', backgroundColor: '#FFFFFF',
                        borderWidth: 1, borderColor: '#B2B2B2', flexDirection: 'row', alignItems: 'center'}}>   
                            <TextInput 
                                style={{height: 40, marginLeft: 9, fontSize: 16, color: '#B2B2B2', width: '95%'}}
                                placeholderTextColor={'#B2B2B2'}
                                value={this.state.remarks}
                                onChangeText={(text) => this.setState({remarks: text})}
                                placeholder={i18.remarks}
                            />
                        </View>    
                    </View> 

                    <View style={[styles.center, {height: 40, marginTop: 20, flexDirection: 'row'}]}>

                        <View style={{flex: 1.5, justifyContent: 'center'}}>
                            <Text style={{marginLeft: '10%'}}>{i18.importantItem}</Text>
                        </View>
                       <View style={[styles.center,{flex: 1, flexDirection: 'row'}]}>
                            <Text>{i18.yes}</Text>
                            <TouchableOpacity onPress={()=> this.setState({isImportant: true})}>
                                <Icon icon={this.state.isImportant ? 'checked' : 'unchecked'} style={styles.checkIcon} />
                            </TouchableOpacity>
                       </View>  
                       <View style={[styles.center,{flex: 1, flexDirection: 'row'}]}>
                            <Text>{i18.no}</Text>
                            <TouchableOpacity onPress={()=> this.setState({isImportant: false})}>
                                <Icon icon={!this.state.isImportant ? 'checked' : 'unchecked'} style={styles.checkIcon} />
                            </TouchableOpacity>
                       </View>         
                    </View>                      
                    <View style={[{marginTop: 20, flexDirection: 'row', flexWrap: 'wrap', alignItems: 'flex-start'}]}>
                        {
                            (this.state.images && this.state.images.length > 0) ? (
                                this.state.images.map((image, idx) => {
                                    {/* console.log('defect item image', image); */ }
                                    let path = image.path;
                                    if (path.indexOf('file://') == -1) {
                                        path = 'file://' + path;
                                    }
                                    let title = i18.Photo + " " + (idx + 1);
                                    return (
                                        <TouchableOpacity
                                            key={idx}
                                            style={{ width: containerWidth, height: containerWidth, backgroundColor: 'transparent', alignItems: 'center', justifyContent: 'center' }}
                                            onPress={() => {
                                                this.props.navigation.navigate('EditPhoto', { item: this.state, currentIndex: idx, callback: this.saveImagesCallback })
                                            }}>
                                            <Image
                                                style={{ width: '90%', height: '90%' }}
                                                source={{ uri: path }}>
                                                <View style={styles.photoLabel}>
                                                    <Text style={{ fontSize: 12, marginLeft: 3 }}>{title}</Text>
                                                </View>
                                            </Image>
                                        </TouchableOpacity>
                                    );
                                })
                            ) : null
                        }
                        <TouchableOpacity 
                            style={{width: containerWidth, height: containerWidth, alignItems: 'center', justifyContent: 'center'}}
                            onPress={() => {
                                this.props.navigation.navigate('EditPhoto', {item: this.state, forceAdd: true, callback: this.saveImagesCallback});
                            }}>
                            <View style={{backgroundColor: '#D8D8D8', width: '90%', height: '90%', alignItems: 'center', justifyContent: 'center'}}>
                                <Icon icon={'TakePhoto'} style={{
                                    width: 30,
                                    fontSize: 26,
                                    backgroundColor:'transparent',
                                    color: '#3C3C3C',
                                    textAlign:'center',
                                }} />
                                <Text style={{fontSize: 12, marginLeft: 3, marginTop: 5}}>{i18.NewPhoto}</Text>
                            </View>
                        </TouchableOpacity>	
                    </View>    

                </ScrollView>
                {showDropDown}
                <View style={{ bottom: 0,
					borderTopWidth: 1, height: 44, width: '100%', backgroundColor: Theme.bg,
					borderColor: '#AEAEAE', alignItems: 'center', justifyContent: 'center'}}>
                    <TouchableOpacity 
                        style={[{alignItems: 'center', justifyContent: 'center', width: '95%'}, (this.props.floor_plans && this.props.floor_plans.length > 0) ? {} : {opacity: 0.2}]}
                        disabled={!(this.props.floor_plans && this.props.floor_plans.length > 0)}
                        onPress={() => {
                            if (this.props.floor_plans && this.props.floor_plans.length > 0)
                                this.props.navigation.navigate('FloorPlan', { floor_plans: this.props.floor_plans, item: this.state, callback: this.saveAnnotationCallback.bind(this) });
                        }}>
                        <View style={{alignItems: 'center', justifyContent: 'center', width: '100%', 
                            borderWidth: 1, borderRadius: 4, borderColor: '#3D3D3D', height: 31}}>
                            <Text style={{color: '#3D3D3D', fontSize: 16, fontWeight: 'bold'}}>{i18.FloorPlan}</Text>
                        </View>	
                    </TouchableOpacity>
				</View>	 
			</View>
		);
	}
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
    },
    center: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    checkIcon: {
        color: '#B2B2B2',
        width: 52,
		fontSize: 26,
		backgroundColor:'transparent',
		textAlign:'center',
    },
    dropDownListContainer: {
		backgroundColor: '#fff',
		width: Dimensions.get('window').width * 0.85,
		height: Dimensions.get('window').height * 0.8,
		marginBottom: Dimensions.get('window').height * 0.05,
		borderWidth: 1,
	},
	photoLabel: {
		backgroundColor: '#rgba(255, 255, 255, 0.7)', 
		height: 23, 
		width: '90%', 
		justifyContent: 'center', 
		top: "5%", 
		position: 'absolute'
	}
});

const mapStateToProps = (state, ownProps) => {
	return {
		projectDetail: state.project.projectDetail,
		user: state.auth.user,
        language: state.setting.language,
        // adhoc_options: (state.project.adhocTree) ? state.project.adhocTree : null,
        adhocTreeInspec: state.project.adhocTreeInspec,
        adhocTreeSpec: state.project.adhocTreeSpec,
        unitsTree: state.project.unitsTree,
        // filteredOptions: state.project.filteredOptions,
        checklist: state.checklists.checklist,
        unitInfo: state.project.adhoc_current_unit,
		floor_plans: (state.project.adhoc_current_unit) ? state.project.adhoc_current_unit.floor_plans : null
	}
}


import { CheckLists as checkListsMapDispatchToProps, Items as itemsMapDispatchToProps, Units as unitsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';

let checkListsProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
    checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {...checkListsProps, ...projectProps};
})(AddHocForm);
