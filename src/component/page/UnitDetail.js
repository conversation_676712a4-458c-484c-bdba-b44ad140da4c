import React, { Component } from 'react';
import {
	StyleSheet,
	View,
	Image,
	TouchableOpacity,
	FlatList,
	Animated,
	ScrollView,
	Dimensions,
	Keyboard,
	Platform,
	NetInfo,
    Alert,
    NativeModules,
    Linking
} from 'react-native';
const TextInput = require('CustTextInput');
import { connect } from 'react-redux';
import Text from 'react-native-text';

import { checkList as Theme} from 'Theme';
import { Icon, Nav, DropDownList, Button } from 'Item';
import i18 from 'i18';
import ScrollableTabView, { DefaultTabBar, } from 'react-native-scrollable-tab-view';
import ActionSheet from 'react-native-actionsheet';
import moment from 'moment';

import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import ModalDropdown from 'react-native-modal-dropdown';

import CachedImage  from 'react-native-cached-image';
const {
    ImageCacheProvider
} = CachedImage;


const CANCEL_INDEX = 0;
const DESTRUCTIVE_INDEX = 4;

class TabBar extends Component {

	renderTab = (item, page) => {
		const styles = tabStyles;

		let { activeTab, tabs, goToPage } = this.props;		
		var isTabActive = activeTab === page;

		return (
			<TouchableOpacity key={item.label} onPress={() => goToPage(page)} style={styles.tab}>
				<Text style={[styles.text]}>
					{ i18[item.label] }
				</Text>
			</TouchableOpacity>
		);
	}

	render() {
		const styles = tabStyles;
		
		let { containerWidth, tabs, scrollValue } = this.props;
		let left = scrollValue.interpolate({ inputRange: [0, 1], outputRange: [0, containerWidth / tabs.length ] });

		return (
			<View style={styles.container}>
				<View style={styles.tabs}>
					{ tabs.map(this.renderTab) }
				</View>
				<Animated.View style={[styles.line, { width:containerWidth / tabs.length, left }]} />
			</View>
		);
	}
}

const tabStyles = StyleSheet.create({
	container:{
		height:50,
		borderBottomWidth:1,
		borderColor:'#888',
	},
	tab: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
	},
	tabs: {
		flex:1,
		flexDirection: 'row',
		justifyContent: 'center',
		backgroundColor:'#fff',
	},
	text:{
		fontSize: 14
	},
	line: {
		position: 'absolute',
		height: 3,
		backgroundColor: '#000',
		bottom: 0,
	}
});


export default class UnitDetail extends Component {

	constructor(props) {
		super(props);
		this.state = {
            options: [],
            keyReasons: [],
            items: [],
            isModalVisible: false,
            keyState: "",
            unitState: "",
            borrower: '',
            areaCode: '',
            phone: '',
            reason: '',
            otherReason: '',
            showKeyDropDown: false,
            hiddenItemSummary: [],
            hiddenChecklistSummary: []
		};
    }

    getDisplayName(userId) {
        if (userId in this.props.users) {
            return this.props.users[userId];
        }

        return userId;
    }
    
	componentWillMount(){
		let { props } = this;
		let { projectDetail, user } = props;
        let options = [];
        let keyReasons = [];
        options.push(i18.Cancel);
        keyReasons.push(i18.Cancel);
        let secondArray = user.roles;
        
        projectDetail.internal_status.forEach((item)=>{
            let firstArray = item.acl;
            let found = firstArray.some(r=> secondArray.includes(r));
            if(found){
                options.push(item.name);
            }
        })

        projectDetail.key_stage.forEach((item)=>{
            keyReasons.push(item.name);
        })
        
        this.setState({
            options: options,
            keyReasons: keyReasons
        });
        props.fetchLocalUnitItems();
        // props.fetchLocalChecklists();
        props.fetchLocalChecklistSummary(props.unit._id)
        props.fetchLocalItemStatusSummary(props.unit._id);
    }


    componentWillReceiveProps(nextProps) {
		let { props } = this;
		let { language, units, items, unit } = props;


		if (nextProps.showKeyForm) {
			this._showModal(nextProps.qrcodeData);
        }
	}

    _showModal(qrcodeData){
        let { props } = this;
        let { unit, user } = props;

        let unitKey = unit.unit_key;

        let split_string;
        let phoneNumber;
        if(qrcodeData) {
            split_string = qrcodeData.split(/(\d+)/);

            phoneNumber = qrcodeData.replace(split_string[0], "");
            phoneNumber = phoneNumber.replace(" ", "");
        }

        let name;

        if(qrcodeData) {
            name = split_string[0];
        }else {
            if(unitKey.current_owner){
                name =  this.props.units[unitKey.current_owner];
            }else {
                name = user.username;
            }

        }

        user.username

        let areacode = unitKey.dialing_code || "+852";
        this.setState({ 
            isModalVisible: true,
            showKeyDropDown: true,
            keyState: unitKey.key_status || i18.lend,
            unitState: ((unitKey.unit_status != i18.ownerKeepKey &&  unitKey.unit_status != i18.ownerNotKeepKey && unitKey.unit_status != i18.ownerGotIn )
                ||  unitKey.unit_status ==  "") ? i18.ownerKeepKey : unitKey.unit_status,
            borrower: name,
            areaCode: areacode.replace("+", ""),
            phone: qrcodeData ? phoneNumber : unitKey.mobile,
            reason: unitKey.reason ? unitKey.reason : this.state.keyReasons[1],
            otherReason: unitKey.remark,
        })
    }

	_hideModal() {
        this.setState({ 
            isModalVisible: false,
            showKeyDropDown: false
        })
    }

	renderList(label) {
		
		const styles = listStyles;
		let { props } = this;
        let { unit, user, viewUnitCheckList, items, units, unitItems, unit_address, projectDetail, qrcodeData } = props;

        if(label == "Checkbox"){
            let checkboxs = projectDetail.checkboxes.map((checkbox , i) => {
                // return projectDetail.checkboxes.map((checkbox)=>{
                //     if(checkbox.name == key){
                //         let firstArray = checkbox.acl;
                //         let secondArray = user.roles;

                //         let found = firstArray.some(r=> secondArray.includes(r))
                //         if(found){
                //             return (
                //                 <TouchableOpacity onPress={() => {
                //                         props.updateCheckboxes(unit._id, key, !unit.checkboxes[key]);
                //                     }}>
                //                     <View style={{height: 45, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 15}}>
                //                         <Text style={{color: '#8B8B8B', fontSize: 14}}>{key}</Text>
                //                         <Icon icon={unit.checkboxes[key] ? 'checked' : 'unchecked'} style={styles.checkIcon} />
                //                     </View>
                //                 </TouchableOpacity>    
                //             )
                //         }
                //     }
                // })

                let checked = false;

                let firstArray = checkbox.acl;
                let secondArray = user.roles;
                let found = firstArray.some(r=> secondArray.includes(r));
                let canModified = false;
                let checkboxName;

                // console.log("checkbox.acl", firstArray, i)
                // console.log("user.roles", secondArray)
                // console.log("unit.checkboxes", unit.checkboxes)


                Object.keys(unit.checkboxes).forEach((key, index) => {
                    if(key == checkbox.name && unit.checkboxes[key]){
                        checked = true;
                    }

                    // if(key == checkbox.name) {
                    //     canModified = true;
                    // }

                });

                return (
                    <TouchableOpacity key={i} onPress={() => {
                            if(found ){
                                props.updateCheckboxes(unit._id, checkbox.name, !unit.checkboxes[checkbox.name]);
                            }
                        }}>
                        <View style={{height: 45, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 15}}>
                            <Text style={{color: '#8B8B8B', fontSize: 14}}>{checkbox.name}</Text>
                            <Icon icon={checked ? 'checked' : 'unchecked'} style={styles.checkIcon} />
                        </View>
                    </TouchableOpacity>    
                )

            });

            return (
                <View style={[styles.container,{flex: 1}]} tabLabel={{label}}>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View style={{marginTop: 6}}>
                            {checkboxs}
                        </View>    
                    </ScrollView>    
                </View>
            )
        } else if(label == "Checklist"){
            let checklists;
            let { checklistSummary } = props;
            if(checklistSummary && checklistSummary.length > 0) {
                checklists = checklistSummary.map(checklist => {
                    let total = checklist.checklist_resp[0] ? checklist.checklist_resp[0].questions_count || 0 : 0;
                    let checklistArray = [];
                    if(this.state.hiddenChecklistSummary.indexOf(checklist.name) === -1) {
                        checklist.checklist_resp.map(resp => {
                            var date = resp.submitted_at || null;
                            if(date) {
                                date = moment.utc(date).format('YYYY-MM-DD HH:mm');
                                date = moment.utc(date).toDate();
                                date = moment(date).local().format('YYYY-MM-DD HH:mm');
                            } else {
                                date = 'N/A';
                            }
                            checklistArray.push(
                                <View style={{width: "98%", marginBottom: 8}}>
                                    <TouchableOpacity>
                                        <View style={{
                                            height: 31,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            borderBottomWidth: 1,
                                            borderBottomColor: 'gray',
                                            paddingHorizontal: 10,
                                            backgroundColor: '#F2F2F2',
                                            width: '100%'
                                        }}>
                                            <Text style={{
                                                color: '#393939',
                                                fontSize: 14
                                            }}>{date}</Text>
                                            <Text style={{
                                                color: '#393939',
                                                fontSize: 14
                                            }}>{this.getDisplayName(resp._created_by)}</Text>
                                        </View>
                                        <View style={{
                                            height: 51,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            paddingHorizontal: 3,
                                            backgroundColor: '#F2F2F2'
                                        }}>
                                            <View style={styles.tab}>
                                                <Text style={[styles.text, {color: Theme.header.satisfy}]}>
                                                    {i18.Satisfy}
                                                </Text>
                                                <Text style={[styles.text, {color: Theme.header.satisfy}]}>
                                                    {`${resp.pass}/${resp.questions_count}`}
                                                </Text>
                                            </View>
                                            <View style={styles.line}/>
                                            <View style={styles.tab}>
                                                <Text style={[styles.text, {color: Theme.header.followUp}]}>
                                                    {i18.FollowUp}
                                                </Text>
                                                <Text style={[styles.text, {color: Theme.header.followUp}]}>
                                                    {`${resp.failed}/${resp.questions_count}`}
                                                </Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            )
                        });
                    }
                    return (
                        <View style={{
                            margin: 5,
                            backgroundColor: '#FFFFFF',
                            alignItems: 'center',
                            shadowColor: '#000',
                            shadowOffset: {width: 0, height: 2},
                            shadowOpacity: 0.5,
                            shadowRadius: 2
                        }}>
                            <TouchableOpacity
                                style={{width: "98%", marginBottom: 8}}
                                onPress={() => {
                                let hiddenChecklistSummary = this.state.hiddenChecklistSummary;
                                if(hiddenChecklistSummary.indexOf(checklist.name) === -1){
                                    hiddenChecklistSummary.push(checklist.name);
                                } else {
                                    hiddenChecklistSummary.pop(checklist.name);;
                                }
                                this.setState({hiddenChecklistSummary: hiddenChecklistSummary});
                            }}>
                                <View style={{
                                    height: 40,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    borderBottomWidth: 1,
                                    borderBottomColor: 'gray',
                                    paddingHorizontal: 10,
                                    width: "97%"
                                }}>
                                    <Text style={{
                                        color: '#393939',
                                        fontSize: 16,
                                        fontWeight: 'bold'
                                    }}>{checklist.name}</Text>
                                    <TouchableOpacity>
                                        <Icon style={{fontSize: 8, color: '#333333', textAlign: 'center'}}
                                              icon={"DROPDOWN"}/>
                                    </TouchableOpacity>
                                </View>
                                <View style={{
                                    height: 51,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    paddingHorizontal: 3
                                }}>
                                    <View style={styles.tab}>
                                        <Text style={[styles.text, {color: Theme.header.unfill}]}>
                                            {i18.Unfill}
                                        </Text>
                                        <Text style={[styles.text, {color: Theme.header.unfill}]}>
                                            {`${(total - checklist.satisfy - checklist.followup)}/${total}`}
                                        </Text>
                                    </View>
                                    <View style={styles.line}/>
                                    <View style={styles.tab}>
                                        <Text style={[styles.text, {color: Theme.header.satisfy}]}>
                                            {i18.Satisfy}
                                        </Text>
                                        <Text style={[styles.text, {color: Theme.header.satisfy}]}>
                                            {`${checklist.satisfy}/${total}`}
                                        </Text>
                                    </View>
                                    <View style={styles.line}/>
                                    <View style={styles.tab}>
                                        <Text style={[styles.text, {color: Theme.header.followUp}]}>
                                            {i18.FollowUp}
                                        </Text>
                                        <Text style={[styles.text, {color: Theme.header.followUp}]}>
                                            {`${checklist.followup}/${total}`}
                                        </Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                            {checklistArray}
                        </View>
                    )
                });
            }
            return (
                <View style={[styles.container,{flex: 1}]} tabLabel={{label}}>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        {checklists}
                    </ScrollView>    
                </View>
            )
        }else if(label == "Key"){

            let unitKey = unit.unit_key;

            let unitDate = unitKey._updated_at;
            unitDate = moment.utc(unitDate).format('YYYY-MM-DD HH:mm');
            unitDate = moment.utc(unitDate).toDate();
            unitDate = moment(unitDate).local().format('YYYY-MM-DD HH:mm');

            return (
                <View style={[styles.container,{flex: 1}]} tabLabel={{label}}>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        <View style={{margin: 5, backgroundColor: '#FFFFFF', alignItems: 'center',
                        shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.5, shadowRadius: 2, padding: 10}}>
                            <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%'}}>
                                <Text style={[styles.keyText, {fontWeight: '600', color: '#404040'}]}>{i18.keyID} : {unitKey.key_id}</Text>
                                <Text style={[styles.keyText, {fontWeight: '600', color: '#6CB39F'}]}>{unitKey.key_status}</Text>
                            </View>
                            <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%'}}>
                                <Text style={[styles.keyText]}>{i18.unitState} : {unitKey.unit_status}</Text>
                                <Text style={[styles.keyText, {color: "#9B9B9B"}]}>{unitDate}</Text>
                            </View>
                            <Text style={[styles.keyText, {width: '100%'}]}>{i18.borrower}: {this.getDisplayName(unitKey.current_owner)}</Text> 
                            <Text style={[styles.keyText, {width: '100%'}]}>{i18.lastModfiy}： {this.getDisplayName(unitKey._updated_by)}</Text>  
                            <Text style={[styles.keyText, {width: '100%'}]}>{i18.phone}： {unitKey.dialing_code} {unitKey.mobile}</Text>  
                            <Text style={[styles.keyText, {width: '100%'}]}>{i18.reason}: {unitKey.reason}</Text> 
                            <Text style={[styles.keyText, {width: '100%'}]}>{i18.otherReason}: {unitKey.remark}</Text>        
                        </View>
                    </ScrollView>
                    <View style={{ position:'absolute', bottom: 0,
					borderTopWidth: 1, height: 44, width: '100%', backgroundColor: Theme.bg,
					borderColor: '#AEAEAE', alignItems: 'center', justifyContent: 'center'}}>
						<TouchableOpacity 
                            onPress={()=>{
                                if (Platform.OS == 'ios') {
                                    NativeModules.CameraManager.checkDeviceAuthorizationStatus().then((isAuthorized) => {
                                        if (isAuthorized) {
                                            this.props.navigation.navigate("ReadQRcode", {fromScreen: 'unit'});
                                        } else {
                                            Alert.alert(
                                                i18.scanQRcode, 
                                                i18.pleaseSetCameraPermission,
                                                [
                                                    {text: i18.Skip, onPress: () => {
                                                        props.updateQRcode("", true);
                                                    }},
                                                    {text: i18.Settings, onPress: () => {
                                                        Linking.openURL("app-settings:");
                                                    }}
                                                ])
                                        }  
                                    });
                                } else {
                                    this.props.navigation.navigate("ReadQRcode", {fromScreen: 'unit'});
                                }
                            }}
                            style={{alignItems: 'center', justifyContent: 'center', width: '95%'}}>
                            <View style={{alignItems: 'center', justifyContent: 'center', width: '100%', 
                                borderWidth: 1, borderRadius: 4, borderColor: '#3D3D3D', height: 31}}>
                                <Text style={{color: '#3D3D3D', fontSize: 16, fontWeight: 'bold'}}>{i18.updateRecord}</Text>
                            </View>	
						</TouchableOpacity>
					</View>	    
                </View>
            )
        } else {
            // Item section
            let itemContainer;
            let { itemStatusSummary} = props;
            if(itemStatusSummary && itemStatusSummary.length > 0) {
                itemContainer = itemStatusSummary.map((item, idx1) => {
                    let dataItemArray = [];
                    if(this.state.hiddenItemSummary.indexOf(item.name) === -1) {
                        for (const key of Object.keys(item.statuses)) {
                            dataItemArray.push(
                                <TouchableOpacity style={{marginHorizontal: 3, width: "98%"}}>
                                    <View style={{backgroundColor: '#F2F2F2', alignItems: 'center', padding: 8}}>
                                        <View style={{flexDirection: 'row', alignItems: 'center', width: '100%'}}>
                                            <Text style={[styles.keyText, {
                                                flex: 5.3,
                                                fontWeight: '600',
                                                color: '#404040'
                                            }]}>{key}</Text>
                                            <Text style={[styles.keyText, {
                                                flex: 1,
                                                fontWeight: '600',
                                                color: '#404040'
                                            }]}>{item.statuses[key]}</Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            )
                        }
                    }
                    return (<View key={idx1} style={{
                        margin: 5, backgroundColor: '#FFFFFF', alignItems: 'center',
                        shadowColor: '#000', shadowOffset: {width: 0, height: 2}, shadowOpacity: 0.5, shadowRadius: 2
                    }}>
                        <View style={{width: "98%", marginBottom: 8, alignItems: 'center'}}>
                            <View style={{
                                height: 40,
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                borderBottomWidth: 1,
                                borderBottomColor: '#AEAEAE',
                                paddingHorizontal: 10,
                                width: "97%"
                            }}>
                                <Text style={{color: '#393939', fontSize: 16, fontWeight: 'bold'}}>{item.name}</Text>
                                <Text style={{color: '#AEAEAE', fontSize: 14,}}></Text>
                            </View>

                            <TouchableOpacity style={{margin: 3, width: "98%"}} onPress={() => {
                                let hiddenItemSummary = this.state.hiddenItemSummary;
                                if(hiddenItemSummary.indexOf(item.name) === -1){
                                    hiddenItemSummary.push(item.name);
                                } else {
                                    hiddenItemSummary.pop(item.name);;
                                }
                                this.setState({hiddenItemSummary: hiddenItemSummary});
                            }}>
                                <View style={{backgroundColor: '#FFFFFF', alignItems: 'center', padding: 8}}>
                                    <View style={{flexDirection: 'row', alignItems: 'center', width: '100%'}}>
                                        <Text style={[styles.keyText, {
                                            flex: 5,
                                            fontWeight: '600',
                                            color: '#404040'
                                        }]}>{i18.All}</Text>
                                        <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                                            <Text
                                                style={[styles.keyText, {fontWeight: '600', color: '#404040'}]}>{item.total}</Text>
                                            <TouchableOpacity style={{
                                                width: 35,
                                                height: 30,
                                                alignItems: 'center',
                                                justifyContent: 'center'
                                            }}>
                                                <Icon style={{
                                                    fontSize: 8,
                                                    color: '#333333',
                                                    textAlign: 'center',
                                                    marginLeft: 10
                                                }} icon={"DROPDOWN"}/>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                            </TouchableOpacity>
                            {dataItemArray}
                        </View>
                    </View>)
                })
            }
            return (
                <View style={[styles.container,{flex: 1}]}tabLabel={{label}}>
                    <ScrollView showsVerticalScrollIndicator={false}>
                        {itemContainer}
                    </ScrollView>
                </View>	
            )
        }
        
		
	}


	showActionSheet() {
		this.ActionSheet.show()
    }
    
    handlePress(i) {
		let { props } = this;
		let { unit } = props;
		if(i != 0){
            if(!this.state.showKeyDropDown){
                let option = this.state.options[i];
                let newArray = [];
                newArray.push(unit._id);
    
                props.updateSelectOptions(newArray, option);
            }else {
               this.setState({reason: this.state.keyReasons[i]});
            }
		}
	}
	



	onSubmit() {
		let { props } = this;	
        let { unit } = props;
        
        if(!unit.status_int) {
            Alert.alert(
                i18.statusCannotBeEmpty,
                    '',
                    [
                        {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                    ]
                )

        }else {
            Alert.alert(
                i18.AskForSubmitUnit,
                    '',
                    [
                        {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                        {text: i18.OK, onPress: () => {
                            props.pendingUnits(unit);
                            this.props.navigation.goBack();
                        }},
                    ]
                )
        }

	}


	render() {
		let { props } = this;

        let { projectDetail, unit, unit_address, user } = props;

        let translatedUnitAddress;
		translatedUnitAddress = unit_address[unit.type] || "";
				
		if(unit.tower) {
			translatedUnitAddress = translatedUnitAddress.replace("{tower}", unit.tower);
		}

		if(unit.flat) {
			translatedUnitAddress = translatedUnitAddress.replace("{flat}", unit.flat);
		}

		if(unit.floor) {
			translatedUnitAddress = translatedUnitAddress.replace("{floor}", unit.floor);
        }   

        let unitKey = unit.unit_key;
        
        let showModal = this.state.isModalVisible ? (
            <View  style={{ alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.7)', position: 'absolute', width: '100%', height: '100%',  justifyContent: 'center' }}>
                <View style={{ backgroundColor: 'rgba(252, 252, 252, 0.9)', width: '80%', minHeight: 467, borderRadius: 8 }}>
                    <KeyboardAwareScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{paddingVertical: 10}}>
                        <Text style={{ fontWeight: '600', fontSize: 17, textAlign: 'center' }}>{i18.updateRecord}</Text>

                        <View style={{paddingTop: 15, paddingBottom: 6, flexDirection: 'row', alignItems: 'center' }}>
                            <Text style={{marginLeft: '5%', paddingVertical: 4}}>{i18.keyState}</Text>
                            <View
                                style={{
                                    marginLeft: '5%',
                                    width: '65%',
                                    borderRadius: 3,
                                    fontSize: 16,
                                    borderWidth: 1,
                                    borderColor: '#FF8034',
                                    flexDirection: 'row',
                                }}
                            >
                                <TouchableOpacity
                                    onPress={()=> this.setState({keyState: i18.lend})} 
                                    style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.keyState == i18.lend ? '#FF8034' : 'transparent'}}>
                                    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5}}>
                                        <Text style={{color: this.state.keyState == i18.lend ? '#FFF' : '#A2A2A2', fontSize: 13}}>{i18.lend}</Text>
                                    </View>    
                                </TouchableOpacity>
                                <TouchableOpacity 
                                    onPress={()=> this.setState({keyState: i18.spoiled})} 
                                    style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.keyState == i18.spoiled  ? '#FF8034' : 'transparent'}}>
                                    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5}}>
                                        <Text style={{color: this.state.keyState == i18.spoiled ? '#FFF' : '#A2A2A2', fontSize: 13}}>{i18.spoiled}</Text>
                                    </View>    
                                </TouchableOpacity>       
                            </View>
                        </View>

                        <View style={{paddingTop: 15, paddingBottom: 6, flexDirection: 'row', alignItems: 'center' }}>
                            <Text style={{marginLeft: '5%', paddingVertical: 4}}>{i18.unitState}</Text>
                            <View
                                style={{
                                    marginLeft: '5%',
                                    width: '65%',
                                    borderRadius: 3,
                                    fontSize: 16,
                                    borderWidth: 1,
                                    borderColor: '#FF8034',
                                    flexDirection: 'row',
                                }}
                            >
                                <TouchableOpacity
                                    onPress={()=> this.setState({unitState: i18.ownerKeepKey})}  
                                    style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.unitState == i18.ownerKeepKey ? '#FF8034' : 'transparent'}}>
                                    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5}}>
                                        <Text style={{color: this.state.unitState == i18.ownerKeepKey? '#FFF' : '#A2A2A2', fontSize: 13}}>{i18.ownerKeepKey}</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={()=> this.setState({unitState: i18.ownerNotKeepKey})}   
                                    style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.unitState == i18.ownerNotKeepKey ? '#FF8034' : 'transparent'}}>
                                    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5}}>
                                        <Text style={{color: this.state.unitState == i18.ownerNotKeepKey  ? '#FFF' : '#A2A2A2', fontSize: 13}}>{i18.ownerNotKeepKey}</Text>
                                    </View>
                                </TouchableOpacity> 
                                <TouchableOpacity
                                    onPress={()=> this.setState({unitState: i18.ownerGotIn})}   
                                    style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: this.state.unitState == i18.ownerGotIn ? '#FF8034' : 'transparent'}}>
                                    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 5}}>
                                        <Text style={{color: this.state.unitState == i18.ownerGotIn ? '#FFF' : '#A2A2A2', fontSize: 13}}>{i18.ownerGotIn}</Text>
                                    </View>
                                </TouchableOpacity>         
                            </View>
                        </View>

                        <View style={{paddingVertical: 6}}>
                            <Text style={{marginLeft: '5%', paddingVertical: 4}}>{i18.borrower}</Text>
                            <TextInput
                                underlineColorAndroid='transparent'
                                style={{
                                    marginLeft: '5%',
                                    width: '90%',
                                    height: 30,
                                    borderRadius: 3,
                                    backgroundColor: "#FFFFFF",
                                    fontSize: 14,
                                    padding: 5,
                                    borderWidth: 1,
                                    borderColor: '#D9D9D9'
                                }}
                                onChangeText={(text) => this.setState({ borrower: text })}
                                value={this.state.borrower}
                            />
                        </View>

                        <View style={{paddingVertical: 6}}>
                            <Text style={{marginLeft: '5%', paddingVertical: 4}}>{i18.phone}</Text>
                            <View style={{flexDirection: 'row', marginLeft: '5%', width: '90%'}}>
                                {/* <TextInput
                                    underlineColorAndroid='transparent'
                                    style={{
                                        width: '37%',
                                        height: 30,
                                        borderRadius: 3,
                                        backgroundColor: "#FFFFFF",
                                        fontSize: 14,
                                        padding: 5,
                                        borderWidth: 1,
                                        borderColor: '#D9D9D9'
                                    }}
                                    onChangeText={(text) => this.setState({ areaCode: text })}
                                    value={this.state.areaCode}
                                /> */}
                                <ModalDropdown 
                                    dropdownStyle={styles.dropdownStyle} 
                                    animated={false} defaultIndex={0} options={this.props.areacodeLabels} 
                                    renderRow={(row, idx, highlighted)=>{
                                        return (
                                            <TouchableOpacity style={{padding: 5}} >
                                                <Text style={styles.dropdownLabel} >{"+" + this.props.areacodeLabels[idx]}</Text>
                                            </TouchableOpacity>
                                        );
                                    }} 
                                    onSelect={selected=>{
                                        this.setState({
                                            areaCode: this.props.areacodeValues[selected]
                                        });
                                    }}>
                                    <View style={styles.areacode}>
                                        <Text style={styles.areacodeText} >{"+" + this.state.areaCode}</Text>
                                    </View>
                                </ModalDropdown>
                                <TextInput
                                    underlineColorAndroid='transparent'
                                    style={{
                                        marginLeft: '3%',
                                        width: '60%',
                                        height: 30,
                                        borderRadius: 3,
                                        backgroundColor: "#FFFFFF",
                                        fontSize: 14,
                                        padding: 5,
                                        borderWidth: 1,
                                        borderColor: '#D9D9D9'
                                    }}
                                    onChangeText={(text) => this.setState({ phone: text })}
                                    value={this.state.phone}
                                />
                            </View>    
                        </View>


                        <View style={{paddingVertical: 6}}>
                            <Text style={{marginLeft: '5%', paddingVertical: 4}}>{i18.reason}</Text>
                            <View style={{flexDirection: 'row', marginLeft: '5%', width: '90%'}}>
                                <TextInput
                                    editable={false}
                                    underlineColorAndroid='transparent'
                                    style={{
                                        width: '90%',
                                        height: 30,
                                        borderRadius: 3,
                                        backgroundColor: "#FFFFFF",
                                        fontSize: 14,
                                        padding: 5,
                                        borderWidth: 1,
                                        borderColor: '#D9D9D9'
                                    }}
                                    onChangeText={(text) => this.setState({ reason: text })}
                                    value={this.state.reason}
                                />

                                <TouchableOpacity 
                                style={{ height: 25, width: '10%', backgroundColor: '#FFFFFF', justifyContent: 'center', alignItems: 'center',
                                borderWidth: 1, borderColor: '#D9D9D9', borderRadius: 3, }} onPress={() => {
                                    this.ActionSheet.show();
                                }}>     
                                    <View style={[styles.center]}>
                                        <Icon style={{ fontSize: 8, color: '#000', textAlign: 'center' }} icon={"DROPDOWN"}/>
                                    </View>  
                                </TouchableOpacity>  
                            </View>
                        </View>

                        <View style={{paddingVertical: 6}}>
                            <Text style={{marginLeft: '5%', paddingVertical: 4}}>{i18.otherReason}</Text>
                            <TextInput
                                underlineColorAndroid='transparent'
                                style={{
                                    marginLeft: '5%',
                                    width: '90%',
                                    height: 30,
                                    borderRadius: 3,
                                    backgroundColor: "#FFFFFF",
                                    fontSize: 14,
                                    padding: 5,
                                    borderWidth: 1,
                                    borderColor: '#D9D9D9'
                                }}
                                onChangeText={(text) => this.setState({ otherReason: text })}
                                value={this.state.otherReason}
                            />
                        </View>
                    </KeyboardAwareScrollView>       
                    <View style={{ backgroundColor: 'rgba(252, 252, 252, 0.9)', borderTopColor: '#D2CACA', borderTopWidth: 1, borderRadius: 8,
                    justifyContent: 'center', alignItems: 'center', flexDirection: 'row', position: 'absolute', width: '100%', bottom: 0 }}>
                        <TouchableOpacity onPress={() => this._hideModal()} style={{ flex: 1 }}>
                            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                                <Text style={{ color: '#2583F9', fontSize: 15, fontWeight: '500' }}>{i18.Cancel}</Text>
                            </View>
                        </TouchableOpacity>
                        <View style={{ height: 44, backgroundColor: '#D2CACA', width: 1 }} />
                        <TouchableOpacity onPress={() => {
                            let data = {
                                key_status: this.state.keyState,
                                unit_status: this.state.unitState,
                                current_owner: this.state.borrower,
                                dialing_code: this.state.areaCode,
                                mobile: this.state.phone,
                                reason: this.state.reason,
                                remark: this.state.otherReason,
                            };
                            if(data.current_owner && data.mobile && data.dialing_code){
                                props.updateUnitKey(data);
                                setTimeout(()=> {
                                    this._hideModal();
                                }, 0)
                            }else {
                                Alert.alert(
                                    i18.KeyAskForInput,
                                        '',
                                        [
                                            {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                                        ]
                                    )
                            }
                            
                        }} style={{ flex: 1 }}>
                            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><Text style={{ color: '#2583F9', fontSize: 15, fontWeight: '500' }}>{i18.update}</Text></View>
                        </TouchableOpacity>
                    </View> 
                </View>
            </View>
        ) : null;

		return (
			<View style={styles.container}>	
				<Nav 
					onRightPress={()=>this.onSubmit()} 
					right="SUBMIT"
					onLeftPress={()=>this.props.navigation.goBack()} 
					left="CLOSE">
					{translatedUnitAddress}
				</Nav>
				<View style={styles.header}>
                    <View>
					    <Text style={{fontWeight: '600', fontSize: 16, color: '#5A5A5A', lineHeight: 22}}>{i18.stage}：{unit.stage}</Text>
                    </View>
                    <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
                        <Text style={{fontWeight: '600', fontSize: 16, color: '#5A5A5A', lineHeight: 22}}>{i18.unitState}</Text>
                        <TouchableOpacity 
                        onPress={()=> this.ActionSheet.show()}
                        style={{width: 136, height: 25, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: '#FFFFFF'}}>
                            <Text style={{fontWeight: '600', fontSize: 14, textAlign: 'center', color: '#5A5A5A', marginLeft: 10 }}>{unit.status_int}</Text>
                            <Icon style={{ fontSize: 8, color: '#333333', textAlign: 'center', marginRight: 6, }} icon={"DROPDOWN"}/>
                        </TouchableOpacity>	
                    </View>
				</View>
				<ScrollableTabView
					tabBarPosition={'top'}
					style={styles.list}
					prerenderingSiblingsNumber={4}
                    initialPage={0}
					showDropDown={true}
					renderTabBar={() => <TabBar />}>
					{this.renderList('Checkbox')}
					{this.renderList('Checklist')}
					{this.renderList('Key')}
					{this.renderList('Item')}
				</ScrollableTabView>
				

				<ActionSheet
					ref={o => this.ActionSheet = o}
					title={!this.state.showKeyDropDown ? i18.ChangeStatus : i18.ChangeReason }
					options={!this.state.showKeyDropDown ? this.state.options : this.state.keyReasons}
					cancelButtonIndex={CANCEL_INDEX}
					onPress={(i) => this.handlePress(i)}
       			/>
                
                {showModal}

			</View>
		);
	}
}

const listStyles = StyleSheet.create({
	container: {
		padding: 3
	},
    checkIcon: {
        color: '#FF8238',
        width: 52,
		fontSize: 26,
		backgroundColor:'transparent',
		textAlign:'center',
    },
    tab: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
	},
	text:{
		fontWeight: '500', 
		fontSize: 14
	},
	line: {
        width: 1,
		height: 32,
		backgroundColor: '#9C9C9C',
    },
    keyText: {
        fontSize: 14,
        lineHeight: 24,
    },
});

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	header:{
		paddingHorizontal: 10,
		paddingVertical: 10,
	},
	list: {
		flex: 1
	},
	option:{
		flex: 1,
		borderBottomWidth: 1,
	},
	optionText:{
		padding: 20,
	},
	areacode:{
        height: 30,
        padding: 5,
        width: 70,
        marginRight: 5,
        backgroundColor: 'white',			
        borderWidth: 1,
        borderColor: '#B2B2B2',
        justifyContent: 'center'
    },
    areacodeText: {
		fontSize: 14
    },
    dropdownStyle: {
        position: 'absolute',
        top: 0, bottom: 0, left: 30, right: 30
    },
    dropdownLabel: {
        
    }
});

const mapStateToProps = (state, ownProps) => {
	let unit_address;
	if(state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0){
		if(state.setting.language == 'en') {
			unit_address = state.project.projectDetail.unit_address_en;
		}else if(state.setting.language == 'zh_hk') {
			unit_address = state.project.projectDetail.unit_address_zh;
		}else {
			unit_address = state.project.projectDetail.unit_address_cn;
		}
	}
    // alert("mapStateToProps:" + JSON.stringify(state.units.itemStatusSummary));
	return {
		projectDetail: state.project.projectDetail,
		user: state.auth.user,
		language: state.setting.language,
        unit: state.units.unit,
        unitItems: state.items.unitItems,
        units: state.units.units,
        unit_address: unit_address,
        viewUnitCheckList: state.checklists.viewUnitCheckList,
        items: state.items.items.filter(item => item.isDefect),
        qrcodeData: state.units.qrcodeData,
        showKeyForm: state.units.showKeyForm,
        users: state.project.users,
        areacodeLabels: state.setting.areacodeLabels,
        areacodeValues: state.setting.areacodeValues,
        itemStatusSummary: state.units.itemStatusSummary,
        checklistSummary: state.units.checklistSummary
	}
}


import { CheckLists as checkListsMapDispatchToProps, Items as itemsMapDispatchToProps, Units as unitsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';

let checkListsProps;
let itemsProps;
let unitsProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
    checkListsProps = checkListsMapDispatchToProps(dispatch, ownProps)
    itemsProps = itemsMapDispatchToProps(dispatch, ownProps)
	unitsProps = unitsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {...itemsProps, ...checkListsProps, ...unitsProps, ...projectProps};
})(UnitDetail);
