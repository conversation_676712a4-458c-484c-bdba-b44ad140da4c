import React, { Component } from 'react';
import {
    View,
    Text,
    TextInput, Alert
} from 'react-native';
import { connect } from 'react-redux';

import { Button } from 'Item';
const i18 = require('i18');
import {error} from 'Constant';

export default class RefreshLoginContractorView extends Component {

    constructor(props) {
        super(props);
        this.state = {
            username: '',
            password: '',
        }
    }


    inputValidation() {
        if (!this.state.username || this.state.username.length === 0) {
            this.refs['username'].focus();
            return false;
        }
        if (!this.state.password || this.state.password.length === 0) {
            this.refs['password'].focus();
            return false;
        }
        if (this.state.username !== this.props.user.username) {
            alert(i18.incorrectCredential);
            return false;
        }
        return true;
    }

    onConfirm = () => {
        if (this.inputValidation()) {
            this.props.refreshLogin(this.state.username, this.state.password).then(resp => {
               if(resp) {
                   Alert.alert(
                       i18.refreshSuccess,
                       '',
                       [
                           {text: i18.OK, onPress: () => {
                                   this.props.onDismiss();
                           }},
                       ]
                   )
               } else {
                alert(i18.incorrectCredential);
               }
            }).catch(err => {
                alert(i18.refreshFailed);
            });
        }
    }

    render() {
        return (
            <View style={styles.overlay}>
                <View style={styles.container}>
                    <Text style={styles.title}>{i18.refreshSession}</Text>
                    <View style={styles.body}>
                        <View style={styles.columnStyle}>
                            <Text>{i18.username}</Text>
                            <TextInput
                                ref={'username'}
                                underlineColorAndroid='transparent'
                                style={styles.inputStyle}
                                value={this.state.username}
                                onChangeText={username => this.setState({ username })} />
                        </View>
                        <View style={styles.columnStyle}>
                            <Text>{i18.password}</Text>
                            <TextInput
                                ref={'password'}
                                underlineColorAndroid='transparent'
                                secureTextEntry={true}
                                style={styles.inputStyle}
                                value={this.state.password}
                                onChangeText={password => this.setState({ password })} />
                        </View>
                        <View style={styles.rowStyle}>
                            <Button onPress={this.props.onDismiss}>{i18.chooseBack}</Button>
                            <Button onPress={this.onConfirm}>{i18.confirm}</Button>
                        </View>
                    </View>
                </View>
            </View>
        )
    }
}

const styles = {
    container: {
        backgroundColor: '#fff',
        display: 'flex',
        flexDirection: 'column',
        width: "100%"
    },
    title: {
        backgroundColor: '#aaa',
        color: '#fff',
        padding: 10,
    },
    body: {
        padding: 5,
        display: 'flex',
        flexDirection: 'column',
    },
    overlay: {
        position: 'absolute',
        top: 0, bottom: 0,
        left: 0, right: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        display: 'flex',
        paddingLeft: 20, paddingRight: 20
    },
    columnStyle: {
        padding: 5
    },
    rowStyle: {
        flexDirection: 'row',
        padding: 5,
        justifyContent: 'space-between'
    },
    inputStyle: {
        padding: 5,
        height: 40,
        borderWidth: 1
    }
}

const mapStateToProps = (state, ownProps) => {
    return {
        user: state.auth.user
    }
}

import {
    Login as loginMapDispatchToProps,
    Others as othersMapDispatchToProps,
    Loading as loadingMapDispatchToProps
} from 'Controller';

let loginProps;
let othersProps;
let loadingProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps) => {
    loginProps = loginMapDispatchToProps(dispatch, ownProps)
    othersProps = othersMapDispatchToProps(dispatch, ownProps)
    loadingProps = loadingMapDispatchToProps(dispatch, ownProps)
    return { ...loginProps, ...othersProps, ...loadingProps };
})(RefreshLoginContractorView);