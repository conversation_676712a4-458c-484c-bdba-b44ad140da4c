import React from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';

// Define props interface
interface LoadingProps {
	// Add any props if needed in the future
}

const Loading: React.FC<LoadingProps> = () => {
	return (
		<View style={styles.container}>
			<ActivityIndicator
				animating={true}
				style={[styles.centering, {transform: [{scale: 1.3}]}]}
				size="large"
				color="#fff"
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	centering: {
		alignItems: 'center',
		justifyContent: 'center',
	},
	container: {
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: 'rgba(0,0,0,0.3)',
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
	},
});

export default Loading;