import React, { Component } from 'react';
import {
    StyleSheet,
    Text,
    View,
    Image,
    TouchableOpacity,
    FlatList,
    Dimensions,
    ScrollView, Alert
} from 'react-native';
import { connect } from 'react-redux'

import { taskList as Theme, nav } from 'Theme';
import { Icon, Nav, DropDownList } from 'Item';
import i18 from 'i18';
import moment from 'moment';


export default class Units extends Component {

	constructor(props) {
		super(props);

		this.state = {
			dropDownList: {},
			dropDownData: [],
		};
	}

	componentWillReceiveProps(nextProps) {
		let { props } = this;
		let { language, units } = props;

		// 11/10: WARNING & TODO -- do the checking to prevent too much loading, have to double check the behaviour is correct or not
		if ((nextProps.units && nextProps.units.length != props.units.length) || nextProps.language != language ){
			let units = nextProps.units;
			let language = nextProps.language;

			// update the count in the tab using the number of checklist
			props.updateUnitsCount(units.length);

			let dropDownData = props.getUnitDropDownData(this.state.dropDownList, units, language);
			this.setState({ dropDownData });
		}
	}

	componentWillMount() {
		let { props } = this;
		props.fetchLocalUnits();
	}

	onAdd = () => {

	}

	onDownload(c) {
        if (c == 'none') {
            Alert.alert(
                i18.noInternetConnection,
                '',
                [
                    {text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
                ]
            )
            return
        }
		this.props.navigation.navigate("UnitDownload");
	}

	renderItem = (item) => {

		const styles = itemStyles;
		let { props } = this;
		let { unit_address } = props;

		let index = item.index;
		let itemDetail = item.item;

		let translatedUnitAddress;
		translatedUnitAddress = unit_address[itemDetail.type] || "";

		if (itemDetail.tower) {
			translatedUnitAddress = translatedUnitAddress.replace("{tower}", itemDetail.tower);
		}

		if (itemDetail.flat) {
			translatedUnitAddress = translatedUnitAddress.replace("{flat}", itemDetail.flat);
		}

		if (itemDetail.floor) {
			translatedUnitAddress = translatedUnitAddress.replace("{floor}", itemDetail.floor);
		}

		var date = itemDetail._created_at;
		date = moment.utc(date).format('YYYY-MM-DD HH:mm');
		date = moment.utc(date).toDate();
		date = moment(date).local().format('YYYY-MM-DD HH:mm');
		// console.log(itemDetail)
		var showVip = itemDetail.vip != "否" && itemDetail.vip != false ? (
			<Icon icon={'vip'} style={[styles.icon, { fontSize: 15, color: '#FFC800' }]} />
		) : null;

		return (
			<TouchableOpacity style={styles.container} key={index} onPress={() => {
				props.modifyUnit(itemDetail._id);
				props.updateQRcode("", false);
				props.getUnitFloorPlan(itemDetail._id);
				this.props.navigation.navigate("UnitDetail")
			}}>
				<View style={styles.detail}>
					<View>
						<View style={styles.address}>
							<Icon icon={'home'} style={[styles.icon, { fontSize: 20 }]} />
							<Text style={[styles.text, { fontWeight: 'bold' }]}>
								{translatedUnitAddress}
							</Text>
							{showVip}
						</View>

						<View style={styles.info}>
							<View style={{ marginLeft: 4, height: 25 }} />
							<Text style={styles.text}>{i18.stage + "： " + itemDetail.stage}</Text>
						</View>
					</View>
				</View>

				<View style={styles.dateContainer}>
					<Text style={styles.date}>{i18.unitState + ": " + itemDetail.status_int}</Text>
				</View>
			</TouchableOpacity>
		);
	}

	onShowDropDownList(data) {
		this.setState({
			dropDownList: data,
		});
	}

	onFilterList(title, list, filter) {
		let { props } = this;
		let { units, language } = props;

		var newDropDownData = this.state.dropDownData;
		let findIndexOfTitle = 0;

		for (var key in newDropDownData) {
			if (newDropDownData[key].title == title) {
				findIndexOfTitle = key;
			}
		}

		let dropDownList = this.state.dropDownList;

		newDropDownData.forEach((item, i) => {
			if (item.title == title) {
				let array;
				let compareValueExist = false;
				// filter.forEach((a)=>{
				// 	if(item.list.includes(a)){
				// 		compareValueExist = true;
				// 	}
				// });

				compareValueExist = filter.some((a) => {
					return item.list.includes(a);
				});

				if (!compareValueExist) {
					array = [];
				} else {
					array = filter;
				}

				if (!item.filter.includes(list)) {
					array.push(list);
				} else {
					var index = array.indexOf(list);
					array.splice(index, 1);
				}

				item.filter = array;
				dropDownList.filter = array;
			} else {
				if (i > findIndexOfTitle) {
					item.filter = [];
				}
			}
		});


		let dropDownData = props.getUnitDropDownData(newDropDownData, units, language);

		this.setState({
			dropDownData: dropDownData,
			dropDownList: dropDownList
		});

	}

	cleanDropDownList(title) {
		let { props } = this;
		let { units, language } = props;

		for (var key in this.state.dropDownData) {
			if (this.state.dropDownData[key].title == title) {
				findIndexOfTitle = key;
			}
		}

		let newDropDownData = this.state.dropDownData.map((data, i) => {
			if (data.title == title) {
				data.filter = [];
			} else {
				if (i > findIndexOfTitle) {
					data.filter = [];
				}
			}
			return data;
		});

		let dropDownData = props.getUnitDropDownData(newDropDownData, units, language);
		let dropDownList = this.state.dropDownList;

		if (dropDownList.title == title) {
			dropDownList.filter = [];

		}

		this.setState({
			dropDownData: dropDownData,
			dropDownList: dropDownList,
		});
	}


	closeDropDownList() {
		this.setState({
			dropDownList: {},
		});
	}

	render() {

		let { props } = this;
		let { units, connectionInfo } = props;

		let showDropDownList = Object.keys(this.state.dropDownList).length > 0 ? (
			<DropDownList
				closeDropDownList={() => this.closeDropDownList()}
				dropDownData={this.state.dropDownData}
				dropDownList={this.state.dropDownList}
				cleanDropDownList={(title) => this.cleanDropDownList(title)}
				onFilterList={(title, list, filter) => this.onFilterList(title, list, filter)}
			/>
		) : null;

		let filteredUnit;
		let dropDownData = this.state.dropDownData;
		let filter = {};

		if (dropDownData.length > 0) {
			if (dropDownData[0].filter.length > 0) {
				filter.type = dropDownData[0].filter;
			}
			if (dropDownData[1].filter.length > 0) {
				filter.tower = dropDownData[1].filter;
			}
			if (dropDownData[2].filter.length > 0) {
				filter.floor = dropDownData[2].filter;
			}
			if (dropDownData[3].filter.length > 0) {
				filter.flat = dropDownData[3].filter;
			}
			if (dropDownData[4].filter.length > 0) {
				filter.stage = dropDownData[4].filter;
			}
			if (dropDownData[5].filter.length > 0) {
				filter.status_int = dropDownData[5].filter;
			}
			if (dropDownData[6].filter.length > 0) {
				filter.vip_trans = dropDownData[6].filter;
			}



			filteredUnit = units.filter((item) => {
				for (let key in filter) {
					if (item[key] === undefined || !filter[key].includes(item[key])) {
						return false;
					}
				}
				return true;
			});
            filteredUnit.map(data => {
                data.sortKey = (data.tower + ' ' + data.floor + ' ' + data.flat).toLowerCase();
            });
            filteredUnit = filteredUnit.sort(function(a, b) {
                return naturalCompare(a.sortKey, b.sortKey);
            });
		}
		// console.log("filteredUnit", filteredUnit);

		let showTask = units.length > 0 ? (
			<FlatList style={styles.list} data={filteredUnit} renderItem={this.renderItem} />
		) : (
				<View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
					<Icon style={{
						fontSize: 100,
						backgroundColor: 'transparent',
						color: '#A8A8A8'
					}} icon={'no-report'} />
					<Text style={{ marginTop: 10, textAlign: 'center', width: 148, fontSize: 16, color: '#B9B9B9', lineHeight: 22, fontWeight: '600' }}>{i18.NoUnit}</Text>
				</View>
			);


		return (
			<View style={styles.container}>
				<Nav onLeftPress={this.onAdd}
					onRightPress={() => {this.onDownload(connectionInfo)}}
					right="DOWNLOAD"
					showDropDown={(filteredUnit && filteredUnit.length > 0)}
					onShowDropDownList={(val) => this.onShowDropDownList(val)}
					dropDownData={this.state.dropDownData}>
					{i18.Unit}
				</Nav>
				{showTask}
				{showDropDownList}
			</View>
		);
	}
}

const itemStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: Theme.item.bg,
		justifyContent: 'space-between',
		padding: 9,
		marginHorizontal: 5,
		marginTop: 8,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.5,
		shadowRadius: 2,

	},
	icon: {
		color: '#484848',
		width: 35,
		backgroundColor: 'transparent',
		textAlign: 'center',
		marginBottom: 5,
	},
	detail: {
		flex: 1,
		justifyContent: 'space-between',
	},
	address: {
		flex: 1,
		flexDirection: 'row',
		alignItems: 'center',
	},
	info: {
		flex: 1,
		flexDirection: 'row',
		alignItems: 'center',
	},
	text: {
		paddingHorizontal: 3,
		fontSize: 13,
	},
	bubble: {
		marginHorizontal: 2,
		paddingHorizontal: 6,
		paddingVertical: 3,
		borderRadius: 10,
		alignItems: 'center',
		justifyContent: 'center'
	},
	bubbleText: {
		fontSize: 12,
	},
	dateContainer: {
		flex: 0.6,
		padding: 5,
		alignItems: 'center',
	},
	date: {
		fontSize: 13,
		color: Theme.item.date,
	},

});

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: Theme.bg,
	},
	list: {
		flex: 1,
		width: '100%'
	},
});

const mapStateToProps = (state, ownProps) => {
	let unit_address;
	if (state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0) {
		if (state.setting.language == 'en') {
			unit_address = state.project.projectDetail.unit_address_en;
		} else if (state.setting.language == 'zh_hk') {
			unit_address = state.project.projectDetail.unit_address_zh;
		} else {
			unit_address = state.project.projectDetail.unit_address_cn;
		}
	}

	return {
		projectDetail: state.project.projectDetail,
		language: state.setting.language,
        connectionInfo: state.setting.connectionInfo,
		units: state.units.units.filter(item => item.checklists),
		unit_address: unit_address,
	}
}

import { Units as unitsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';
import naturalCompare from "string-natural-compare";

let projectProps;
let unitsProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps) => {
	unitsProps = unitsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return { ...unitsProps, ...projectProps };
})(Units);
