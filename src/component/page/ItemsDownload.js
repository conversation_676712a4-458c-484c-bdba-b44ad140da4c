'use strict';

import React, { Component } from 'react';
import {
	StyleSheet,
	View,
	TouchableOpacity,
	Dimensions,
    ScrollView,
    SectionList,
    ListView,
    Animated,
	FlatList,
	NetInfo,
	Alert,
} from 'react-native';

import Text from 'react-native-text';
import { theme, nav, taskList } from 'Theme';
import { Icon, Nav, DropDownList } from 'Item';
import i18 from 'i18';
import ScrollableTabView, { DefaultTabBar, } from 'react-native-scrollable-tab-view';
import { connect } from 'react-redux';


class TabBar extends Component {

	renderTab = (item, page) => {
        const styles = tabStyles;

		let { activeTab, tabs, goToPage, selectedData } = this.props;		
		var isTabActive = activeTab === page;

		return (
			<TouchableOpacity key={item.label} onPress={() => goToPage(page)} style={styles.tab}>
				<Text style={[styles.text]}>
					{ i18[item.label] } ({item.label == 'All' ? this.props.tasks.length : selectedData.length})
				</Text>
			</TouchableOpacity>
		);
	}

	render() {
		const styles = tabStyles;
		
		let { containerWidth, tabs, scrollValue } = this.props;
		let left = scrollValue.interpolate({ inputRange: [0, 1], outputRange: [0, containerWidth / tabs.length ] });

		return (
			<View style={styles.container}>
				<View style={styles.tabs}>
					{ tabs.map(this.renderTab) }
				</View>
				<Animated.View style={[styles.line, { width:containerWidth / tabs.length, left }]} />
			</View>
		);
	}
}

const tabStyles = StyleSheet.create({
	container:{
        height:50,
		borderBottomWidth:1,
		borderColor:'#888',
	},
	tab: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
	},
	tabs: {
		flex:1,
		flexDirection: 'row',
		justifyContent: 'center',
		backgroundColor:'#fff',
	},
	text:{
		fontSize: 14
	},
	line: {
		position: 'absolute',
		height: 3,
		backgroundColor: '#000',
		bottom: 0,
	}
});



export default class ItemsDownload extends Component {

    constructor(props) {
		super(props);
		this.state = {
			dropDownList: {},
            dropDownData: [],
			filterItems: false,
			selectedData: [],
			isFirstLoad: true,
			toggledVal: 0,
			toggleLabels: [i18.ItemTypeFollowup, i18.ItemTypeSatisfied, i18.ItemTypeAll]
		};
    }
    
    componentWillMount() {
        let { props } = this;
        let { projectDetail, language } = props;
        props.fetchItemDropdownOptions(projectDetail).then(dropDownData => {
            this.setState({
                dropDownData: dropDownData,
                isFirstLoad: false,
            });
        });
    }

    // Deprecated
   	// componentWillReceiveProps(nextProps) {
    	// console.log("***** nextProps", nextProps)
        // alert("componentWillReceiveProps")
		// let { props } = this;
        // let { projectDetail, language, items } = props;
        //
		// if(nextProps.items.length > 0 && nextProps.items && this.state.isFirstLoad){
		// 	let items = nextProps.items;
         //    let language = nextProps.language;
        //
			// let dropDownData = props.getItemDownlaodDropDownData(projectDetail, language, items);
		// 	this.setState({
		// 		dropDownData: dropDownData,
		// 		isFirstLoad: false,
		// 	});
		// }
    // }

    onShowDropDownList(data) {
		this.setState({
			dropDownList: data,
		});
    }
    
    onBack = ()=>{
		let { props } = this;
		props.cleanItemData();
		props.fetchLocalItems();
		this.props.navigation.goBack();
	}

    onToggle = () =>{
    	let counter = this.state.toggledVal;
    	if(counter < 2) {
            counter++;
		} else {
    		counter = 0;
		}
        this.setState({
            toggledVal: counter
		})
    }

	onFilterList(title, list) {
        // this.onSubmitFilter(false);
        var newDropDownData = this.state.dropDownData;
        newDropDownData.forEach((item, i) => {
			if(item.title == title) {
                if(!item.filter.includes(list)){
                    item.filter.push(list);
                }
                else {
					var index = item.filter.indexOf(list);
					item.filter.splice(index, 1);
				}
			}
        });

        this.setState({
			dropDownData: newDropDownData,
        });

	}
	
	cleanDropDownList(title) {

		let { props } = this;
		let { items, language } = props;

		let dropDownData = this.state.dropDownData.map((data)=>{
			if(data.title == title){
				data.filter = [];
			}
			return data;
		});
		this.setState({
			dropDownData: dropDownData
        });

		// Deprecated logic
        // let checkDropDownHasFilter = false;
        
        // dropDownData.forEach((item)=>{
        //     if(item.filter.length > 0){
        //         checkDropDownHasFilter = true;
        //     }
		// })
		
		// checkDropDownHasFilter = dropDownData.some((item) => {
		// 	return item.filter.length > 0;
		// });

        // if(checkDropDownHasFilter){
         //    this.onSubmitFilter(false);
		// }
		
		this.filterData(items, dropDownData);
	}

	checkUnitIsVip(tower, floor, flat) {
		let unitInfo = this.props.vipUnits.find((info) => {
			let match = false;
			if (info.tower) {
				match = info.tower == tower;
			}
			if (match) 
				if (info.floor) {
					match = info.floor == floor;
				}
			if (match) 
				if (info.flat) {
					match = info.flat == flat;
				}
			return match;
		});

		if (unitInfo) {
			return true;
		}

		return false;
	}

    renderItem(item, tab) {

        // console.log(item.item);
        var index = 0;
        const styles = itemStyles;
        let { props } = this;
        let { unit_address, filterData } = props;

        let translatedUnitAddress;
        translatedUnitAddress = unit_address[item.type] || "";
       
            
        if(item.tower) {
            translatedUnitAddress = translatedUnitAddress.replace("{tower}", item.tower);
        }

        if(item.flat) {
            translatedUnitAddress = translatedUnitAddress.replace("{flat}", item.flat);
        }

        if(item.floor) {
            translatedUnitAddress = translatedUnitAddress.replace("{floor}", item.floor);
        }

		let option = "";
		if (item.option) {
			if (item.option.length > 16) {
				option = item.option.substring(0, 13) + '...'
			} else {
				option = item.option
			}
		}
		let selected = false;
        if (this.state.selectedData.indexOf(item._id) > -1) {
			selected = true;
        }
                
		if(tab == 'All' || selected){
			return (
				<TouchableOpacity key={index} style={styles.container} onPress={() => {
						let selectedData = this.state.selectedData;

						if (selectedData.indexOf(item._id) > -1) {
							var index = selectedData.indexOf(item._id);
							selectedData.splice(index, 1);
						}else {
							selectedData.push(item._id);
						}

						this.setState({
							selectedData: selectedData,
						});

						let sectionData = filterData.slice();
						sectionData[0].fake = !sectionData[0].fake;

						props.fakeUpdateItemData(sectionData);
					}}>
					<View style={styles.selection}>
						<Icon icon={selected ? 'checked' : 'unchecked'} style={styles.checkIcon} />
					</View>
					<View style={styles.mainContainer}>
						<View style={styles.detail}>
							<View style={styles.address}>
								<View style={{flex: 1.8, flexDirection: 'row', alignItems:'center'}}>
									<Icon icon={'home'}  style={[styles.icon,{fontSize: 20}]} />
									{
										(this.checkUnitIsVip(item.tower, item.floor, item.flat)) ? (
											<Icon icon={'vip'} style={[styles.icon,{fontSize: 15, color: '#FFC800', width: 20}]} />
										) : null
									}
									<Text style={[styles.text, {fontWeight: 'bold', maxWidth: 140}]}>{translatedUnitAddress + " - " + item.location}</Text>
								</View>
								<View style={{flex: 1, flexDirection: 'row', justifyContent:'space-between', alignItems:'center'}}>
									<Text style={[styles.text, {fontWeight: 'bold', color: '#767676', width: 80}]}>{item.level1} {item.level2}</Text>
								</View>
							</View>

							<View style={[styles.info, option === '滿意'? styles.infoBorderSatisfied: styles.infoBorderFollowup]}>
                                <Text style={[styles.text, option === '滿意'? styles.optionsSatisfied: styles.optionsFollowup]}>{option}</Text>
							</View>
						</View>
					</View>
				</TouchableOpacity>
			);

		} else {
			return null
		}

		index++;
	}
	
    
    
    renderList(label) {

        const styles = listStyles;
        let { props } = this;
		let { items, filterData } = props;
        filterData.map(data => {
            data.sortKey = (data.tower + ' ' + data.floor + ' ' + data.flat).toLowerCase();
        });
        filterData = filterData.sort(function(a, b) {
            return naturalCompare(a.sortKey, b.sortKey);
        });
		
		let filteredItems;
		let dropDownData = this.state.dropDownData;
		let filter = {};

        if(label == 'All' && filterData.length > 0) {
            return (
                <View style={styles.container} tabLabel={{label}}>
                    <FlatList style={styles.list} data={filterData} renderItem={({item}) => this.renderItem(item, 'All')} />
                </View>
            );
        }else if(label == 'Selected' && filterData.length > 0) {
            return (
                <View style={styles.container} tabLabel={{label}}>
                    <FlatList style={styles.list} data={filterData} renderItem={({item}) => this.renderItem(item, 'Selected')} />
                </View>
            );
        }
	}

	filterData(sectionData, newDropDownData) {
		
		let { props } = this;

		let filteredSectionData;
		let dropDownData = newDropDownData;
		let filter = {};
        
        if(dropDownData.length > 0){
			if(dropDownData[0].filter.length > 0) {
				filter.status = dropDownData[0].filter;
            }
            if(dropDownData[1].filter.length > 0) {
				filter.tower = dropDownData[1].filter;
			}
			if(dropDownData[2].filter.length > 0) {
				filter.floor = dropDownData[2].filter;
			}
			if(dropDownData[3].filter.length > 0) {
				filter.flat = dropDownData[3].filter;
            }
			if(dropDownData[4].filter.length > 0) {
				filter.location = dropDownData[4].filter;
			}
			if(dropDownData[5].filter.length > 0) {
				filter.level1 = dropDownData[5].filter;
            }
            if(dropDownData[6].filter.length > 0) {
				filter.level2 = dropDownData[6].filter;
			}
	
			filteredSectionData = sectionData.filter((item) => {
				for(let key in filter) {
					if(item[key] === null || item[key] === undefined || !filter[key].includes(item[key])){
						return false;
					}
				}
				return true;
			});
			props.filterItem(filteredSectionData);
		}
		
	}

    
    // updateListSelection(selected) {

    //     let { props } = this;
    //     let { items } = props;
   
    //     var tasks = items;
    //     tasks.map((task)=>{
    //         task.selected = selected;
    //     });

    //     props.updateListSelection(tasks);

    // }

    onSubmitFilter(val) {
		let {dropDownData} = this.state;
 		if(dropDownData[1].filter.length === 0  || dropDownData[2].filter.length === 0) {
			// || dropDownData[3].filter.length === 0) {
            alert(i18.itemSearchMandatoryMsg);
 			return;
		}

        let { props } = this;
        let { projectDetail } = props;
        props.searchRespItems(projectDetail._id, this.state.dropDownData, this.state.toggledVal);
	}
	
	closeDropDownList() {
		this.setState({
			dropDownList: {},
		});
	}

	render() {
        let { props } = this;
		let { items, projectDetail, filterData, connectionInfo } = props;
		var showDropDownList = Object.keys(this.state.dropDownList).length > 0  ? (
			<DropDownList 
				dropDownList={this.state.dropDownList}
				dropDownData={this.state.dropDownData}
                closeDropDownList={()=> this.closeDropDownList()}
				cleanDropDownList={(title) => this.cleanDropDownList(title)}
				onFilterList={(title, list)=> this.onFilterList(title, list)}
				/>
        ) : null;
        
        let showItem;
		if(items.length > 0){
			showItem = (
				<ScrollableTabView
					tabBarPosition={'top'}
					style={styles.list}
					prerenderingSiblingsNumber={3}
					renderTabBar={() => <TabBar  tasks={filterData} selectedData={this.state.selectedData}/>}>
					{this.renderList('All')}
					{this.renderList('Selected')}
				</ScrollableTabView>
			)
		}else {
			showItem = (
				<View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
					<Icon style={{
						fontSize: 100,
						backgroundColor:'transparent',
						color: '#A8A8A8'}} icon={'report'}/>
					<Text style={{marginTop: 10, textAlign: 'center', width: 148, fontSize: 16, color: '#B9B9B9', lineHeight: 22, fontWeight: '600'}}>{i18.NoItem}</Text>
				</View>
			)
        }
        

        let showFilterButton = false;
        let dropDownData = this.state.dropDownData;

        if(dropDownData.length > 0){
            if(dropDownData[0].filter || dropDownData[1].filter || dropDownData[2].filter || dropDownData[3].filter ||
                dropDownData[4].filter || dropDownData[5].filter || dropDownData[6].filter) {
                    showFilterButton = true;
            }
        }

		return (
			<View style={styles.container}>
                <Nav 
                    left='BACK' 
					showDropDown={true} 
                    showFilterButton={showFilterButton}
                    onSubmitFilter={() => {
                        this.onSubmitFilter(true);
                        this.filterData(items, this.state.dropDownData);
                    }}
                    onLeftPress={this.onBack}
					onShowDropDownList={(val)=>this.onShowDropDownList(val)} 
					dropDownData={this.state.dropDownData}

                    right='TOGGLE'
                    onRightPress={this.onToggle}
                    toggleLabels={this.state.toggleLabels}
					toggledVal={this.state.toggledVal}>
					{i18.ItemDownloadList}
				</Nav>
                {showItem}
                <View style={styles.bottomContainer}>
                    <TouchableOpacity  onPress={() => {
						let selectedData = this.state.selectedData.slice();
						filterData.forEach((item)=> {
                            if(!selectedData.includes(item._id)){
                                selectedData.push(item._id);
                            }
						})

						this.setState({selectedData: selectedData});
						// let sectionData = filterData.map((section, i) => {
						// 	if(i == 0) {
						// 		section.fake = section.fake ? false : true;
						// 	}
						// 	return section;   
						// });
						let sectionData = filterData.slice();
						if (sectionData.length > 0)
							sectionData[0].fake = !sectionData[0].fake;
						props.fakeUpdateItemData(sectionData);
					}}>
                    <View style={[styles.bottomButtonContainer, {borderColor: '#E1E1E1'}]}>
                        <Text style={[styles.bottomButtonText, {color: '#FF8136'}]}>{i18.chooseAll}</Text>
                    </View>
                    </TouchableOpacity>
                     <TouchableOpacity  onPress={() => {
						this.setState({selectedData: []})
                        //  let sectionData = filterData.map((section, i) => {
						// 	if(i == 0) {
						// 		section.fake = section.fake ? false : true;
						// 	}
						// 	return section;   
						// });
						let sectionData = filterData.slice();
						if (sectionData.length > 0) {
							sectionData[0].fake = !sectionData[0].fake;
						}
						props.fakeUpdateItemData(sectionData);
					}}>
                     <View style={[styles.bottomButtonContainer, {borderColor: '#FF863E'}]}>
                       <Text style={[styles.bottomButtonText, {color: '#FF8136'}]}>{i18.deselect}</Text>
                    </View>
                    </TouchableOpacity>
                    <TouchableOpacity  onPress={() => {
                    	if(this.state.selectedData.length === 0) {
                    		alert(i18.noDownLoadSelectionWarningMsg);
                    		return;
                    	}
						if(connectionInfo == 'wifi' || connectionInfo == 'wimax' || connectionInfo == 'ethernet') {
							props.downloadItem(this.state.selectedData);
							this.setState({selectedData: []});
						} else if(connectionInfo == 'none'){
							Alert.alert(
							i18.noInternetConnection,
								'',
								[
									{text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
								]
							)
						} else {
							Alert.alert(
							i18.noWifiDownload,
								'',
								[
									{text: i18.Cancel, onPress: () => console.log('Cancel Pressed')},
									{text: i18.download , onPress: () => {
										props.downloadItem(this.state.selectedData);
										this.setState({selectedData: []});
									}},
								]
							)
						}

					}}>  
                     <View style={[styles.bottomButtonContainer, {borderColor: '#FF8034', backgroundColor: '#FF8034'}]}>
                        <Text style={[styles.bottomButtonText, {color: '#FFFFFF'}]}>{i18.download}</Text>
                    </View>
                    </TouchableOpacity>      
                </View>    
                {showDropDownList}
			</View>
		);
	}
}


const itemStyles = StyleSheet.create({
	container: {
		flexDirection: 'row',
        marginTop: 8,
        minHeight: 84,
    },
    mainContainer: {
        flex: 2,
        backgroundColor: taskList.item.bg,
        flexDirection: 'row',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,
    },
    selection: {
        flex: 0.5 ,
        alignItems:'center',
        justifyContent:'center',	
    },
    checkIcon: {
        color: '#FF8238',
        width: 52,
		fontSize: 26,
		backgroundColor:'transparent',
		textAlign:'center',
    },
	icon: {
        color: '#484848',
        width: 44,
		backgroundColor:'transparent',
		textAlign:'center',
	},
    detail: {
		flex: 1,
	},
	address: {
		flex:1,
		flexDirection: 'row',
		alignItems:'center',
		paddingLeft: 5,	
	},
	info: {
		flex:1,		
		flexDirection: 'row',
		alignItems:'center',
		backgroundColor: '#F5F5F5',
		borderRightWidth: 1,
		borderRightColor: '#FFFFFF',
		borderBottomWidth: 3,
		// borderBottomColor: '#C77777',
		paddingLeft: 5,	
	},
	infoBorderSatisfied: {
        borderBottomColor: '#69b29d',
	},
    infoBorderFollowup: {
        borderBottomColor: '#C77777',
    },
	text: {
		paddingHorizontal: 3,
		color: taskList.item.text,
		fontSize:14,
	},
	statusContainer: {
		flex:1,	
		backgroundColor: "#F5F5F5",
		alignItems: 'center',
		justifyContent: 'center',
	},
	statusText: {	
		fontSize: 14,
		color: '#353535',
		fontWeight: 'bold'
	},
    optionsSatisfied: {
        color: '#69b29d'
    },
    optionsFollowup: {
        color: '#C77777'
    },
});


const listStyles = StyleSheet.create({
	container: {
        flex: 1,
	},
	header: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingVertical:12,
	},
	headerField: {
		flex: 2,
		fontSize: 12,
		textAlign: 'center',
	},
	list: {
		flex: 1,
	},
	popUp: {
		flexDirection: 'row',
		position: 'absolute',
		bottom: 0,
		left: 0,
		right: 0,

		borderTopColor: 'black',
		borderTopWidth: 0.5,
		justifyContent: 'space-around',
		alignItems: 'center',
		height: 40,
	},
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
		backgroundColor: taskList.bg,

    },
    list: {
        flex: 1,
        width: '100%',
    },
    dropDownListContainer: {
		backgroundColor: '#fff',
		width: Dimensions.get('window').width * 0.85,
		height: Dimensions.get('window').height * 0.8,
		marginBottom: Dimensions.get('window').height * 0.05,
		borderWidth: 1,
    },
    bottomContainer: {
      padding: 5,  
      height: 44,
      backgroundColor: '#FFF',
      borderTopWidth: 1,
      borderColor: '#AEAEAE' ,
      flexDirection: 'row',
      justifyContent: 'space-between',
	  alignItems: 'center',
    },
    bottomButtonContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        height: 31,
        width: 95,
        borderWidth: 1,
        borderRadius: 4,
    },
    bottomButtonText: {
        textAlign: 'center',
        width: 90,
        fontSize: 16,
        fontWeight: 'bold',
    },
});

const mapStateToProps = (state, ownProps) => {
	// console.log("mapStateToProps", state);
    let unit_address;
	if(state.project.projectDetail != null && Object.keys(state.project.projectDetail).length > 0){
		if(state.setting.language == 'en') {
			unit_address = state.project.projectDetail.unit_address_en;
		}else if(state.setting.language == 'zh_hk') {
			unit_address = state.project.projectDetail.unit_address_zh;
		}else {
			unit_address = state.project.projectDetail.unit_address_cn;
		}
    }
    return {
		items: state.items.items.filter(item => !item.status_history),
		filterData: state.items.filterData.filter(item => !item.status_history),
        unit_address: unit_address,
        projectDetail: state.project.projectDetail,
		language: state.setting.language,
		connectionInfo: state.setting.connectionInfo,
		vipUnits: state.project.vipUnits
    }
}


import { Items as itemsMapDispatchToProps, Project as projectMapDispatchToProps } from 'Controller';
import naturalCompare from "string-natural-compare";

let itemsProps;
let projectProps;

module.exports = connect(mapStateToProps, (dispatch, ownProps)=>{
	itemsProps = itemsMapDispatchToProps(dispatch, ownProps)
	projectProps = projectMapDispatchToProps(dispatch, ownProps)
	return {...itemsProps, ...projectProps};
})(ItemsDownload);

