import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import { Provider } from 'react-redux';
import { Store } from 'redux';
import { RootState } from '../types';
import App from './App';
import { Loading } from './page';
const key = require('../service/key/key');

const initStore = require('../redux').init;

const Root: React.FC = () => {
  const [store, setStore] = useState<Store<RootState, any> | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    key().then((k: string) => {
      setStore(initStore(k));
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <Loading />;
  }

  if (store) {
    return (
      <Provider store={store}>
        <App />
      </Provider>
    );
  }

  return <View />;
};

export default Root;
