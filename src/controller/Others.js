import Server from 'Server'

const Actions = require('Redux').Action
import globalVal from '../../src/globalVal.js'
import LocalDB from 'LocalDB'
import CachedImage from 'react-native-cached-image'

const {ImageCacheProvider} = CachedImage
import <PERSON><PERSON> from 'react-native-cookie'
import { _getHeaders, _getServerUrl } from 'Request'
import { SERVER_URL } from 'Config'
import { Platform } from 'react-native'
import { Crashlytics } from 'react-native-fabric'

function downloadImage(picture) {
	console.log(`picture ${picture}`);
	if (!picture.startsWith("http")) {
		let serverUrl = _getServerUrl();
		picture = serverUrl + "blob_image/" + picture;
	}
	return ImageCacheProvider.deleteCachedImage(picture). then(() => {
		return ImageCacheProvider.cacheImage(picture, {}, _getHeaders).then((imagePath)=>{
			return imagePath;
		}).catch((err) => {
			console.log("downloadImage failed", err);
			return picture;
		});
	});
}

const mapDispatchToProps = (dispatch, ownProps) => {
  return {
		changeLanguage: (lang)=>{
			dispatch(Actions.setting.changeLanguage(lang));
		},
		cameraStatus: (data)=>{
			dispatch(Actions.setting.cameraStatus(data));
		},
    logout: () => {
      return Server.logout().then(() => {
        return Cookie.clear()
      }).then(() => {
        dispatch(Actions.navigation.replace('Login'))
      }).then(() => {
        dispatch(Actions.auth.loggedOutUser())
        dispatch({
          type: 'DF_APP_RESET'
        })
      }).catch(err => {
        if (Platform.OS !== 'ios') {
          Crashlytics.logException(`Cannot logout: ${JSON.stringify(err)}`)
        } else {
          Crashlytics.recordError(`Cannot logout: ${JSON.stringify(err)}`)
        }
        dispatch(Actions.navigation.replace('Login'))
      })
    },
		changeProject: async (item, user, projectLists, context, navigation) => {
			globalVal.stopShowingWelcome = true;
			dispatch({ type: "DF_APP_RESET"	});
			dispatch(Actions.loading.api('fetchProjectDetail'));
			if (context.store.persistor) {
				context.store.persistor.purge();
			}
			await LocalDB.clearAll();
      await ImageCacheProvider.clearCache();

			setTimeout(() => {
				dispatch(Actions.auth.loggedInUser(user));
				dispatch(Actions.project.fetchProjectLists(projectLists));
				console.log("setMainNavigation", navigation);
				dispatch(Actions.navigation.setMainNavigation(navigation));

				Server.fetchProjectDetail(item._id).then(projectDetail =>{
					dispatch(Actions.loading.apiSuccess('fetchProjectDetail'));
					dispatch(Actions.loading.api('fetchUsers'));
					return Server.fetchUsers()
						.then((users) => {
							dispatch(Actions.project.fetchUsers(users));
              dispatch(Actions.loading.apiSuccess('fetchUsers'))
              dispatch(Actions.loading.api('fetchUnitInfo'))
              return Server.fetchUnitInfo(projectDetail._id).then((unitsInfo) => {
                let image_list = new Set()
                unitsInfo.forEach((unit) => {
                  if (unit.floor_plans) {
                    unit.floor_plans.forEach(i => image_list.add(i))
                  }
                })
                image_list = [...image_list].map((value) => {return async () => {return await downloadImage(value)}})
                const cancelable = true
                dispatch(Actions.loading.storage('fetchProjectDetail'))
								dispatch(Actions.progressBar.reset(image_list.length, true, cancelable))
								let successCount = 0;
                Promise.sequence(image_list, (subResult, step, total) => {
                  console.log(`subResult ${subResult}`);
									console.log(`step ${step}`);
									if(step > 0 && subResult){
										successCount = successCount + 1;
									}
                  dispatch(Actions.progressBar.progress(step, subResult instanceof Error, cancelable, successCount))
                }, {
                  skipIfError: true, cancelable
                }).then().catch((err) => { console.error(err)})
                dispatch(Actions.loading.apiSuccess('fetchUnitInfo'))
                setTimeout(() => {
                  dispatch(Actions.project.fetchUnitsInfo(unitsInfo))
                  dispatch(Actions.project.fetchProjectDetail(projectDetail))
                  dispatch(Actions.loading.storageSuccess('fetchProjectDetail'))
                })
							});
						});
				})
				.catch(error => {
					console.warn('error',error);
					dispatch(Actions.loading.apiSuccess('fetchProjectDetail'));
				})


				dispatch(Actions.project.updateSelectedProject(item));
			}, 0);
		},
		networkInfoUpdate: (info) => {
			dispatch(Actions.setting.networkInfoUpdate(info));
			if (info != 'none') {
				Server.getAreaCode()
					.then((list) => {
						let values = [];
						let labels = [];
						list.forEach((area) => {
							values.push(area.value);
							labels.push(area.label);
						});
						dispatch(Actions.setting.setAreacodeData(labels, values));
					});
			}
		}
  }
}

module.exports = mapDispatchToProps;
