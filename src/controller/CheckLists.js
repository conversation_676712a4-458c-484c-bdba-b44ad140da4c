import ImagePicker from 'react-native-image-picker';

import Server from 'Server';
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import { delay } from 'Utilities';
import i18 from 'i18';

const Actions = require('Redux').Action;

const mapDispatchToProps = (dispatch, ownProps) => {
  return {
		fetchChecklists: (code) => {
			dispatch(Actions.loading.api('fetchChecklists'));
			console.logTime('fetchChecklists start');			
            return Server.fetchChecklists(code).then(resp => {	
                console.logTime('fetchChecklists end');			
				return dispatch(Actions.checklists.fetched(resp, false));
				// return true;
			}).then(() => {
                return dispatch(Actions.loading.apiSuccess('fetchChecklists'));
			}).catch(error => {
				console.warn('fetchChecklists error',error);
				alert(error);				
                dispatch(Actions.loading.apiSuccess('fetchChecklists'));
				return false;
            })
		},
      	fetchChecklistDropdownOptions: (projectId, language) => {
            dispatch(Actions.loading.api('getCheckListDownloadDropDownData'));
            return Server.fetchChecklistDropdownOptions(projectId).then(resp => {
                let dropDownData = [
                    {
                        title: i18.Checklist,
                        list: resp.name || [],
                        filter: [],
                    },
                    {
                        title: i18.tower,
                        list: resp.tower || [],
                        filter: [],
                    },
                    {
                        title: i18.floor,
                        list: resp.floor || [],
                        filter: [],
                    },
                    {
                        title: i18.flat,
                        list: resp.flat || [],
                        filter: [],
                    }
                ];
                dispatch(Actions.loading.apiSuccess('getCheckListDownloadDropDownData'));
                return dropDownData;
            }).catch(err => {
                alert(err);
                dispatch(Actions.loading.apiSuccess('getCheckListDownloadDropDownData'));
            })
		},
        // updateListSelection: (tasks) => {
        //     dispatch(Actions.checklists.selected(tasks));
        // },
        downloadChecklist: (checklist, code)=>{
			// Download following checklist
			// console.log('downloading checklist:',checklist);

			// create an object instance for cancel
			let cancelable = {};			

			let asyncTask = checklist.map(c => ()=>{
				return Server.fetchChecklist(code, c).then(result=>{
					result.questions.forEach((question)=>{
						if(typeof question.selectOptions == "undefined") {
							question.selectOptions = [];
							question.status = 'Unfill';
							question.otherText = "";
						}
					})

					let itemDetail = result.questions;
					let formatResult = [], map = {}, idx = 0, hardcodeIndex = 0, locationID = 0, currentIndex = 1;
					for(let element of itemDetail){
						let curr = 0;
						if(map[element.location] !== undefined){
							curr = map[element.location];
						}
						else{
							curr = idx;
							map[element.location] = idx++;
							locationID++;
							formatResult.push({data : [], location : element.location, locationID: locationID});
						}
			
						formatResult[curr].data.push({
							status: element.status,
							description: element.description,
							auto_assign_to_user: element.auto_assign_to_user,
							level1: element.level1,
							level2: element.level2,
							location : element.location,
							options: element.options,
							_id: element._id, 
							selectOptions: element.selectOptions,
							otherText: element.otherText,
							currentIndex: currentIndex,
							images: element.pictures,
						});
						hardcodeIndex++;
						currentIndex++;
					}
			
					result.questions = formatResult;
					result.downloadFromChecklist = true;
					// Put it to LocalDB
					return LocalDB.save('Checklist',result).then(resp => result);
				})
			});

			console.logTime('downloadChecklist start');
			dispatch(Actions.progressBar.reset(asyncTask.length,true,cancelable));
			
			let successCount = 0;
			Promise.sequence( asyncTask, (subResult, step, total)=>{
				if(step > 0 && subResult && subResult._id){
					successCount = successCount + 1;
				}
				dispatch(Actions.progressBar.progress(step, subResult instanceof Error, cancelable, successCount));
			},{
				skipIfError: true,
				cancelable
			}).then(result=>{
				console.logTime('downloadChecklist end');

				//console.log('Promise.sequence result:',result);
				// console.log('result:',result.map(r => r._id));

				// Put them to redux, therefore, UI will refresh
				result = result.filter((item)=> {
					return item._id
				})
				dispatch(Actions.checklists.fetched(result, false));

				return result;
			}).catch(err=>{
				// should not be here				
				console.warn('err:',err);				
				alert(err);
			});
		},
		fetchLocalChecklists: () => {
			dispatch(Actions.loading.storage('fetchLocalChecklists'));
			// console.logTime('fetchLocalChecklists start');
			setTimeout(()=>{
				LocalDB.fetch('Checklist').then(resp => {
					// console.log("fetchLocalChecklists", resp);	
					// console.logTime('fetchLocalChecklists end');
                    return dispatch(Actions.checklists.fetched(resp, false));
				}).then(() => {
					return dispatch(Actions.loading.storageSuccess('fetchLocalChecklists'));	
				}).catch(error => {
					console.warn('fetchLocalChecklists error',error);				
					dispatch(Actions.loading.storageSuccess('fetchLocalChecklists'));
				});
			},0);
		},
		// fetchLocalAdhocOptions: () => {
		// 	dispatch(Actions.loading.storage('fetchLocalAdhocOptions'));
		// 	setTimeout(()=>{
		// 		LocalDB.fetch('AdhocFormOption').then(resp => {
		// 			dispatch(Actions.loading.storageSuccess('fetchLocalAdhocOptions'));
		// 			dispatch(Actions.checklists.fetchedAdhocFormOption(resp));
		// 		}).catch(error => {
		// 			console.warn('fetchLocalAdhocOptions error',error);				
		// 			dispatch(Actions.loading.storageSuccess('fetchLocalAdhocOptions'));
		// 		});
		// 	},0);
		// },
		modifyCheckList: (id) => {
			dispatch(Actions.checklists.modifyCheckList(id));
		},
		modifyAdhocCheckList: (id) => {
			dispatch(Actions.checklists.modifyAdhocCheckList(id));
		},
		modifyUnitCheckList: (items, id) => {
			dispatch(Actions.checklists.modifyUnitCheckList(items, id));
		},
		filterCheckList: (label, data, filteredChecklist) => {
			dispatch(Actions.checklists.filterCheckList(label, data, filteredChecklist));
		},
		searchChecklists: (project, data) => {
			dispatch(Actions.loading.api('searchChecklists'));
			// console.logTime('searchRespItems start');
			const checklistFilter = (!data[0].filter || data[0].filter.length === 0)? null: data[0].filter;
			const towerFilter = (!data[1].filter || data[1].filter.length === 0)? null: data[1].filter;
			const floorFilter = (!data[2].filter || data[2].filter.length === 0)? null: data[2].filter;
			const unitFilter = (!data[3].filter || data[3].filter.length === 0)? null: data[3].filter;
			Server.fetchChecklistsCustom(project, towerFilter, floorFilter, unitFilter, checklistFilter).then(resp => {
			  // console.logTime('searchRespItems end');
			  dispatch(Actions.loading.apiSuccess('searchChecklists'));
			  dispatch(Actions.checklists.fetched(resp, true));
			}).catch(error => {
			  // console.log('error: ',error);
			  alert(i18.getItemErrMsg);
			  dispatch(Actions.loading.apiSuccess('searchChecklists'));
			})
		},
		fakeUpdateUnfillData: (data) => {
			dispatch(Actions.checklists.fakeUpdateUnfillData(data));
		},
		fakeUpdateAdhocData: (data) => {
			dispatch(Actions.checklists.fakeUpdateAdhocData(data));
		},
		updateSelectOptions: (data, label, id, option, from) => {
			dispatch(Actions.checklists.updateSelectOptions(data, label, id, option, from));
		},
		updateLabelData: (data) => {
			dispatch(Actions.loading.storage('updateLabelData'));
			setTimeout(()=>{
				dispatch(Actions.checklists.updateLabelData(data));
				dispatch(Actions.loading.storageSuccess('updateLabelData'));
			},0);
		},
		updateFloorPlanAnnotations: (id, annotations) => {
			dispatch(Actions.checklists.updateFloorPlanAnnotations(id, annotations));
		},
		updateItemImages: (id, paths) => {
			dispatch(Actions.checklists.updateItemImages(id, paths));
		},
		reCalLabelData: () => {
			dispatch(Actions.checklists.reCalLabelData());
		},
		updateOtherText: (id, otherText) => {
			dispatch(Actions.checklists.updateOtherText(id, otherText));
		},
		updateComments: (id, text) => {
			dispatch(Actions.checklists.updateComments(id, text));
		},
		pendingCheckLists: (checklist) => {
			dispatch(Actions.loading.storage('pendingCheckLists'));
			setTimeout(()=>{
				dispatch(Actions.checklists.pendingCheckLists(checklist));
				dispatch(Actions.project.updatePendingCount("+1"));
				dispatch(Actions.loading.storageSuccess('pendingCheckLists'));
			},0);
		},
		fetchLocalPendingChecklists: () => {
			dispatch(Actions.loading.storage('fetchLocalPendingChecklists'));
			// console.logTime('fetchLocalPendingChecklists start');
			setTimeout(()=>{
				LocalDB.fetch('PendingCheckList').then(resp => {	
					// console.logTime('fetchLocalPendingChecklists end');
					// console.log('fetchLocalPendingChecklists:',resp);
					dispatch(Actions.loading.storageSuccess('fetchLocalPendingChecklists'));
					dispatch(Actions.checklists.fetchedPendingCheckLists(resp));
				}).catch(error => {
					console.warn('fetchLocalPendingChecklists error',error);				
					dispatch(Actions.loading.storageSuccess('fetchLocalPendingChecklists'));
				});
			},0);
		},
		filterTask: (data) => {
			dispatch(Actions.checklists.filterTask(data));
		},
		fakeUpdateTaskData: (data) => {
			// dispatch(Actions.checklists.fakeUpdateTaskData(data));
		},
		getAdHocFilter: (tower, floor, flat, location, level1, level2, isInspection = true) => {
			
			let adhoc_options = { tower, floor, flat, location, level1, level2, isInspection };

			dispatch(Actions.project.getAdHocFilter(adhoc_options));
		},
		createAddHocCheckList: (data) => {
			if(!data.id) {
				data.label = data.firstOption
				if(data.firstOption == '額外清單' || data.firstOption == 'Special') {
					data.firstOption = 'Special';			
				}else {
					data.firstOption = 'Inspection';	
				}

				if(data.secondOption == '內部' || data.secondOption == 'Internal') {
					data.secondOption = 'adhoc';			
				}else {
					data.secondOption = 'customer';	
				}
			}
			dispatch(Actions.checklists.createAddHocCheckList(data));
		},
		resetChecklist: () => {
			dispatch(Actions.checklists.resetChecklist());
		},
		modifyAddHocCheckList: (data) => {
			dispatch(Actions.checklists.modifyAddHocCheckList(data));
		},
		deleteAdhocData: (id, data) => {
			dispatch(Actions.checklists.deleteAdhocData(id, data));
		},
		clearChecklists: () => {
			dispatch(Actions.checklists.clearChecklists());
		}
    }
}

module.exports = mapDispatchToProps;