import ImagePicker from 'react-native-image-picker';

import Server from 'Server';
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import { delay } from 'Utilities';
import i18 from 'i18';

const Actions = require('Redux').Action;

import CachedImage  from 'react-native-cached-image';
const {
    ImageCacheProvider
} = CachedImage;

import { _getHeaders, _getServerUrl } from 'Request';
import { SERVER_URL } from 'Config';

async function downloadEachUnit(c, code){

	let eachResult;
    let validResponses = [];
	return Server.fetchUnit(c).then(resp =>{
        eachResult = resp;
		return Promise.all([Server.fetchItemStatusSummary(eachResult._id)
			, Server.fetchChecklistRespSummary(eachResult._id)]).then(results => {
				const itemStatuses = results[0] || [];
				const checklistsSum = results[1] || [];
            	LocalDB.save('ItemSummary', itemStatuses).then(resp => {/* console.log("item summary", resp) */});
            	LocalDB.save('ChecklistSummary', checklistsSum).then(resp => {/* console.log("item summary", resp) */});
        }).then(()=>{
        	return eachResult
		}).catch(err => {
            // Do nothing, use original responses
    	});
	});
}

function downloadImage(picture) {
	let path = picture.path;
	if (!path) {
		path = picture;
	}
	if (!path.startsWith("http")) {
		let serverUrl = _getServerUrl();
		path = serverUrl + "blob_image/" + path;
	}
	return ImageCacheProvider.deleteCachedImage(path).then(() => {
		return ImageCacheProvider.cacheImage(path, {}, _getHeaders).then((imagePath)=>{
			return imagePath;
		}).catch((err) => {
			// console.log("downloadImage failed", err);
			return path;
		});
	});
}

const mapDispatchToProps = (dispatch, ownProps) => {
  return {
	fetchUnits: (project) => {
		dispatch(Actions.loading.api('fetchUnits'));
		// console.logTime('fetchUnits start');
		Server.fetchUnits(project).then(resp => {
			resp.map((item)=>{
				if(item.vip ) {
					item.vip_trans  = 'Yes';
				} else {
					item.vip_trans  = 'No';
				}
				return item
			})
			// console.logTime('fetchUnits end');
			dispatch(Actions.loading.apiSuccess('fetchUnits'));
			dispatch(Actions.units.fetched(resp, false));
		}).catch(error => {
			console.warn('error',error);
			alert(error);
			dispatch(Actions.loading.apiSuccess('fetchUnits'));
		})
	},
  	fetchUnitDropdownOptions: (projectDetail) => {
	  	dispatch(Actions.loading.api('fetchUnitDropdownOptions'));
		let internal_status = [];
		if(projectDetail.internal_status.length > 0) {
			internal_status = projectDetail.internal_status.map(status => {
				return status.name;
			});
		}
		return Server.fetchUnitDropdownOptions(projectDetail._id).then(resp => {
            let dropDownData = [
                {   title: i18.unitType,
                    list: resp.type || [],
                    filter: []
                },
                {
                    title: i18.tower,
                    list: resp.tower || [],
                    filter: []
                },
                {
                    title: i18.floor,
                    list: resp.floor || [],
                    filter: []
                },
                {
                    title: i18.flat,
                    list: resp.flat || [],
                    filter: []
                },
                {
                    title: i18.stage,
                    list: resp.stage || [],
                    filter: [],
                },
                {
                    title: i18.internalStatus,
                    list: internal_status || [],
                    filter: [],
                },
            ];
			dispatch(Actions.loading.apiSuccess('fetchUnitDropdownOptions'));
			return dropDownData;
		}).catch(err => {
		  alert(err);
		  dispatch(Actions.loading.apiSuccess('fetchUnitDropdownOptions'));
		})
	},
	// updateListSelection: (units) => {
	// 	dispatch(Actions.units.selected(units));
	// },
	downloadUnit: (unit, code)=>{
		let asyncTask1 = [];

		// Download following checklist
		// console.log('downloading unit:',unit);

		// create an object instance for cancel
		let cancelable = {};

		unit.map(c =>{
			// console.log("unit c", c)
			let task1 = () => downloadEachUnit(c, code);
			asyncTask1.push(task1);
		});

		// console.logTime('downloadUnit start');
		dispatch(Actions.progressBar.reset(asyncTask1.length,true,cancelable));

		let successCount = 0;
		Promise.sequence( asyncTask1, (subResult, step, total)=>{
			if(step > 0 && subResult && subResult._id){
				successCount = successCount + 1;
			}
			// console.log("download unit sginle result: ", subResult);
			// console.log("step: ", step);
			// console.log("total: ", total);
			// console.log("successCount: ", successCount);

			dispatch(Actions.progressBar.progress(step, subResult instanceof Error, cancelable, successCount));
		},{
			skipIfError: true,
			cancelable
		}).then(result=>{
			// console.logTime('downloadUnit end');
			// console.log('Promise.sequence result:',result);
			result = result.filter((item)=> {
				return item._id
			})

			// Put them to redux, therefore, UI will refresh
			dispatch(Actions.units.fetched(result, false));
			LocalDB.save('Unit',result).then(resp => {/* console.log("resp unit", resp) */});

			// LocalDB.fetch('Checklist').then(resp => {
			// 	// console.log("fetchLocalChecklists", resp);
			// 	// console.logTime('fetchLocalChecklists end');
			// 	dispatch(Actions.loading.storageSuccess('fetchLocalChecklists'));
			// 	dispatch(Actions.checklists.fetched(resp, false));
			// }).catch(error => {
			// 	console.warn('fetchLocalChecklists error',error);
			// 	dispatch(Actions.loading.storageSuccess('fetchLocalChecklists'));
			// });

			// LocalDB.fetch('Item').then(resp => {
			// 	// console.log("fetchLocalItems", resp);
			// 	// console.logTime('fetchLocalItems end');
			// 	dispatch(Actions.loading.storageSuccess('fetchLocalItems'));
			// 	dispatch(Actions.items.fetched(resp, false));
			// 	dispatch(Actions.project.updateItemsCount(resp.filter(item => item.status_history && item.isDefect).length));
			// }).catch(error => {
			// 	console.warn('fetchLocalItems error',error);
			// 	dispatch(Actions.loading.storageSuccess('fetchLocalItems'));
			// });

			return result;
		}).catch(err=>{
			// should not be here
			console.warn('err:',err);
			alert(err);
		});
	},
	fetchLocalUnits: () => {
		dispatch(Actions.loading.storage('fetchLocalUnits'));
		//console.logTime('fetchLocalUnits start');
		setTimeout(()=>{
			LocalDB.fetch('Unit').then(resp => {
				// console.logTime('fetchLocalUnits end');
				dispatch(Actions.loading.storageSuccess('fetchLocalUnits'));
				dispatch(Actions.units.fetched(resp, false));
			}).catch(error => {
				console.warn('error',error);
				dispatch(Actions.loading.storageSuccess('fetchLocalUnits'));
			});
		},0);
	},
	fetchLocalChecklistSummary: (unit) => {
	  dispatch(Actions.loading.storage('fetchLocalItemStatusSummary'));
	  //console.logTime('fetchLocalUnits start');
	  setTimeout(()=>{
		  LocalDB.fetch('ChecklistSummary').then(resp => {
			  // console.logTime('fetchLocalUnits end');
			  dispatch(Actions.loading.storageSuccess('fetchLocalItemStatusSummary'));
			  dispatch(Actions.units.fetchLocalChecklistSummary(resp.filter(item=> item.unit === unit)));
		  }).catch(error => {
			  console.warn('error',error);
			  dispatch(Actions.loading.storageSuccess('fetchLocalItemStatusSummary'));
		  });
	  },0);
	},
  	fetchLocalItemStatusSummary: (unit) => {
	  dispatch(Actions.loading.storage('fetchLocalItemStatusSummary'));
	  //console.logTime('fetchLocalUnits start');
	  setTimeout(()=>{
		  LocalDB.fetch('ItemSummary').then(resp => {
			  // console.logTime('fetchLocalUnits end');
			  dispatch(Actions.loading.storageSuccess('fetchLocalItemStatusSummary'));
			  dispatch(Actions.units.fetchLocalItemStatusSummary(resp.filter(item=> item.unit === unit)));
		  }).catch(error => {
			  console.warn('error',error);
			  dispatch(Actions.loading.storageSuccess('fetchLocalItemStatusSummary'));
		  });
	  },0);
	},
	modifyUnit: (id) => {
		dispatch(Actions.units.modifyUnit(id));
		dispatch(Actions.checklists.viewUnitCheckList(id));
	},
	updateSelectOptions: (data, option) => {
		dispatch(Actions.units.updateSelectOptions(data, option));
	},
	updateExpandedChecklist: (id) => {
		dispatch(Actions.checklists.updateExpandedChecklist(id));
	},
	updateCheckboxes: (id, key, value) => {
		dispatch(Actions.units.updateCheckboxes(id, key, value));
	},
	filterUnit: (data) => {
		dispatch(Actions.units.filterUnit(data));
	},
	searchUnits: (project, data) => {
	  dispatch(Actions.loading.api('fetchUnitsCustom'));
	  // console.logTime('searchRespItems start');
	  const typeFilter = (!data[0].filter || data[0].filter.length === 0)? null: data[0].filter;
	  const towerFilter = (!data[1].filter || data[1].filter.length === 0)? null: data[1].filter;
	  const floorFilter = (!data[2].filter || data[2].filter.length === 0)? null: data[2].filter;
	  const unitFilter = (!data[3].filter || data[3].filter.length === 0)? null: data[3].filter;
	  const stageFilter = (!data[4].filter || data[4].filter.length === 0)? null: data[4].filter;
	  const internalStatusFilter = (!data[5].filter || data[5].filter.length === 0)? null: data[5].filter;

	  Server.fetchUnitsCustom(project, typeFilter, towerFilter, floorFilter, unitFilter
		  , stageFilter, internalStatusFilter).then(resp => {
		  // console.logTime('searchRespItems end');
		  dispatch(Actions.loading.apiSuccess('fetchUnitsCustom'));
		  dispatch(Actions.units.fetched(resp, true));
	  }).catch(error => {
		  console.log('error: ',error);
		  alert(i18.getItemErrMsg);
		  dispatch(Actions.loading.apiSuccess('fetchUnitsCustom'));
	  })
	},
	fakeUpdateUnitData: (data) => {
		dispatch(Actions.units.fakeUpdateUnitData(data));
	},
	pendingUnits: (unit) => {
		dispatch(Actions.loading.storage('pendingUnits'));
		setTimeout(()=>{
			dispatch(Actions.units.pendingUnits(unit));
			dispatch(Actions.project.updatePendingCount("+1"));
			dispatch(Actions.loading.storageSuccess('pendingUnits'));
		},0);
	},
	fetchLocalPendingUnits: () => {
		dispatch(Actions.loading.storage('fetchLocalPendingUnits'));
		//console.logTime('fetchLocalPendingUnits start');
		setTimeout(()=>{
			LocalDB.fetch('PendingUnit').then(resp => {
				// console.logTime('fetchLocalPendingUnits end');
				// console.log('fetchLocalPendingUnits:',resp);
				dispatch(Actions.loading.storageSuccess('fetchLocalPendingUnits'));
				dispatch(Actions.units.fetchedPendingUnits(resp));
			}).catch(error => {
				console.warn('error',error);
				dispatch(Actions.loading.storageSuccess('fetchLocalPendingUnits'));
			});
		},0);
	},
	updateQRcode: (data, showKeyForm) => {
		dispatch(Actions.units.updateQRCode(data, showKeyForm));
	},
	updateUnitKey: (data) => {
		dispatch(Actions.units.updateUnitKey(data));
	},
	resetNewUnitset: (data)=> {
        dispatch(Actions.units.resetNewUnitset(data));
    }
  }
}

module.exports = mapDispatchToProps;
