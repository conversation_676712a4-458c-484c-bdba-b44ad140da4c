import ImagePicker from 'react-native-image-picker';

import Server from 'Server';
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import { delay } from 'Utilities';
import { Answers } from 'react-native-fabric';
import { Platform } from 'react-native';

const Actions = require('Redux').Action;

const mapDispatchToProps = (dispatch, ownProps) => {
  return {
		login: (username, password)=>{
            dispatch(Actions.loading.api('login'));
            // username = "test_cs";
            // password = "test";
            return Server.login(username,password).then(resp => {
                Answers.logLogin(`Username with ${Platform.OS}`, true);	
                dispatch(Actions.loading.apiSuccess('login'));
                dispatch(Actions.auth.loggedInUser(resp));
                return Server.fetchProjectLists().then(response =>{
                    dispatch(Actions.loading.apiSuccess('fetchProjectLists'));
                    dispatch(Actions.project.fetchProjectLists(response));       
                    

                    return true;
                })
                .catch(error => {
                    console.warn('error',error);				
                    dispatch(Actions.loading.apiSuccess('fetchProjectLists'));
                    return false;
                })
            }).catch(error => {
                Answers.logLogin(`Username with ${Platform.OS}`, false);
                console.warn('error',error);
                dispatch(Actions.loading.apiError('login'));
                if (error) {
                    var err = JSON.parse(error);
                    if (err) {
                        alert(err.message);
                    } else {
                        alert(error);
                    }
                }
                return false;
            })
        },
        adLogin: (session) => {
            dispatch(Actions.loading.api('login'));
            return Server.adValidate(session).then(resp => {		
                dispatch(Actions.loading.apiSuccess('login'));
                dispatch(Actions.auth.loggedInUser(resp)); 
                return Server.fetchProjectLists().then(response =>{
                    dispatch(Actions.loading.apiSuccess('fetchProjectLists'));
                    dispatch(Actions.project.fetchProjectLists(response));       
                    
                    return true;
                })
                .catch(error => {
                    console.warn('error',error);				
                    dispatch(Actions.loading.apiSuccess('fetchProjectLists'));
                    return false;
                })
            }).catch(error => {
                console.warn('error',error);
                dispatch(Actions.loading.apiError('login'));
                if (error) {
                    var err = JSON.parse(error);
                    if (err) {
                        alert(err.message);
                    } else {
                        alert(error);
                    }
                }
                return false;
            })
        },
        forgetPassword: (email) => { 
            dispatch(Actions.auth.showAlertForgotPassword(false)); 
            dispatch(Actions.loading.api('forgotPassword'));
            return Server.forgotPassword(email).then(resp => {
                dispatch(Actions.loading.apiSuccess('forgotPassword'));
                if(resp){
                    dispatch(Actions.auth.showAlertForgotPassword(true)); 
                }
            })
            .catch(error => {
                if (error) {
                    var err = JSON.parse(error);
                    if (err) {
                        alert(err.message);
                    } else {
                        alert(error);
                    }
                }		
                dispatch(Actions.loading.apiSuccess('forgotPassword'));
                return false;
            })
        },
        changeForgotPasswordAlertStatus: (val) => {
            dispatch(Actions.auth.showAlertForgotPassword(val)); 
        },
        refreshAdLogin: (session) => {
          dispatch(Actions.loading.api('login'));
          return Server.adValidate(session).then(resp => {
              dispatch(Actions.loading.apiSuccess('login'));
              dispatch(Actions.auth.loggedInUser(resp));
              return true;
          }).catch(error => {
              console.warn('error',error);
              dispatch(Actions.loading.apiError('login'));
              if (error) {
                  var err = JSON.parse(error);
                  if (err) {
                      alert(err.message);
                  } else {
                      alert(error);
                  }
              }
              return false;
          })
        },
        refreshLogin: (username, password)=>{
              dispatch(Actions.loading.api('refreshLogin'));
              return Server.login(username,password).then(resp => {
                  Answers.logLogin(`Username with ${Platform.OS}`, true);
                  dispatch(Actions.loading.apiSuccess('refreshLogin'));
                  dispatch(Actions.auth.loggedInUser(resp));
                  return true;
              }).catch(error => {
                  Answers.logLogin(`Username with ${Platform.OS}`, false);
                  console.warn('error',error);
                  dispatch(Actions.loading.apiError('refreshLogin'));
                  if (error) {
                      var err = JSON.parse(error);
                      if (err) {
                          alert(err.message);
                      } else {
                          alert(error);
                      }
                  }
                  return false;
              })
          },

    }
}

module.exports = mapDispatchToProps;