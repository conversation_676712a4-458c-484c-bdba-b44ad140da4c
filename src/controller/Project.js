import ImagePicker from 'react-native-image-picker';

import Server from 'Server';
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import { delay } from 'Utilities';
import i18 from 'i18';

const Actions = require('Redux').Action;

import CachedImage  from 'react-native-cached-image';
const {
    ImageCacheProvider
} = CachedImage;

import { _getHeaders, _getServerUrl } from 'Request';
import { SERVER_URL } from 'Config';

function downloadImage(picture) {
	if (!picture.startsWith("http")) {
		let serverUrl = _getServerUrl();
		picture = serverUrl + "blob_image/" + picture;
	}
	return ImageCacheProvider.deleteCachedImage(picture).then(() => {
		return ImageCacheProvider.cacheImage(picture, {}, _getHeaders).then((imagePath)=>{
			return imagePath;
		}).catch((err) => {
			console.log("downloadImage failed", err);
			return picture;
		})
	});
}

const mapDispatchToProps = (dispatch, ownProps) => {
	var Obj = {
		fetchProjectLists: () => {
            dispatch(Actions.loading.api('fetchProjectLists'));
            return Server.fetchProjectLists().then(response =>{
                return dispatch(Actions.project.fetchProjectLists(response));
            }).then(() => {
                return dispatch(Actions.loading.apiSuccess('fetchProjectLists'));
			}).catch((error) => {
				console.log("fetchProjectLists", error);
                return dispatch(Actions.loading.apiSuccess('fetchProjectLists'));
			});
        },
		updateHandover: (data) => {
			dispatch(Actions.loading.api('updateHandover'));
			let tasks = [];
			// step 1: submit data
			let TITLES = ['Mr','Miss','Mrs'];
			let submitData = () => {
				let handoverData = {
					"_id": data.unitId,
					"contact_1_first_name": data.contactFirstName,
					"contact_1_last_name": data.contactLastName,
					"contact_1_phone": (data.contactPhone) ? data.contactAreacode + data.contactPhone + "" : undefined,
					"contact_1_areacode": (data.contactPhone) ? data.contactAreacode + "" : undefined,
					"contact_2_first_name": data.contactFirstName2,
					"contact_2_last_name": data.contactLastName2,
					"contact_2_phone": (data.contactPhone2) ? data.contactAreacode2 + data.contactPhone2 + "" : undefined,
					"contact_2_areacode": (data.contactPhone2) ? data.contactAreacode2 + "" : undefined,
					"owner_1_first_name_zh": data.firstName_zh,
					"owner_1_last_name_zh": data.lastName_zh,
					"owner_1_first_name_en": data.firstName_en,
					"owner_1_last_name_en": data.lastName_en,
					"owner_1_title": TITLES.indexOf(data.ownerTitle),
					"owner_1_address": data.address,
					"owner_1_phone": (data.phone) ? data.areacode + data.phone + "" : undefined,
					"owner_1_areacode": (data.phone) ? data.areacode + "" : undefined,
					"owner_1_email": data.email,
					"owner_2_first_name_zh": data.firstName2_zh,
					"owner_2_last_name_zh": data.lastName2_zh,
					"owner_2_first_name_en": data.firstName2_en,
					"owner_2_last_name_en": data.lastName2_en,
					"owner_2_title": TITLES.indexOf(data.ownerTitle2),
					"owner_2_address": data.address2,
					"owner_2_phone": (data.phone2) ? data.areacode2 + data.phone2 + "" : undefined,
					"owner_2_areacode": (data.phone2) ? data.areacode2 + "" : undefined,
					"owner_2_email": data.email2,
					"handover_date": new Date(),
					"electron_1_id": data.meterNumber,
					"electron_1_value": (data.meterReading) ? Number(data.meterReading) : undefined,
					"electron_2_id": data.meterNumber2,
					"electron_2_value": (data.meterReading2) ? Number(data.meterReading2) : undefined,
					"water_1_id": data.waterMeterNumber,
					"water_1_value": (data.waterMeterReading) ? Number(data.waterMeterReading) : undefined,
					"water_2_id": data.waterMeterNumber2,
					"water_2_value": (data.waterMeterReading2) ? Number(data.waterMeterReading2) : undefined,
					"gas_1_id": data.gasMeterNumber,
					"gas_1_value": (data.gasMeterReading) ? Number(data.gasMeterReading) : undefined,
					"staff": data.staff,
					"primary_contact": data.primaryContact
				};
				return Server.updateHandoverData(handoverData);
			}
			tasks.push(submitData);

			// step 2: upload pdf
			data.forms.forEach((form) => {
				if (form.dataPath && form.dataPath.length > 0) {
					let task = () => {
						let pathComponent = form.dataPath.split("/");
						if (form.dataPath.indexOf('file://') == -1) {
							form.dataPath = "file://" + form.dataPath;
						}
						return Server.uploadPdf({
                                fileName: pathComponent[pathComponent.length - 1],
                                origURL: form.dataPath,
                            }).then(result => {
							console.log("Server.uploadPdf", result)
							// then update the related path
							return Server.updateHandoverForm({
								_id: form.unitId,
								title: form.title,
								path: result
							}).then((result2) => {
								console.log("Server.updateHandoverForm", result2)
								return result2;
							});
						})
					};
					tasks.push(task);
				}
			});

			return Promise.sequence(tasks).then((resp) => {
				console.log("updateHandover", resp)
				dispatch(Actions.loading.apiSuccess('updateHandover'));
				return resp;
			}).catch((err) => {
				alert(err);
				dispatch(Actions.loading.apiSuccess('updateHandover'));
			});
		},
		getFormSubmissionFilter: (tower, floor, flat) => {
			dispatch(Actions.project.getAdHocFilter({tower, floor, flat}));
		},
		fetchHandoverForm: (id) => {
			dispatch(Actions.loading.api('fetchHandoverForm'));
			console.logTime('fetchHandoverForm start');
            return Server.fetchHandoverForm(id).then(resp => {
                console.logTime('fetchHandoverForm end');
                dispatch(Actions.loading.apiSuccess('fetchHandoverForm'));
				dispatch(Actions.project.fetchHandoverForm(resp));
				return true;
            }).catch(error => {
                console.warn('fetchHandoverForm error',error);
                dispatch(Actions.loading.apiSuccess('fetchHandoverForm'));
				return false;
            })
		},
		updateKeyQRcode: (data, showKeyForm) => {
			dispatch(Actions.project.updateKeyQRCode(data, showKeyForm));
		},
		updateKey: (data) => {
			dispatch(Actions.project.updateKey(data));
			dispatch(Actions.project.updatePendingCount("+1"));
		},
		fetchLocalPendingKey: () => {
			dispatch(Actions.loading.storage('fetchLocalPendingKey'));
			//console.logTime('fetchLocalPendingKey start');
			setTimeout(()=>{
				LocalDB.fetch('QrcodeData').then(resp => {
					// console.logTime('fetchLocalPendingKey end');
					dispatch(Actions.loading.storageSuccess('fetchLocalPendingKey'));
					dispatch(Actions.project.fetched(resp));
				}).catch(error => {
					console.warn('error',error);
					dispatch(Actions.loading.storageSuccess('fetchLocalPendingKey'));
				});
			},0);
		},
		updateSelectedProject: (item)=>{
			dispatch(Actions.project.updateSelectedProject(item));
		},
    fetchChecklistList: (id) => {
      dispatch(Actions.loading.api('fetchChecklistList'))
      return Server.fetchChecklistList(id).then(response => {
        dispatch(Actions.loading.apiSuccess('fetchChecklistList'))
        dispatch(Actions.project.fetchChecklistList(response))
        return true
      }).catch(() => {
        dispatch(Actions.loading.apiSuccess('fetchChecklistList'))
      })
    },
    fetchProjectDetail: (projectCode) => {
      dispatch(Actions.loading.api('fetchProjectDetail'))
      Server.fetchProjectDetail(projectCode).then(projectDetail => {
        projectDetail.stage = ['Construction', 'Inspection', 'DLP', 'DLP_expired']
        dispatch(Actions.loading.apiSuccess('fetchProjectDetail'))
        dispatch(Actions.loading.api('fetchUsers'))
        return Server.fetchUsers()
          .then((users) => {
            dispatch(Actions.project.fetchUsers(users))
            dispatch(Actions.loading.apiSuccess('fetchUsers'))
            dispatch(Actions.loading.api('fetchUnitInfo'))
            return Server.fetchUnitInfo(projectDetail._id).then((unitsInfo) => {
              let image_list = new Set()
              unitsInfo.forEach((unit) => {
                if (unit.floor_plans) {
                  unit.floor_plans.forEach(i => image_list.add(i))
                }
              })
              image_list = [...image_list].map((value) => {return async () => {return await downloadImage(value)}})
              const cancelable = true
              dispatch(Actions.loading.storage('fetchProjectDetail'))
			  dispatch(Actions.progressBar.reset(image_list.length, true, cancelable))

			  let successCount = 0;
              Promise.sequence(image_list, (subResult, step, total) => {
				// console.log(`step ${step}`, subResult, total, successCount);
				if(step > 0 && subResult){
					successCount = successCount + 1;
				}
                dispatch(Actions.progressBar.progress(step, subResult instanceof Error, cancelable, successCount))
              }, {
                skipIfError: true, cancelable
              }).then().catch((err) => { console.error(err)})
              dispatch(Actions.loading.apiSuccess('fetchUnitInfo'))
              setTimeout(() => {
                dispatch(Actions.project.fetchUnitsInfo(unitsInfo))
                dispatch(Actions.project.fetchProjectDetail(projectDetail))
                dispatch(Actions.navigation.replace('Main'))
                dispatch(Actions.loading.storageSuccess('fetchProjectDetail'))
              }, 0)
            })
          })
      })
        .catch(error => {
          console.warn('error', error)
          dispatch(Actions.loading.apiSuccess('fetchProjectDetail'))
        })
    },
		updateChecklistsCount: (count) => {
			dispatch(Actions.project.updateChecklistsCount(count));
		},
		updateUnitsCount: (count) => {
			dispatch(Actions.project.updateUnitsCount(count));
		},
		updateItemsCount: (count) => {
			dispatch(Actions.project.updateItemsCount(count));
		},
		updatePendingCount: (count) => {
			dispatch(Actions.project.updatePendingCount(count));
		},
        // getCheckListDownloadDropDownData: (projectDetail, language) => {
         //    let dropDownData = [
		// 		{
		// 			title: i18.Checklist,
		// 			list: projectDetail.checklistList ? projectDetail.checklistList.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.tower,
		// 			list: projectDetail.tower ? projectDetail.tower.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.floor,
		// 			list: projectDetail.floor ? projectDetail.floor.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.flat,
		// 			list: projectDetail.flat ? projectDetail.flat.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		// {   title: language == 'en' ? 'Status' : '填寫狀態',
		// 		// 	list: ['Yes', 'No'],
		// 		// 	filter: ''
		// 		// },
         //    ];
        //
         //    return dropDownData;
		// },        // getCheckListDownloadDropDownData: (projectDetail, language) => {
         //    let dropDownData = [
		// 		{
		// 			title: i18.Checklist,
		// 			list: projectDetail.checklistList ? projectDetail.checklistList.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.tower,
		// 			list: projectDetail.tower ? projectDetail.tower.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.floor,
		// 			list: projectDetail.floor ? projectDetail.floor.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.flat,
		// 			list: projectDetail.flat ? projectDetail.flat.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		// {   title: language == 'en' ? 'Status' : '填寫狀態',
		// 		// 	list: ['Yes', 'No'],
		// 		// 	filter: ''
		// 		// },
         //    ];
        //
         //    return dropDownData;
		// },
		// getItemDownloadDropDownData: (projectDetail, language, items) => {
        //
		// 	let data = items;
		// 	let level1 = [] , level2 = [], location = [], status = [];
		// 	let defect_item_status = projectDetail.defect_item_status;
        //
		// 	defect_item_status.forEach((item)=>{
		// 		status.push(item.name);
		// 	})
        //
		// 	data.forEach((item)=>{
		// 		if(item.location){
		// 			// location.push(item.location);
		// 			location = Obj.itemPushfunction(location, item.location);
		// 		}
		// 		if(item.level1){
		// 			// level1.push(item.level1);
		// 			level1 = Obj.itemPushfunction(level1, item.level1);
		// 		}
		// 		if(item.level2){
		// 			// level2.push(item.level2);
		// 			level2 = Obj.itemPushfunction(level2, item.level2);
		// 		}
		// 	})
        //
		// 	// level1 = level1.filter(function(item, pos) {
		// 	// 	return level1.indexOf(item) == pos;
		// 	// })
        //
		// 	// level2 = level2.filter(function(item, pos) {
		// 	// 	return level2.indexOf(item) == pos;
		// 	// })
        //
		// 	// location = location.filter(function(item, pos) {
		// 	// 	return location.indexOf(item) == pos;
		// 	// })
        //
        //
         //    let dropDownData = [
		// 		{   title: i18.fillStatus,
		// 			list: status ? status : [],
		// 			filter: []
		// 		},
		// 		{
		// 			title: i18.tower,
		// 			list: projectDetail.tower ? projectDetail.tower.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.floor,
		// 			list: projectDetail.floor ? projectDetail.floor.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.flat,
		// 			list: projectDetail.flat ? projectDetail.flat.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.location,
		// 			list: location ? location.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.level1,
		// 			list: level1 ? level1.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
		// 		{
		// 			title: i18.level2,
		// 			list: level2 ? level2.sort((a, b) => Obj.naturalCompare(a, b)) : [],
		// 			filter: [],
		// 		},
        //
         //    ];
        //
         //    return dropDownData;
		// },
		getUnitDropDownData: (dropDownList, units, language) => {

			let data = units.slice();

			let status_int = [] , vip = [], stage = [], type = [], tower = [] , floor = [], flat = [];
			let filter1 = [], filter2 = [], filter3 = [], filter4 = [], filter5 = [], filter6 = [], filter7 = [];

			if(dropDownList.length > 0) {
				filter1 = dropDownList[0].filter;
				filter2 = dropDownList[1].filter;
				filter3 = dropDownList[2].filter;
				filter4 = dropDownList[3].filter;
				filter5 = dropDownList[4].filter;
				filter6 = dropDownList[5].filter;
				filter7 = dropDownList[6].filter;
			}

			if(dropDownList.length > 0 && (filter1.length > 0 || filter2.length > 0 || filter3.length > 0
				|| filter4.length > 0 || filter5.length > 0 || filter6.length > 0 || filter7.length > 0)) {

				if(filter1.length > 0) {
					data.filter(el => filter1.includes(el.type)).forEach((item)=>{
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						stage = Obj.itemPushfunction(stage, item.stage);
						status_int = Obj.itemPushfunction(status_int, item.status_int);
						vip = Obj.itemPushfunction(vip, item.vip_trans);
					})
					data.forEach((item)=>{
						type = Obj.itemPushfunction(type, item.type);
					})
				}

				if(filter2.length > 0) {
					data.filter(el => filter2.includes(el.tower)).forEach((item)=>{
						type = Obj.itemPushfunction(type, item.type);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						stage = Obj.itemPushfunction(stage, item.stage);
						status_int = Obj.itemPushfunction(status_int, item.status_int);
						vip = Obj.itemPushfunction(vip, item.vip_trans);
					})

					if(filter1.length > 0) {
						data.filter(el => filter1.includes(el.type)).forEach((item)=>{
							tower = Obj.itemPushfunction(tower, item.tower);
						})

						floor = [], flat = [], stage = [], status_int = [], vip = [];
						data.filter(el => filter1.length > 0 ? filter1.includes(el.type) : el.type != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
							flat = Obj.itemPushfunction(flat, item.flat);
							stage = Obj.itemPushfunction(stage, item.stage);
							status_int = Obj.itemPushfunction(status_int, item.status_int);
							vip = Obj.itemPushfunction(vip, item.vip_trans);
						})

					}else {
						data.forEach((item)=>{
							tower = Obj.itemPushfunction(tower, item.tower);
						})
					}

				}


				if(filter3.length > 0) {
					data.filter(el => filter3.includes(el.floor)).forEach((item)=>{
						type = Obj.itemPushfunction(type, item.type);
						tower = Obj.itemPushfunction(tower, item.tower);
						flat = Obj.itemPushfunction(flat, item.flat);
						stage = Obj.itemPushfunction(stage, item.stage);
						status_int = Obj.itemPushfunction(status_int, item.status_int);
						vip = Obj.itemPushfunction(vip, item.vip_trans);
					})

					if(filter2.length > 0) {
						data.filter(el => filter2.includes(el.tower)).forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
						})

						flat = [], stage = [], status_int = [], vip = [];
						data.filter(el => filter1.length > 0 ? filter1.includes(el.type) : el.type != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.filter(el => filter3.length > 0 ?  filter3.includes(el.floor) : el.floor != undefined)
						.forEach((item)=>{
							flat = Obj.itemPushfunction(flat, item.flat);
							stage = Obj.itemPushfunction(stage, item.stage);
							status_int = Obj.itemPushfunction(status_int, item.status_int);
							vip = Obj.itemPushfunction(vip, item.vip_trans);
						})
					}else {
						data.forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
						})
					}
				}


				if(filter4.length > 0) {
					data.filter(el => filter4.includes(el.flat)).forEach((item)=>{
						type = Obj.itemPushfunction(type, item.type);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						stage = Obj.itemPushfunction(stage, item.stage);
						status_int = Obj.itemPushfunction(status_int, item.status_int);
						vip = Obj.itemPushfunction(vip, item.vip_trans);
					})

					if(filter3.length > 0) {
						data.filter(el => filter3.includes(el.floor)).forEach((item)=>{
							flat = Obj.itemPushfunction(flat, item.flat);
						})

						stage = [], status_int = [], vip = [];

						data.filter(el => filter1.length > 0 ? filter1.includes(el.type) : el.type != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.filter(el => filter3.length > 0 ?  filter3.includes(el.floor) : el.floor != undefined)
						.filter(el => filter4.length > 0 ?  filter4.includes(el.flat) : el.flat != undefined)
						.forEach((item)=>{
							stage = Obj.itemPushfunction(stage, item.stage);
							status_int = Obj.itemPushfunction(status_int, item.status_int);
							vip = Obj.itemPushfunction(vip, item.vip_trans);
						})

					}else {
						data.forEach((item)=>{
							flat = Obj.itemPushfunction(flat, item.flat);
						})
					}

				}


				if(filter5.length > 0) {
					data.filter(el => filter5.includes(el.stage)).forEach((item)=>{
						type = Obj.itemPushfunction(type, item.type);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						status_int = Obj.itemPushfunction(status_int, item.status_int);
						vip = Obj.itemPushfunction(vip, item.vip_trans);
					})

					if(filter4.length > 0) {
						data.filter(el => filter4.includes(el.flat)).forEach((item)=>{
							stage = Obj.itemPushfunction(stage, item.stage);
						})

						status_int = [], vip = [];

						data.filter(el => filter1.length > 0 ? filter1.includes(el.type) : el.type != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.filter(el => filter3.length > 0 ?  filter3.includes(el.floor) : el.floor != undefined)
						.filter(el => filter4.length > 0 ?  filter4.includes(el.flat) : el.flat != undefined)
						.filter(el => filter5.length > 0 ?  filter5.includes(el.stage) : el.stage != undefined)
						.forEach((item)=>{
							status_int = Obj.itemPushfunction(status_int, item.status_int);
							vip = Obj.itemPushfunction(vip, item.vip_trans);
						})

					}else {
						data.forEach((item)=>{
							stage = Obj.itemPushfunction(stage, item.stage);
						})
					}

				}

				if(filter6.length > 0) {
					data.filter(el => filter6.includes(el.status_int)).forEach((item)=>{
						type = Obj.itemPushfunction(type, item.type);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						stage = Obj.itemPushfunction(stage, item.stage);
						vip = Obj.itemPushfunction(vip, item.vip_trans);
					})

					if(filter5.length > 0) {
						data.filter(el => filter5.includes(el.stage)).forEach((item)=>{
							status_int = Obj.itemPushfunction(status_int, item.status_int);
						})

						vip = [];

						data.filter(el => filter1.length > 0 ? filter1.includes(el.type) : el.type != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.filter(el => filter3.length > 0 ?  filter3.includes(el.floor) : el.floor != undefined)
						.filter(el => filter4.length > 0 ?  filter4.includes(el.flat) : el.flat != undefined)
						.filter(el => filter5.length > 0 ?  filter5.includes(el.stage) : el.stage != undefined)
						.filter(el => filter6.length > 0 ? filter6.includes(el.status_int) : el.status_int != undefined)
						.forEach((item)=>{
							vip = Obj.itemPushfunction(vip, item.vip);
						})

					}else {
						data.forEach((item)=>{
							status_int = Obj.itemPushfunction(status_int, item.status_int);
						})
					}

				}


				if(filter7.length > 0) {
					data.filter(el => filter7.includes(el.vip_trans)).forEach((item)=>{
						type = Obj.itemPushfunction(type, item.type);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						stage = Obj.itemPushfunction(stage, item.stage);
						status_int = Obj.itemPushfunction(status_int, item.status_int);
					})

					if(filter6.length > 0) {
						data.filter(el => filter6.includes(el.status_int))
						.forEach((item)=>{
							vip = Obj.itemPushfunction(vip, item.vip_trans);
						})

					}else {
						data.forEach((item)=>{
							vip = Obj.itemPushfunction(vip, item.vip_trans);
						})
					}
				}

			} else {
				data.forEach((item)=>{
					type = Obj.itemPushfunction(type, item.type);
					tower = Obj.itemPushfunction(tower, item.tower);
					floor = Obj.itemPushfunction(floor, item.floor);
					flat = Obj.itemPushfunction(flat, item.flat);
					stage = Obj.itemPushfunction(stage, item.stage);
					status_int = Obj.itemPushfunction(status_int, item.status_int);
					vip = Obj.itemPushfunction(vip, item.vip_trans);
				})
			}

			// type = type.filter(function(item, pos) {
			// 	return type.indexOf(item) == pos;
			// })

			// tower = tower.filter(function(item, pos) {
			// 	return tower.indexOf(item) == pos;
			// })

			// floor = floor.filter(function(item, pos) {
			// 	return floor.indexOf(item) == pos;
			// })

			// flat = flat.filter(function(item, pos) {
			// 	return flat.indexOf(item) == pos;
			// })

			// status_int = status_int.filter(function(item, pos) {
			// 	return status_int.indexOf(item) == pos;
			// })

			// vip = vip.filter(function(item, pos) {
			// 	return vip.indexOf(item) == pos;
			// })

			// stage = stage.filter(function(item, pos) {
			// 	return stage.indexOf(item) == pos;
			// })





			let dropDownData = [
				{   title: i18.unitType,
					list: type ? type.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[0].filter : [],
				},
				{
					title: i18.tower,
					list: tower ? tower.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[1].filter : [],
				},
				{
					title: i18.floor,
					list: floor ? floor.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[2].filter : [],
				},
				{
					title: i18.flat,
					list: flat ? flat.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[3].filter : [],
				},
				{
					title: i18.stage,
					list: stage ? stage.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[4].filter : [],
				},
				{
					title: i18.internalStatus,
					list: status_int  ? status_int.sort((a, b) => Obj.naturalCompare(a, b))  : [],
					filter: dropDownList.length > 0 ? dropDownList[5].filter : [],
				},
				{
					title: language == 'en' ? 'VIP' : 'VIP',
					list: vip ? vip.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[6].filter : [],
				},
			];
			return dropDownData;
		},
		getUnitDownloadDropDownData: (projectDetail, language) => {
			console.log("projectDetail", projectDetail)
			if(projectDetail.type){
				projectDetail.vip = ['Yes', 'No'];
			}
			let internal_status = [];
			if(projectDetail.internal_status.length > 0) {
				// projectDetail.internal_status.map((item)=>{
				// 	internal_status.push(item.name);
				// })
				internal_status = projectDetail.internal_status.map((item) => {
					return item.name;
				});
			}

			let dropDownData = [
				{   title: i18.unitType,
					list: projectDetail.type ? projectDetail.type.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: []
				},
				{
					title: i18.tower,
					list: projectDetail.tower ? projectDetail.tower.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: [],
				},
				{
					title: i18.floor,
					list: projectDetail.floor ? projectDetail.floor.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: [],
				},
				{
					title: i18.flat,
					list: projectDetail.flat ? projectDetail.flat.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: [],
				},
				{
					title: i18.stage,
					list: projectDetail.stage ? projectDetail.stage.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: [],
				},
				{
					title: i18.internalStatus,
					list: internal_status  ? internal_status.sort((a, b) => Obj.naturalCompare(a, b))  : [],
					filter: [],
				},
				{
					title: language == 'en' ? 'VIP' : 'VIP',
					list: projectDetail.vip ? projectDetail.vip.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: [],
				},

			];

			return dropDownData;
		},
		getItemDropDownData: (dropDownList, items, language) => {

			let data = items;
			let level1 = [] , level2 = [], location = [], status = [], tower = [] , floor = [], flat = [];
			let filter1 = [], filter2 = [], filter3 = [], filter4 = [], filter5 = [], filter6 = [], filter7 = [];

			if(dropDownList.length > 0) {
				filter1 = dropDownList[0].filter;
				filter2 = dropDownList[1].filter;
				filter3 = dropDownList[2].filter;
				filter4 = dropDownList[3].filter;
				filter5 = dropDownList[4].filter;
				filter6 = dropDownList[5].filter;
				filter7 = dropDownList[6].filter;
			}

			if(dropDownList.length > 0 && (filter1.length > 0 || filter2.length > 0 || filter3.length > 0
				|| filter4.length > 0 || filter5.length > 0 || filter6.length > 0 || filter7.length > 0)) {

				if(filter1.length > 0) {
					data.filter(el => filter1.includes(el.status)).forEach((item)=>{
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						location = Obj.itemPushfunction(location, item.location);
						level1 = Obj.itemPushfunction(level1, item.level1);
						level2 = Obj.itemPushfunction(level2, item.level2);
					})
					data.forEach((item)=>{
						status = Obj.itemPushfunction(status, item.status);
					})
				}

				if(filter2.length > 0) {
					data.filter(el => filter2.includes(el.tower)).forEach((item)=>{
						status = Obj.itemPushfunction(status, item.status);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						location = Obj.itemPushfunction(location, item.location);
						level1 = Obj.itemPushfunction(level1, item.level1);
						level2 = Obj.itemPushfunction(level2, item.level2);
					})

					if(filter1.length > 0) {
						data.filter(el => filter1.includes(el.status)).forEach((item)=>{
							tower = Obj.itemPushfunction(tower, item.tower);
						})

						floor = [], flat = [], location = [], level1 = [], level2 = [];
						data.filter(el => filter1.length > 0 ?  filter1.includes(el.status) : el.status != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
							flat = Obj.itemPushfunction(flat, item.flat);
							location = Obj.itemPushfunction(location, item.location);
							level1 = Obj.itemPushfunction(level1, item.level1);
							level2 = Obj.itemPushfunction(level2, item.level2);
						})

					}else {
						data.forEach((item)=>{
							tower = Obj.itemPushfunction(tower, item.tower);
						})
					}

				}


				if(filter3.length > 0) {
					data.filter(el => filter3.includes(el.floor)).forEach((item)=>{
						status = Obj.itemPushfunction(status, item.status);
						tower = Obj.itemPushfunction(tower, item.tower);
						flat = Obj.itemPushfunction(flat, item.flat);
						location = Obj.itemPushfunction(location, item.location);
						level1 = Obj.itemPushfunction(level1, item.level1);
						level2 = Obj.itemPushfunction(level2, item.level2);
					})

					if(filter2.length > 0) {
						data.filter(el => filter2.includes(el.tower)).forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
						})

						flat = [], location = [], level1 = [], level2 = [];

						data.filter(el => filter1.length > 0 ?  filter1.includes(el.status) : el.status != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.filter(el => filter3.length > 0 ?  filter3.includes(el.floor) : el.floor != undefined)
						.forEach((item)=>{
							flat = Obj.itemPushfunction(flat, item.flat);
							location = Obj.itemPushfunction(location, item.location);
							level1 = Obj.itemPushfunction(level1, item.level1);
							level2 = Obj.itemPushfunction(level2, item.level2);
						})

					}else {
						data.forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
						})
					}

				}


				if(filter4.length > 0) {
					data.filter(el => filter4.includes(el.flat)).forEach((item)=>{
						status = Obj.itemPushfunction(status, item.status);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						location = Obj.itemPushfunction(location, item.location);
						level1 = Obj.itemPushfunction(level1, item.level1);
						level2 = Obj.itemPushfunction(level2, item.level2);
					})

					if(filter3.length > 0) {
						data.filter(el => filter3.includes(el.floor)).forEach((item)=>{
							flat = Obj.itemPushfunction(flat, item.flat);
						})

						location = [], level1 = [], level2 = [];

						data.filter(el => filter1.length > 0 ?  filter1.includes(el.status) : el.status != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.filter(el => filter3.length > 0 ?  filter3.includes(el.floor) : el.floor != undefined)
						.filter(el => filter4.length > 0 ?  filter4.includes(el.flat) : el.flat != undefined)
						.forEach((item)=>{
							location = Obj.itemPushfunction(location, item.location);
							level1 = Obj.itemPushfunction(level1, item.level1);
							level2 = Obj.itemPushfunction(level2, item.level2);
						})

					}else {
						data.forEach((item)=>{
							flat = Obj.itemPushfunction(flat, item.flat);
						})
					}

				}


				if(filter5.length > 0) {
					data.filter(el => filter5.includes(el.location)).forEach((item)=>{
						status = Obj.itemPushfunction(status, item.status);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						level1 = Obj.itemPushfunction(level1, item.level1);
						level2 = Obj.itemPushfunction(level2, item.level2);
					})

					if(filter4.length > 0) {
						data.filter(el => filter4.includes(el.flat)).forEach((item)=>{
							location = Obj.itemPushfunction(location, item.location);
						})

						level1 = [], level2 = [];

						data.filter(el => filter1.length > 0 ?  filter1.includes(el.status) : el.status != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.filter(el => filter3.length > 0 ?  filter3.includes(el.floor) : el.floor != undefined)
						.filter(el => filter4.length > 0 ?  filter4.includes(el.flat) : el.flat != undefined)
						.filter(el => filter5.length > 0 ?  filter5.includes(el.location) : el.location != undefined)
						.forEach((item)=>{
							level1 = Obj.itemPushfunction(level1, item.level1);
							level2 = Obj.itemPushfunction(level2, item.level2);
						})

					}else {
						data.forEach((item)=>{
							location = Obj.itemPushfunction(location, item.location);
						})
					}

				}

				if(filter6.length > 0) {
					data.filter(el => filter6.includes(el.level1)).forEach((item)=>{
						status = Obj.itemPushfunction(status, item.status);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						location = Obj.itemPushfunction(location, item.location);
						level2 = Obj.itemPushfunction(level2, item.level2);
					})

					if(filter5.length > 0) {
						data.filter(el => filter5.includes(el.location)).forEach((item)=>{
							level1 = Obj.itemPushfunction(level1, item.level1);
						})

						level2 = [];

						data.filter(el => filter1.length > 0 ?  filter1.includes(el.status) : el.status != undefined)
						.filter(el => filter2.length > 0 ?  filter2.includes(el.tower) : el.tower != undefined)
						.filter(el => filter3.length > 0 ?  filter3.includes(el.floor) : el.floor != undefined)
						.filter(el => filter4.length > 0 ?  filter4.includes(el.flat) : el.flat != undefined)
						.filter(el => filter5.length > 0 ?  filter5.includes(el.location) : el.location != undefined)
						.filter(el => filter6.length > 0 ? filter6.includes(el.level1) : el.level1 != undefined)
						.forEach((item)=>{
							level2 = Obj.itemPushfunction(level2, item.level2);
						})

					}else {
						data.forEach((item)=>{
							level1 = Obj.itemPushfunction(level1, item.level1);
						})
					}

				}


				if(filter7.length > 0) {
					data.filter(el => filter7.includes(el.level2)).forEach((item)=>{
						status = Obj.itemPushfunction(status, item.status);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						location = Obj.itemPushfunction(location, item.location);
						level1 = Obj.itemPushfunction(level1, item.level1);
					})

					if(filter6.length > 0) {
						data.filter(el => filter6.includes(el.level1))
						.forEach((item)=>{
							level2 = Obj.itemPushfunction(level2, item.level2);
						})

					}else {
						data.forEach((item)=>{
							level2 = Obj.itemPushfunction(level2, item.level2);
						})
					}
				}

			} else {
				data.forEach((item)=>{
					status = Obj.itemPushfunction(status, item.status);
					tower = Obj.itemPushfunction(tower, item.tower);
					floor = Obj.itemPushfunction(floor, item.floor);
					flat = Obj.itemPushfunction(flat, item.flat);
					location = Obj.itemPushfunction(location, item.location);
					level1 = Obj.itemPushfunction(level1, item.level1);
					level2 = Obj.itemPushfunction(level2, item.level2);
				})
			}

			// status = status.filter(function(item, pos) {
			// 	return status.indexOf(item) == pos;
			// })

			// tower = tower.filter(function(item, pos) {
			// 	return tower.indexOf(item) == pos;
			// })

			// floor = floor.filter(function(item, pos) {
			// 	return floor.indexOf(item) == pos;
			// })

			// flat = flat.filter(function(item, pos) {
			// 	return flat.indexOf(item) == pos;
			// })

			// level1 = level1.filter(function(item, pos) {
			// 	return level1.indexOf(item) == pos;
			// })

			// level2 = level2.filter(function(item, pos) {
			// 	return level2.indexOf(item) == pos;
			// })

			// location = location.filter(function(item, pos) {
			// 	return location.indexOf(item) == pos;
			// })


			let dropDownData = [
				{   title: i18.fillStatus,
					list: status ? status.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? filter1 : [],
				},
				{
					title: i18.tower,
					list: tower ? tower.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? filter2 : [],
				},
				{
					title: i18.floor,
					list: floor ? floor.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? filter3 : [],
				},
				{
					title: i18.flat,
					list: flat ? flat.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? filter4 : [],
				},
				{
					title: i18.location,
					list: location ? location.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? filter5 : [],
				},
				{
					title: i18.level1,
					list: level1 ? level1.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? filter6 : [],
				},
				{
					title: i18.level2,
					list: level2 ? level2.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter:dropDownList.length > 0 ? filter7 : [],
				},

			];
			return dropDownData;
		},
		getCheckListDropDownData: (dropDownList, sectionData, language) => {
			// console.log("data", sectionData);
			// console.log("dropDownList",dropDownList);

			let newArray = [], location = [], level1 = [] , level2 = [];

			//TODO:  modify the format to fix the level 3 data

			sectionData.forEach((item)=>{
				let eachData = item.data;
				eachData.forEach((eachItem)=>{
					newArray.push(eachItem);
				})
			})


			let data = newArray;
			// console.log("data", data)

			if(dropDownList.length > 0 && (dropDownList[0].filter.length > 0 || dropDownList[1].filter.length > 0 || dropDownList[2].filter.length > 0)) {
				if(dropDownList[0].filter.length > 0) {
					// data.filter(el => dropDownList[0].filter.includes(el.level1)).forEach((item)=>{
					// 	level2 = Obj.itemPushfunction(level2, item.level2);
					// 	location = Obj.itemPushfunction(location, item.location);
					// })
					data.forEach((item)=>{
						if (dropDownList[0].filter.includes(item.location)) {
							level1 = Obj.itemPushfunction(level1, item.level1);
							level2 = Obj.itemPushfunction(level2, item.level2);
						}
						location = Obj.itemPushfunction(location, item.location);
					})
				}

				if(dropDownList[1].filter.length > 0) {
					// data.filter(el => dropDownList[1].filter.includes(el.level2)).forEach((item)=>{
					// 	level1 = Obj.itemPushfunction(level1, item.level1);
					// 	location = Obj.itemPushfunction(location, item.location);
					// })

					if(dropDownList[0].filter.length > 0) {
						data.filter(el => dropDownList[1].filter.includes(el.level1)).forEach((item)=>{
							location = Obj.itemPushfunction(location, item.location);
							level2 = Obj.itemPushfunction(level2, item.level2);
						})
						data.filter(el => dropDownList[0].filter.includes(el.location)).forEach((item)=>{
							level1 = Obj.itemPushfunction(level1, item.level1);
						})
						level2 = [];
						data.filter(el => dropDownList[0].filter.includes(el.location)).filter(el => dropDownList[1].filter.includes(el.level1)).forEach((item)=>{
							level2 = Obj.itemPushfunction(level2, item.level2);
							// console.log(1111110, location)
						})
					}else {
						data.forEach((item)=>{
							if (dropDownList[1].filter.includes(item.level1)) {
								location = Obj.itemPushfunction(location, item.location);
								level2 = Obj.itemPushfunction(level2, item.level2);
							}
							level1 = Obj.itemPushfunction(level1, item.level1);
						})
					}

				}

				if(dropDownList[2].filter.length > 0) {
					data.filter(el => dropDownList[2].filter.includes(el.level2)).forEach((item)=>{
						location = Obj.itemPushfunction(location, item.location);
						level1 = Obj.itemPushfunction(level1, item.level1);
					})

					if(dropDownList[1].filter.length > 0) {
						data.filter(el => dropDownList[1].filter.includes(el.level1)).forEach((item)=>{
							level2 = Obj.itemPushfunction(level2, item.level2);
						})
					}else {
						data.forEach((item)=>{
							level2 = Obj.itemPushfunction(level2, item.level2);
						})
					}

				}

			} else {
				data.forEach((item)=>{
					location = Obj.itemPushfunction(location, item.location);
					level1 = Obj.itemPushfunction(level1, item.level1);
					level2 = Obj.itemPushfunction(level2, item.level2);
				})
			}

			// level1 = level1.filter(function(item, pos) {
			// 	return level1.indexOf(item) == pos;
			// })

			// level2 = level2.filter(function(item, pos) {
			// 	return level2.indexOf(item) == pos;
			// })

			// location = location.filter(function(item, pos) {
			// 	return location.indexOf(item) == pos;
			// })

			let dropDownData = [
				{
					title: i18.location,
					list: location.sort((a, b) => Obj.naturalCompare(a, b)),
					filter: dropDownList.length > 0 ? dropDownList[0].filter : [],
				},
				{
					title: i18.level1,
					list: level1.sort((a, b) => Obj.naturalCompare(a, b)),
					filter: dropDownList.length > 0 ? dropDownList[1].filter : [],
				},
				{
					title: i18.level2,
					list: level2.sort((a, b) => Obj.naturalCompare(a, b)),
					filter: dropDownList.length > 0 ? dropDownList[2].filter : [],
				},
			];

            return dropDownData;
		},
		getTaskListDropDownData: (dropDownList, data, language) => {

			// console.log("data", data);
			// console.log("dropDownList",dropDownList);
			let name = [], tower = [] , floor = [], flat = [];



			if(dropDownList.length > 0 && (dropDownList[0].filter.length > 0 || dropDownList[1].filter.length > 0 || dropDownList[2].filter.length > 0 || dropDownList[3].filter.length > 0)) {
				if(dropDownList[0].filter.length > 0) {
					data.filter(el => dropDownList[0].filter.includes(el.name)).forEach((item)=>{
						floor = Obj.itemPushfunction(floor, item.floor);
						flat = Obj.itemPushfunction(flat, item.flat);
						tower = Obj.itemPushfunction(tower, item.tower);
					})
					data.forEach((item)=>{
						name = Obj.itemPushfunction(name, item.name);
					})
				}

				if(dropDownList[1].filter.length > 0) {
					data.filter(el => dropDownList[1].filter.includes(el.tower)).forEach((item)=>{
						name = Obj.itemPushfunction(name, item.name);
						flat = Obj.itemPushfunction(flat, item.flat);
						floor = Obj.itemPushfunction(floor, item.floor);
					})

					if(dropDownList[0].filter.length > 0) {
						data.filter(el => dropDownList[0].filter.includes(el.name)).forEach((item)=>{
							tower = Obj.itemPushfunction(tower, item.tower);
						})
						floor = [];
						data.filter(el => dropDownList[0].filter.includes(el.name))
							.filter(el => dropDownList[1].filter.includes(el.tower))
							.forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
						})
					}else {
						data.forEach((item)=>{
							tower = Obj.itemPushfunction(tower, item.tower);
						})
					}

				}

				if(dropDownList[2].filter.length > 0) {
					data.filter(el => dropDownList[2].filter.includes(el.floor)).forEach((item)=>{
						name = Obj.itemPushfunction(name, item.name);
						tower = Obj.itemPushfunction(tower, item.tower);
						flat = Obj.itemPushfunction(flat, item.flat);
					})

					if(dropDownList[1].filter.length > 0) {
						data.filter(el => dropDownList[1].filter.includes(el.tower)).forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
						})
						flat = [];
						data.filter(el => dropDownList[0].filter.includes(el.name))
							.filter(el => dropDownList[1].filter.includes(el.tower))
							.filter(el => dropDownList[2].filter.includes(el.floor))
							.forEach((item)=>{
								flat = Obj.itemPushfunction(flat, item.flat);
						})
					}else {
						data.forEach((item)=>{
							floor = Obj.itemPushfunction(floor, item.floor);
						})
					}


				}


				if(dropDownList[3].filter.length > 0) {
					data.filter(el => dropDownList[3].filter.includes(el.flat)).forEach((item)=>{
						name = Obj.itemPushfunction(name, item.name);
						tower = Obj.itemPushfunction(tower, item.tower);
						floor = Obj.itemPushfunction(floor, item.floor);
					})

					if(dropDownList[2].filter.length > 0) {
						data.filter(el => dropDownList[2].filter.includes(el.floor))
						.forEach((item)=>{
							flat = Obj.itemPushfunction(flat, item.flat);
						})

					}else {
						data.forEach((item)=>{
							flat = Obj.itemPushfunction(flat, item.flat);
						})
					}


				}


			} else {
				data.forEach((item)=>{
					name = Obj.itemPushfunction(name, item.name);
					tower = Obj.itemPushfunction(tower, item.tower);
					floor = Obj.itemPushfunction(floor, item.floor);
					flat = Obj.itemPushfunction(flat, item.flat);
				})
			}


			// tower = tower.filter(function(item, pos) {
			// 	return tower.indexOf(item) == pos;
			// })

			// floor = floor.filter(function(item, pos) {
			// 	return floor.indexOf(item) == pos;
			// })

			// flat = flat.filter(function(item, pos) {
			// 	return flat.indexOf(item) == pos;
			// })

            let dropDownData = [
				{
					title: i18.Checklist,
					list: name ? name.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[0].filter : [],
				},
				{
					title: i18.tower,
					list: tower ? tower.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[1].filter : [],
				},
				{
					title: i18.floor,
					list: floor ? floor.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[2].filter : [],
				},
				{
					title: i18.flat,
					list: flat ? flat.sort((a, b) => Obj.naturalCompare(a, b)) : [],
					filter: dropDownList.length > 0 ? dropDownList[3].filter : [],
				}
            ];
            return dropDownData;
		},
		itemPushfunction: (item, pushItem) => {
			if(pushItem){
				let found = false;
				for (var i = 0, len = item.length; i < len; i++) {
					if (item[i] === pushItem) {
						found = true;
						break;
					}
				}

				if (!found)
					item.push(pushItem);
			}
			return item;
		},
		getUnitFloorPlan: (unitId) => {
			dispatch(Actions.project.getUnitFloorPlan(unitId));
		},
		naturalCompare: (a, b) => {
			var ax = [], bx = [];

			a.replace(/(\d+)|(\D+)/g, function(_, $1, $2) { ax.push([$1 || Infinity, $2 || ""]) });
			b.replace(/(\d+)|(\D+)/g, function(_, $1, $2) { bx.push([$1 || Infinity, $2 || ""]) });

			while(ax.length && bx.length) {
				var an = ax.shift();
				var bn = bx.shift();
				var nn = (an[0] - bn[0]) || an[1].localeCompare(bn[1]);
				if(nn) return nn;
			}

			return ax.length - bx.length;
		}

	}

	return Obj;
}

module.exports = mapDispatchToProps;
