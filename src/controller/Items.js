import ImagePicker from 'react-native-image-picker';

import Server from 'Server';
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import { delay } from 'Utilities';
import moment from 'moment';
import i18 from 'i18';

const Actions = require('Redux').Action;
// import { naturalCompare } from '../util/commonHelper'
import naturalCompare from "string-natural-compare";

import CachedImage  from 'react-native-cached-image';
const {
    ImageCacheProvider
} = CachedImage;
import { SERVER_URL } from 'Config';
import { _getHeaders, _getServerUrl } from 'Request';
import {storeItemsForPending} from "../redux/reducer/items";

async function downloadItem(result){
    let sequences = result.pictures.map(picture=> ()=>downloadImage(picture));
    return Promise.sequence(sequences)
    .then(()=>{
        console.log("Put it to LocalDB")
        let resp = ImageCacheProvider.getCacheInfo().then((resp)=>{
            //console.log("defaultImageCacheManager", resp)
        });
        
        //Put it to LocalDB
        return LocalDB.save('Item',result).then(resp => result);
    })  
}

function downloadImage(picture) {
	if (!picture.path.startsWith("http")) {
		let serverUrl = _getServerUrl();
		picture.path = serverUrl + "blob_image/" + picture.path;
	}
	// first delete the cached image, otherwise the new content with the same path would failed
	return ImageCacheProvider.deleteCachedImage(picture.path). then(() => {
		return ImageCacheProvider.cacheImage(picture.path, {}, _getHeaders).then((imagePath)=>{
			return imagePath;
		}).catch((err) => {
			console.log("downloadImage failed", err);
			return picture.path;
		});
	});
}


const mapDispatchToProps = (dispatch, ownProps) => {
  return {
        fetchItems: (project) => {
			dispatch(Actions.loading.api('fetchItems'));
			console.logTime('fetchItems start');
            Server.fetchItems(project).then(resp => {
                console.logTime('fetchItems end');
                dispatch(Actions.loading.apiSuccess('fetchItems'));
                dispatch(Actions.items.fetched(resp, false));
            }).catch(error => {
				console.warn('error',error);
				alert(error);
                dispatch(Actions.loading.apiSuccess('fetchItems'));
            })
        },
        fetchItemDropdownOptions: (projectDetail) => {
            dispatch(Actions.loading.api('fetchItemDropdownOptions'));
            let status = [];
            let defect_item_status = projectDetail.defect_item_status;
            if(defect_item_status) {
                defect_item_status.forEach((item) => {
                    status.push(item.name);
                })
            }
            return Server.fetchItemDropdownOptions(projectDetail._id).then(resp => {
                let dropDownData = [
                    {   title: i18.fillStatus,
                        list: status || [],
                        filter: []
                    },
                    {
                        title: i18.tower,
                        list: resp.tower || [],
                        filter: [],
                    },
                    {
                        title: i18.floor,
                        list: resp.floor || [],
                        filter: [],
                    },
                    {
                        title: i18.flat,
                        list: resp.flat || [],
                        filter: [],
                    },
                    {
                        title: i18.location,
                        list: resp.location || [],
                        filter: [],
                    },
                    {
                        title: i18.level1,
                        list: resp.level1 || [],
                        filter: [],
                    },
                    {
                        title: i18.level2,
                        list: resp.level2 || [],
                        filter: [],
                    },
                    {
                        title: i18.Checklist,
                        list: resp.name || [],
                        filter: [],
                    }
                ];
                dispatch(Actions.loading.apiSuccess('fetchItemDropdownOptions'));
                return dropDownData;
            }).catch(err => {
            	alert(err);
                dispatch(Actions.loading.apiSuccess('fetchItemDropdownOptions'));
			})
        },
        // updateListSelection: (items) => {
        //     dispatch(Actions.items.selected(items));
        // },
        downloadItem: (item)=>{
			// Download following checklist
			// console.log('downloading item:',item);

			// create an object instance for cancel
			let cancelable = {};			

			let asyncTask = item.map(c => ()=>{
				return Server.fetchItem(c).then(result=>{
                    
                    //TODO: promise sequence for downlaoding image  
                    return downloadItem(result)

				})
			});

			console.logTime('downloadItem start');
			dispatch(Actions.progressBar.reset(asyncTask.length,true,cancelable));
			
			let successCount = 0;
			Promise.sequence( asyncTask, (subResult, step, total)=>{
				if(step > 0 && subResult && subResult._id){
					successCount = successCount + 1;
				}
				dispatch(Actions.progressBar.progress(step, subResult instanceof Error, cancelable, successCount));
			},{
				skipIfError: true,
				cancelable
			}).then(result=>{

				// console.log('Promise.sequence result:',result);
				// console.log('result:',result.map(r => r._id));

				// filter out the error object(s)
				let downloadItems = result.filter((r) => {
					return ('_id' in r);
				});
				console.logTime('downloadItem end');
				// console.log('result:',downloadItems.map(r => r._id));

				// Put them to redux, therefore, UI will refresh
				dispatch(Actions.items.fetched(downloadItems, false));

				return result;
			}).catch(err=>{
				// should not be here				
				console.warn('err:',err);				
				alert(err);
			});
		},
		fetchLocalItems: () => {
			dispatch(Actions.loading.storage('fetchLocalItems'));
			// console.logTime('fetchLocalItems start');
			setTimeout(()=>{
				LocalDB.fetch('Item').then(resp => {	
					// console.logTime('fetchLocalItems end');
					// console.log('fetchLocalItems:',resp);
					dispatch(Actions.loading.storageSuccess('fetchLocalItems'));
					dispatch(Actions.items.fetched(resp, true));
				}).catch(error => {
					console.warn('error',error);				
					dispatch(Actions.loading.storageSuccess('fetchLocalItems'));
				});
			},0);
		},
		fetchLocalUnitItems: () => {
			dispatch(Actions.loading.storage('fetchLocalUnitItems'));
			console.logTime('fetchLocalUnitItems start');
			setTimeout(()=>{
				LocalDB.fetch('UnitItem').then(resp => {	
					// console.logTime('fetchLocalUnitItems end');
					// console.log('fetchLocalUnitItems:',resp);
					dispatch(Actions.loading.storageSuccess('fetchLocalUnitItems'));
					dispatch(Actions.items.fetchLocalUnitItems(resp));
				}).catch(error => {
					console.warn('error',error);				
					dispatch(Actions.loading.storageSuccess('fetchLocalUnitItems'));
				});
				LocalDB.fetch('Item').then(resp => {	
					// console.logTime('fetchLocalItems end');
					// console.log('fetchLocalItems:',resp);
					dispatch(Actions.loading.storageSuccess('fetchLocalItems'));
					dispatch(Actions.items.fetched(resp, false));
				}).catch(error => {
					console.warn('error',error);				
					dispatch(Actions.loading.storageSuccess('fetchLocalItems'));
				});
			},0);
		},
		modifyItem: (id) => {
			dispatch(Actions.items.modifyItem(id));
		},
		filterItem: (data) => {
			dispatch(Actions.items.filterItem(data));
		},
        searchRespItems: (project, data, searchDefectType) => {
            dispatch(Actions.loading.api('fetchRespItemsCustom'));
            // console.logTime('searchRespItems start');
            const statusFilter = (!data[0].filter || data[0].filter.length === 0)? null: data[0].filter;
            const towerFilter = (!data[1].filter || data[1].filter.length === 0)? null: data[1].filter;
            const floorFilter = (!data[2].filter || data[2].filter.length === 0)? null: data[2].filter;
            const unitFilter = (!data[3].filter || data[3].filter.length === 0)? null: data[3].filter;
            const locationFilter = (!data[4].filter || data[4].filter.length === 0)? null: data[4].filter;
            const level1Filter = (!data[5].filter || data[5].filter.length === 0)? null: data[5].filter;
            const level2Filter = (!data[6].filter || data[6].filter.length === 0)? null: data[6].filter;
            const checklistFilter = (!data[7].filter || data[7].filter.length === 0)? null: data[7].filter;
            Server.fetchRespItemsCustom(project, statusFilter, towerFilter, floorFilter, unitFilter,
                locationFilter, level1Filter, level2Filter, checklistFilter, searchDefectType).then(resp => {
                // console.logTime('searchRespItems end');
                dispatch(Actions.loading.apiSuccess('fetchRespItemsCustom'));
                dispatch(Actions.items.fetched(resp, true));
            }).catch(error => {
                console.log('error: ',error);
                alert(i18.getItemErrMsg);
                dispatch(Actions.loading.apiSuccess('fetchRespItemsCustom'));
            })
        },
		fakeUpdateItemData: (data) => {
			dispatch(Actions.items.fakeUpdateItemData(data));
		},
		updateSelectOptions: (data, option) => {
			dispatch(Actions.items.updateSelectOptions(data, option));
        },
		updateItemImages: (id, paths) => {
			dispatch(Actions.items.updateItemImages(id, paths));
		},
        addMessage: (message, checklistRespItem, isConnected, user) => {
            if(isConnected){
                Server.addMessage(message, checklistRespItem).then(resp => {	
                    dispatch(Actions.items.addMessage(resp));		
                    dispatch(Actions.loading.apiSuccess('addMessage'));
                }).catch(error => {
					alert(error);
                    console.warn('error',error);				
                    dispatch(Actions.loading.apiSuccess('addMessage'));
                })
            }else {
                let obj = {
                    content: message,
                    user: user.username,
                    _id: user._id,
                    date: moment.utc()
                }
                dispatch(Actions.items.addOfflineMessage(obj, checklistRespItem));
            }
        },
		// updateLabelData: (data) => {
		// 	dispatch(Actions.checklists.updateLabelData(data));
		// },
		// reCalLabelData: () => {
		// 	dispatch(Actions.checklists.reCalLabelData());
		// },
		// updateOtherText: (id, otherText) => {
		// 	dispatch(Actions.checklists.updateOtherText(id, otherText));
		// },
		pendingItems: (item) => {
			dispatch(Actions.loading.storage('pendingItems'));
			setTimeout(()=>{
                if(!item.images) {
                    dispatch(Actions.items.pendingItems(item));
                    dispatch(Actions.project.updatePendingCount("+1"));
                    dispatch(Actions.loading.storageSuccess('pendingItems'));
                } else {
                    Promise.all(item.images.filter(image => (image && image.path))).then(resp => {
                        if (resp) {
                            item.images = resp;
                        }
                        dispatch(Actions.items.pendingItems(item));
                        dispatch(Actions.project.updatePendingCount("+1"));
                        dispatch(Actions.loading.storageSuccess('pendingItems'));
                    });
                }
			},0);
		},
		selectItemsForPending: (data, items) => { // data = selected data
			dispatch(Actions.loading.storage('pendingItems'));
			setTimeout(()=>{
                const result = Promise.all(items.map((item)=> {
                    if(data.includes(item._id)){
                        return new Promise((resolve, reject)=>{
                            item.saved_at = new Date(); // only used in app locally
                            LocalDB.save('PendingItem', item ).then(()=>{
                                resolve(item);
                            }).catch((error) => {
                                alert('Unable to convert Item to PendingItem in local storage.');
                                reject(item);
                            })
                        }).then((item) => {
                            LocalDB.delete('Item', item._id ).catch(()=> {
                            	alert('Unable to remove Item in local storage.');
							});
                            return item;
                        })
                    }
                })).then(()=>{
                    dispatch(Actions.items.selectItemsForPending(data));
                    dispatch(Actions.project.updatePendingCount("+" + data.length));
                    dispatch(Actions.loading.storageSuccess('pendingItems'));
                });
                result.then();
			},0);
		},
		fetchLocalPendingItems: () => {
			dispatch(Actions.loading.storage('fetchLocalPendingItems'));
			// console.logTime('fetchLocalPendingItems start');
			setTimeout(()=>{
				LocalDB.fetch('PendingItem').then(resp => {	
					// console.logTime('fetchLocalPendingItems end');
					// console.log('fetchLocalPendingItems:',resp);
					dispatch(Actions.loading.storageSuccess('fetchLocalPendingItems'));
					dispatch(Actions.items.fetchedPendingItems(resp, false));
				}).catch(error => {
					// console.warn('error',error);
					dispatch(Actions.loading.storageSuccess('fetchLocalPendingItems'));
				});
			},0);
		},
		cleanItemData: ()=> {
			dispatch(Actions.items.cleanItemData());
		}
    }
}

module.exports = mapDispatchToProps;