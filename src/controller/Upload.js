import ImagePicker from 'react-native-image-picker';

import Server from 'Server';
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import { delay } from 'Utilities';
import {Alert, Platform} from 'react-native'
import { Crashlytics } from 'react-native-fabric'
import i18 from 'i18';


const Actions = require('Redux').Action;

async function createRespItem (objectCreateChecklistRespItem, source, checkingRequired) {

  let sequences = objectCreateChecklistRespItem.images.map(img => () => uploadImage(img))
  let pictures

  return Promise.sequence(sequences)
    .then(a => {
      pictures = a
    })
    .then(() => {
      let data
      pictures = pictures.filter(x => x)
      if (source == 'checklist') {
        data = {
          comments: objectCreateChecklistRespItem.comments ? objectCreateChecklistRespItem.comments : undefined,
          option: objectCreateChecklistRespItem.option ? objectCreateChecklistRespItem.option : '',
          pictures: pictures,
          annotations: objectCreateChecklistRespItem.annotations ? objectCreateChecklistRespItem.annotations : undefined
        }
      } else {
        data = {
          //comments: objectCreateChecklistRespItem.comments,
          option: objectCreateChecklistRespItem.option ? objectCreateChecklistRespItem.option : undefined,
          pictures: pictures,
          description: objectCreateChecklistRespItem.description ? objectCreateChecklistRespItem.description : undefined,
          location: objectCreateChecklistRespItem.location ? objectCreateChecklistRespItem.location : undefined,
          level1: objectCreateChecklistRespItem.level1 ? objectCreateChecklistRespItem.level1 : undefined,
          level2: objectCreateChecklistRespItem.level2 ? objectCreateChecklistRespItem.level2 : undefined,
          critical: objectCreateChecklistRespItem.critical ? objectCreateChecklistRespItem.critical : false,
          annotations: objectCreateChecklistRespItem.annotations ? objectCreateChecklistRespItem.annotations : undefined
        }
      }
      return Server.createChecklistRespItem(objectCreateChecklistRespItem.checklist_resp, objectCreateChecklistRespItem._id
          , data, objectCreateChecklistRespItem.checklistId, objectCreateChecklistRespItem.originalId, checkingRequired)
    }).then(result => {
        // console.log("createChecklistRespItem result: ", result);
        return result
    }).catch(err => {
      if (Platform.OS !== 'ios') {
        Crashlytics.logException(`Cannot sent ${JSON.stringify(image)} ${JSON.stringify(err)}`)
      } else {
        Crashlytics.recordError(`Cannot sent ${JSON.stringify(image)} ${JSON.stringify(err)}`)
      }
      // console.log('err1', err)
      // console.warn('err:', err)
      if(err.http_status) {
          alert(`(${err.http_status}) ${i18.UploadConnectionIssue}`);
      } else {
          alert(JSON.stringify(err));
      }
      return false
    })
}

function uploadImage (image) {
  let progress = (data) => {
    //console.log('progress:',data);
  }
  return Server.uploadImage(image, progress).then((result) => {
    let combine = {
      path: result,
      caption: image.caption,
    }
    return combine
  }).catch(err => {
    if (Platform.OS !== 'ios') {
      Crashlytics.logException(`Cannot sent ${JSON.stringify(image)} ${JSON.stringify(err)}`)
    } else {
      Crashlytics.recordError(`Cannot sent ${JSON.stringify(image)} ${JSON.stringify(err)}`)
    }
    console.log('err2', err)
    console.warn('err:', err)
    alert(JSON.stringify(err));
  })
}



async function updateRespItem(id, images, messages, status) {
    let sequences = images.map(img => () => uploadImage(img));

    let pictures;


    let messageDidNotUpload = [];
    messages.forEach((message) => {
        if (message.offline) {
            messageDidNotUpload.push(message.content);
        }
    })

    return Promise.sequence(sequences)
        .then(a => {
            pictures = a;
        })
        .then(() => {
            let data = {
                messages: messageDidNotUpload,
                pictures: pictures,
                status: status ? status : "未開始",
                files: [],
            }
            return Server.updateChecklistRespItem(id, data)
        }).then(result => {
            return result;
        })
}


async function updateKey(task2) {
    task2.originalId = task2._id;
    task2._id = undefined;
    task2.current_owner_type = 'staff';
    task2.last_handled_by =  "w7qDh6";

    return Server.updateKey(task2)
    .catch(err => {
        console.warn('err key:', err);
        alert(err);
        throw err;
    });
}

async function updateUnit(id, stage, status_int, checkboxes, unit_key) {

    if(unit_key.hasUpdated){
        unit_key.originalId = undefined;
        unit_key.current_owner_type = 'staff';
        unit_key.last_handled_by =  "w7qDh6";
        // console.log("unit_key", unit_key)
        Server.updateKey(unit_key)
        .catch(err => {
            console.warn('err unit key:', err);
            alert(err);
            throw err;
        });
    }


    let data = {
        _id: id,
        stage: stage,
        status_int: status_int,
        checkboxes: checkboxes,
    }
    return Server.updateUnit(id, data)
        .catch(err => {
            console.warn('err:', err);
            alert(err);
            throw err;
        });
}

function refreshUploadUI(dispatch, removedItemList, removedItemType) {
    if(removedItemList.length > 0) {
        dispatch(Actions.loading.storage(removedItemType));
        console.logTime(`${removedItemType} start`);
        setTimeout(() => {
            if (removedItemType === 'removeLocalPendingChecklists') {
                dispatch(Actions.checklists.removePendingCheckLists(removedItemList));
            } else if (removedItemType === 'removeLocalPendingItems') {
                dispatch(Actions.items.removePendingItems(removedItemList));
            } else if (removedItemType === 'removeLocalPendingUnits') {
                dispatch(Actions.units.removePendingUnits(removedItemList));
            } else if(removedItemType === 'removePendingKey') {
                dispatch(Actions.project.removePendingKey(removedItemList));
            }
            dispatch(Actions.loading.storageSuccess(removedItemType));
        }, 0);
    }
}

const mapDispatchToProps = (dispatch, ownProps) => {
    var obj = {
        uploadChecklist: false,
        uploadItem: false,
        uploadUnit: false,
        uploadKey: false,
        uploadTask: (data) => {
            // create an object instance for cancel
            let cancelable = {};
            let chklistSubitemsMap = {}; // Mapped checklist id with checklist to-be-submitted items no

            let asyncTask1 = [];
            let asyncTask2 = [];
            let allTask = [];
            // console.log("data", data);
            data.forEach(item => {
                if (item.type == 'checklist') {
                    return item.data.map(c => {
                        let objForResp = {};

                        if(c.source == 'adhoc' || c.source == 'customer'){
                            objForResp.source = c.source;
                            objForResp.name = c.name;
                            objForResp.flat = c.flat ? c.flat : undefined;
                            objForResp.floor = c.floor ? c.floor : undefined;
                            objForResp.tower = c.tower ? c.tower : undefined;
                            objForResp.unit = c.unit ? c.unit : undefined;
                            c.questions.forEach(q => {
                                q.checklistId = c._id;
                                q.type2 = 'checklist';
                                obj.uploadChecklist = true;
                                allTask.push(q);
                                chklistSubitemsMap[c._id] = (chklistSubitemsMap[c._id] || 0) + 1 ;
                            });
                        } else {
                            objForResp.source = 'checklist';
                            objForResp.checklist = c._id;
                            c.questions.forEach(q => {
                                q.data.filter(f => f.status != "Unfill").map(d => {
                                    d.checklistId = c._id;
                                    d.type2 = 'checklist';
                                    obj.uploadChecklist = true;
                                    allTask.push(d);
                                    chklistSubitemsMap[c._id] = (chklistSubitemsMap[c._id] || 0) + 1 ;
                                });
                            });
                        }
                        let task1;

                        if(c.checklist_resp) {
                            task1 = () => {
                                return c;
                            }
                        }else {
                            task1 = () => Server.createChecklistResp(objForResp).then(result => {
                                c.checklist_resp = result._id;
                                return c;
                            })
                                .catch(err => {
                                    console.warn('err createChecklistResp:', err);
                                    alert(err);
                                });
                        }

                        asyncTask1.push(task1);
                    });
                } else if (item.type == 'item') {
                    return item.data.map(c => {
                        c.type2 = 'item';
                        obj.uploadItem = true;
                        allTask.push(c);
                    });
                } else if (item.type == 'unit') {
                    return item.data.map(c => {
                        c.type2 = 'unit';
                        obj.uploadUnit = true;
                        allTask.push(c);
                    });
                } else if (item.type == 'key') {
                    return item.data.map(c => {
                        c.type2 = 'key';
                        obj.uploadKey = true;
                        allTask.push(c);
                    });
                }
            });

            console.logTime('upload start');
            dispatch(Actions.progressBar.reset(allTask.length, false, cancelable));

            Promise.sequence(asyncTask1)
                .then((result) => {
                    // Temporary storage for removed items
                    let removedChecklistList = [];
                    let removedItemList = [];
                    let removedUnitList = [];
                    let removedKeyList = []
                    let chklistSubitemsCounterMap = {}; // Mapped checklist id with checklist to-be-submitted items no
                    let checkingRequired = true;
                    asyncTask2 = allTask.map(task2 => {
                        let option;
                        if (task2.type2 == 'checklist') {
                            option = task2.otherText ? (task2.selectOptions.toString() + "," + task2.otherText) : (task2.selectOptions.toString());

                            let currentTask = result.find((task1) => {
                                return task1._id == task2.checklistId;
                            });
                            if (currentTask) {
                                task2.checklist_resp = currentTask.checklist_resp;
                                task2.source = currentTask.source;
                            }
                        }

                        let images = (task2.images) ? task2.images.filter(imagePath => {
                            // check it is not a url and check it is a valid file path
                            let validFilePath = true;
                            if (!imagePath.path){
                                return false
                            }
                            if (imagePath.path.startsWith('file://')) {
                                let path = imagePath.path.replace("file://", "");
                                if (path.length == 20) {
                                    validFilePath = false;
                                }
                            }
                            return !imagePath.path.startsWith('http') && validFilePath;
                        }).map((imagePath) => {
                            let pathComponent = imagePath.path.split("/");

                            if (imagePath.path.indexOf('file://') == -1) {
                                imagePath.path = "file://" + imagePath.path;
                            }
                            return {
                                fileName: pathComponent[pathComponent.length - 1],
                                origURL: imagePath.path,
                                caption: imagePath.caption
                            };
                        }) : [];

                        return () => {
                            if (task2.type2 == 'checklist') {
                                let objectCreateChecklistRespItem = {};

                                if(task2.source != 'adhoc' && task2.source != 'customer'){
                                    objectCreateChecklistRespItem.checklist_resp = task2.checklist_resp;
                                    objectCreateChecklistRespItem._id = task2._id;
                                    objectCreateChecklistRespItem.originalId = task2._id;
                                    objectCreateChecklistRespItem.checklistId = task2.checklistId;
                                    objectCreateChecklistRespItem.option  = option;
                                    objectCreateChecklistRespItem.images  = images;
                                    objectCreateChecklistRespItem.comments  = task2.comments;
                                    objectCreateChecklistRespItem.annotations  = task2.annotations;
                                    task2.source = 'checklist';
                                }else {
                                    objectCreateChecklistRespItem.checklist_resp = task2.checklist_resp;
                                    objectCreateChecklistRespItem._id = undefined;
                                    objectCreateChecklistRespItem.checklistId = task2.checklistId;
                                    objectCreateChecklistRespItem.originalId = task2._id;
                                    objectCreateChecklistRespItem.option  = option;
                                    objectCreateChecklistRespItem.images  = images;
                                    objectCreateChecklistRespItem.description  = task2.description;
                                    objectCreateChecklistRespItem.location  = task2.location;
                                    objectCreateChecklistRespItem.level1  = task2.level1;
                                    objectCreateChecklistRespItem.level2  = task2.level2;
                                    objectCreateChecklistRespItem.critical  = task2.critical;
                                    objectCreateChecklistRespItem.unit  = task2.unit;
                                    objectCreateChecklistRespItem.annotations  = task2.annotations;
                                }
                                // return createRespItem(objectCreateChecklistRespItem, task2.source)
                                return createRespItem(objectCreateChecklistRespItem, task2.source, checkingRequired)
                                    .then(checkList => {
                                        // TODO modification may be reuired for checking only the first item
                                        // checkingRequired = false;
                                        chklistSubitemsCounterMap[checkList.checklistId] = (chklistSubitemsCounterMap[checkList.checklistId] || 0) + 1;
                                        if(chklistSubitemsMap[checkList.checklistId] === chklistSubitemsCounterMap[checkList.checklistId]) {
                                            LocalDB.delete('PendingCheckList', checkList.checklistId ).then(() => {
                                                removedChecklistList.push(checkList);
                                            }).catch(err => {
                                                console.log(err);
                                            });
                                        } else {
                                            removedChecklistList.push(checkList);
                                        }
                                        return checkList;
                                    });
                            } else if (task2.type2 == 'item') {

                                return updateRespItem(task2._id, images, task2.messages, task2.status)
                                    .then(item => {
                                        LocalDB.delete('PendingItem', item._id ).then(()=> {
                                            removedItemList.push(item);
                                        }).catch(err => {
                                            console.log(err);
                                        });
                                        return item;
                                    });
                            } else if (task2.type2 == 'unit') {
                                return updateUnit(task2._id, task2.stage, task2.status_int, task2.checkboxes, task2.unit_key)
                                    .then(unit => {
                                        LocalDB.delete('PendingUnit', unit._id ).then(()=> {
                                            removedUnitList.push(unit);
                                        }).catch(err => {
                                            console.log(err);
                                        });
                                        return unit;
                                    });
                            } else if (task2.type2 == 'key') {
                                return updateKey(task2)
                                    .then(key => {
                                        LocalDB.delete('QrcodeData', task2.originalId).then(()=> {
                                            removedKeyList.push(key);
                                        }).catch(err => {
                                            console.log(err);
                                        });
                                        return key;
                                });
                            }
                        }
                    })

                    let successCount = 0;
                    Promise.sequence(asyncTask2, (subResult, step, total) => {
                        // console.log(subResult, step, total);
                        if(step > 0 && subResult){
                            successCount = successCount + 1;
                        }
                        dispatch(Actions.progressBar.progress(step, subResult instanceof Error, cancelable, successCount));
                    }, {
                            skipIfError: false,
                            cancelable
                        }).then(result => {
                            console.logTime('Upload end');
                            // Dispatch removed items
                            refreshUploadUI(dispatch, removedChecklistList, 'removeLocalPendingChecklists');
                            refreshUploadUI(dispatch, removedItemList, 'removeLocalPendingItems');
                            refreshUploadUI(dispatch, removedUnitList, 'removeLocalPendingUnits');
                            refreshUploadUI(dispatch, removedKeyList, 'removePendingKey');
                            // Update the counter with the remaining item count
                            const totalCount = (allTask.length || 0) - (removedChecklistList.length || 0)
                                - (removedItemList.length || 0) - (removedUnitList.length || 0) - (removedKeyList.length || 0);
                            dispatch(Actions.project.updatePendingCount((!totalCount || totalCount < 0)? 0 : totalCount));
                            return result;
                        }).catch(err => {
                            alert("Error:" + JSON.stringify(err));
                            Alert.alert(
                                i18.UploadInterrupted,
                                '',
                                [
                                    {text: i18.OK, onPress: () => {
                                            dispatch(Actions.progressBar.cancel());
                                            // Dispatch removed items
                                            refreshUploadUI(dispatch, removedChecklistList, 'removeLocalPendingChecklists');
                                            refreshUploadUI(dispatch, removedItemList, 'removeLocalPendingItems');
                                            refreshUploadUI(dispatch, removedUnitList, 'removeLocalPendingUnits');
                                            refreshUploadUI(dispatch, removedKeyList, 'removePendingKey');
                                            // Update the counter with the remaining item count
                                            const totalCount = (allTask.length || 0) - (removedChecklistList.length || 0)
                                                - (removedItemList.length || 0) - (removedUnitList.length || 0) - (removedKeyList.length || 0);
                                            dispatch(Actions.project.updatePendingCount((!totalCount || totalCount < 0)? 0 : totalCount));
                                            // return err;
                                        }
                                    },
                                ],
                                { cancelable: false }
                            );
                        })
                }).then(result => {

              }).catch(err => {
                  console.warn('err:', err)
                return false
            })
        },



    }



    return obj;
}

module.exports = mapDispatchToProps;
