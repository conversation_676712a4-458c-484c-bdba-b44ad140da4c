import ImagePicker from 'react-native-image-picker';

import Server from 'Server';
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import { delay } from 'Utilities';

const Actions = require('Redux').Action;

const mapDispatchToProps = (dispatch, ownProps) => {
  return {
		setMainNavigation: (navigation)=>{
			dispatch(Actions.navigation.setMainNavigation(navigation));
		},
  }
}

module.exports = mapDispatchToProps;