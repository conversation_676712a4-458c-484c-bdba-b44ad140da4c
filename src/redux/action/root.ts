import { createActions } from 'reduxsauce';

// Define action types
export interface RootActionTypes {
	ROOT_TEST: string;
}

// Define action creators
export interface RootActionCreators {
	test: () => { type: string };
}

const { Types, Creators } = createActions({
	// testFetch: ['someParam'],
	// testFetchSuccess: ['someResp'],
	// testFetchError: ['someError'],
	test: null,
	// testCustom: (a, b) => {
	// 	return { type: 'CUSTOM', total: a + b };
	// },
	// onClick: ['username'],
},{
	prefix: 'ROOT_'
});

export { Types, Creators };
export default { Types, Creators };