import { createActions } from 'reduxsauce'

const { Types, Creators } = createActions({
	fetched: ['units', 'isResetNew'],
	selected: ['units'],
	modifyUnit: ['id'],
	filterUnit: ['data'],
	fakeUpdateUnitData: ['data'],
	updateSelectOptions: ['data', 'option'],
	updateCheckboxes: ['id', 'key', 'value'],
	pendingUnits: ['unit'],
	fetchedPendingUnits: ['data'],
	removePendingUnits: ['data'],
	updateQRCode: ['data', 'showKeyForm'],
	updateUnitKey: ['data'],
    resetNewUnitset: ['data'],
    fetchLocalItemStatusSummary: ['data'],
    fetchLocalChecklistSummary: ['data']
},{
	prefix: 'UNITS_'
})
module.exports = { Types, Creators }