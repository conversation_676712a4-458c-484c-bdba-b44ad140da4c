import { createActions } from 'reduxsauce'

const { Types, Creators } = createActions({
	fetched: ['items', 'isResetNew'],
	fetchLocalUnitItems: ['items'],
	selected: ['items'],
	modifyItem: ['id'],
	filterItem: ['data'],
	fakeUpdateItemData: ['data'],
	updateSelectOptions: ['data', 'option'],
	updateItemImages: ['id', 'paths'],
	addMessage: ['data'],
	addOfflineMessage: ['message', 'id'],
	// updateLabelData: ['data'],
	// updateOtherText: ['id', 'text'],
	// reCalLabelData: [],
	pendingItems: ['item'],
	selectItemsForPending: ['data', 'items'],
	fetchedPendingItems: ['data'],
	removePendingItems: ['data'],
	cleanItemData: [],
},{
	prefix: 'ITEM_'
})
module.exports = { Types, Creators }