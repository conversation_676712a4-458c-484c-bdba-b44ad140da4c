import { createActions } from 'reduxsauce'

const { Types, Creators } = createActions({
	fetched: ['checklists', 'isResetNew'],
	loaded: ['checklists'],
	selected: ['checklists'],
	modifyCheckList: ['id'],
	modifyUnitCheckList: ['items', 'id'],
	modifyAdhocCheckList: ['id'],
	viewUnitCheckList: ['id'],
	filterCheckList: ['label', 'data', 'filteredChecklist'],
	fakeUpdateUnfillData: ['data'],
	fakeUpdateAdhocData: ['data'],
	updateSelectOptions: ['data', 'label', 'id', 'option', 'from'],
	updateLabelData: ['data'],
	updateOtherText: ['id', 'text'],
	updateComments: ['id', 'text'],
	updateFloorPlanAnnotations: ['id', 'annotations'],
	updateItemImages: ['id', 'paths'],
	reCalLabelData: [],
	pendingCheckLists: ['checklist'],
	fetchedPendingCheckLists: ['data'],
	removePendingCheckLists: ['data'],
	// fetchedAdhocFormOption: ['data'],
	filterTask: ['data'],
	fakeUpdateTaskData: ['data'],
	getFormSubmissionFilter: ['formSubmission_options'],
	createAddHocCheckList: ['data'],
	modifyAddHocCheckList:  ['data'],
	resetChecklist: [],
	deleteAdhocData: ['id', 'data'],
	clearChecklists: [],
	updateExpandedChecklist: ['id'],
},{
	prefix: 'CHECKLIST_'
})
module.exports = { Types, Creators }