import { createActions } from 'reduxsauce'

const { Types, Creators } = createActions({
    fetched: ['data'],
    fetchProjectLists: ['projectLists'],
    updateSelectedProject: ['selectedProject'],
    fetchProjectDetail: ['projectDetail'],
    updateChecklistsCount: ['checklistsCount'],
    updateUnitsCount: ['unitsCount'],
    updateItemsCount: ['itemsCount'],
    updatePendingCount: ['pendingCount'],
    fetchUnitsInfo: ['unitsInfo'],
    getAdHocFilter: ['adhoc_options'],
    updateKeyQRCode: ['data', 'showKeyForm'],
    updateKey: ['data'],
    removePendingKey: ['data'],
    fetchHandoverForm: ['data'],
    getUnitFloorPlan: ['unitId'],
    fetchChecklistList: ['data'],
    fetchUsers:['users']
},{
	prefix: 'PROJECT_'
})

module.exports = { Types, Creators }