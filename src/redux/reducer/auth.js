import { createReducer } from 'reduxsauce'
import { REHYDRATE } from 'redux-persist/constants'
import Server from 'Server'
import moment from 'moment';

const { Types } = require('../action/auth');

export const INITIAL_STATE = {
	user: null,
	lastLoginDateStr: null,
	showAlertForgotPassword: false,
}

export const loggedInUser = (state = INITIAL_STATE, action) => {
	let nowStr = moment().local().format('YYYY-MM-DD HH:mm');
	return { ...state, user: action.user.deepClone(), lastLoginDateStr: nowStr };
}

export const loggedOutUser = (state = INITIAL_STATE, action) => {
	return { ...state, user: null, lastLoginDateStr: null };
}

// special listener, Remove it later
export const autoLogin = (state = INITIAL_STATE, action) => {

	let user = action.payload.auth && action.payload.auth.user || null;
	Server.setUser(user);

	return state;
}

export const showAlertForgotPassword = (state = INITIAL_STATE, action) => {
	return { ...state, showAlertForgotPassword: action.val };
}

export const HANDLERS = {
	[Types.LOGGED_IN_USER]: loggedInUser,
	[Types.LOGGED_OUT_USER]: loggedOutUser,
	[REHYDRATE]: autoLogin, // special listener, Remove it later
	[Types.SHOW_ALERT_FORGOT_PASSWORD]: showAlertForgotPassword,
}

module.exports = createReducer(INITIAL_STATE, HANDLERS);