import { createReducer } from 'reduxsauce'
import { NavigationActions } from 'react-navigation';
import { REHYDRATE } from 'redux-persist/constants'

import Server from 'Server';

const {Types} = require('../action/navigation'); 

export const INITIAL_STATE = { 
	main: null,
	sub: null 
}

export const replace = (state = INITIAL_STATE, action)=>{
	state.main.dispatch(NavigationActions.reset({
		index: 0,
		actions: [NavigationActions.navigate({ routeName: action.name })]
	}));

	return state;
}

export const replaceRehydrate = (state = INITIAL_STATE, action) => {
	let user = action.payload && action.payload.auth && action.payload.auth.user;
	let projectDetail = action.payload && action.payload.project && action.payload.project.projectDetail;

	if (user && projectDetail){
		// only go to main page when the user is logged in and he/she has selected a project
		Server.autoLogin(user);
		return replace(state, {name:'Main'});
	}	
	else			return replace(state, {name:'Login'});
}

export const setMainNavigation = (state = INITIAL_STATE, action) => {
	if (state.main)
		return state;
	else
  	return { ...state, main: action.navigation }
}

export const setSubNavigation = (state = INITIAL_STATE, action) => {
  if (state.sub)
		return state;
	else
  	return { ...state, sub: action.navigation }
}


export const HANDLERS = {
	[REHYDRATE]: replaceRehydrate,
  [Types.REPLACE]: replace,	
  [Types.SET_MAIN_NAVIGATION]: setMainNavigation,
  [Types.SET_SUB_NAVIGATION]: setSubNavigation,
}

module.exports = createReducer(INITIAL_STATE, HANDLERS);