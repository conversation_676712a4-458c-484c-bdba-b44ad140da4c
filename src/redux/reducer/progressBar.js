import { createReducer } from 'reduxsauce'

const { Types } = require('../action/progressBar');

export const INITIAL_STATE = {
	successCount:0,
	step:0,
	total:0,
	error:0,
	cancelable: null,
	isDownload: false
}

export const progress = (state = INITIAL_STATE, action) => {
	if (state.cancelable !== action.cancelable)
		return state; // not match, ignore this action
		
	if (state.cancelable.canceled)
		return state; // canceled before, ignore this action
	return { ...state, step: action.step, error: action.isError? state.error+1 : state.error, successCount: action.successCount};
}

export const reset = (state = INITIAL_STATE, action) => {
	return { ...INITIAL_STATE, total: action.total || 0, cancelable: action.cancelable, isDownload: !!action.isDownload };
}

export const cancel = (state = INITIAL_STATE, action) => {
	// don't create a new object, this is exception case
	state.cancelable.canceled = true; 
	return {...state, total:0, step:0, error: 0};
}

export const HANDLERS = {
	[Types.RESET]: reset,	
	[Types.CANCEL]: cancel,		
	[Types.PROGRESS]: progress,
}

module.exports = createReducer(INITIAL_STATE, HANDLERS);