import { createReducer } from 'reduxsauce'

const { Types } = require('../action/setting');

export const INITIAL_STATE = {
	language: "zh_hk",
	cameraOpenned: false,
	connectionInfo: null,
	areacodeLabels: ["852", "86"],
	areacodeValues: ["852", "86"]
}

export const changeLanguage = (state = INITIAL_STATE, action) => {
	return { ...state, language: action.language };
}

export const cameraStatus = (state = INITIAL_STATE, action) => {
	return { ...state, cameraOpenned: action.data };
}

export const networkInfoUpdate = (state = INITIAL_STATE, action) => {
	return { ...state, connectionInfo: action.info };
}

export const setAreacodeData = (state = INITIAL_STATE, action) => {
	return { ...state, areacodeLabels: action.labels, areacodeValues: action.values };
}

export const HANDLERS = {
	[Types.CHANGE_LANGUAGE]: changeLanguage,
	[Types.CAMERA_STATUS]: cameraStatus,
	[Types.NETWORK_INFO_UPDATE]: networkInfoUpdate,
	[Types.SET_AREACODE_DATA]: setAreacodeData
}

module.exports = createReducer(INITIAL_STATE, HANDLERS);