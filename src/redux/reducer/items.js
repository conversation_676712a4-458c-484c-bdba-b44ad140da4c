import { createReducer } from 'reduxsauce'

const { Types } = require('../action/items');
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';

export const INITIAL_STATE = {
	items: [],
	modifyItem: "",
	item: {},
    pendingItems: [],
    filterData: [],
    unitItems: [],
    itemsWithHistory: [],
    filterDataWithHistory: []
}

export const fetchedItems = (state = INITIAL_STATE, action) => {
    const isResetNew = action.isResetNew;
    let newItems = [...action.items];
	newItems.map( item => {
		if (typeof item.selected == "undefined") {
			item.selected = false;
		}
	});

    let items = isResetNew? state.items.filter(item => item.status_history) : state.items;
    // let items = isResetNew? state.items.filter(item => item.status_history && item.isDefect) : state.items;
    items = items.map( item => {
		let newItem = newItems.find(c => c._id === item._id);
		if (newItem){
			// newItems = newItems.filter(c => c._id !== newItem._id);
			let itemIndex = newItems.indexOf(newItem);
			if (itemIndex > -1) {
				newItems.splice(itemIndex, 1);
			} else {
				console.log("failed!");
			}
			if (item.status_history)
				return item;
			else
				return newItem;
		}else
			return item;
    })
    
    let joinedItems = items.concat(newItems);
    let defectItems = joinedItems.filter(item => item.status_history);
    // let defectItems = joinedItems.filter(item => item.status_history && item.isDefect);
    
	return { ...state, items: joinedItems, filterData: joinedItems, itemsWithHistory: defectItems, filterDataWithHistory: defectItems };
}

export const fetchLocalUnitItems = (state = INITIAL_STATE, action) => {
    // console.log(action.items);
	return { ...state, unitItems: action.items };
}

// export const loadedChecklists = (state = INITIAL_STATE, action) => {
// 	return { ...state, checklists: [...action.checklists] };
// }

export const seletedItems = (state = INITIAL_STATE, action) => {

	let newItem = state.items.map (item => {
		action.items.map (updateItem =>{
			if(item._id == updateItem._id) {
				item.selected = updateItem.selected
			}
		})
		return item;
	})

	return { ...state, items: [...newItem] };
}

export const modifyItem = (state = INITIAL_STATE, action) => {
	let sectionData;
	// state.items.forEach((c) =>{
	// 	if(c._id == action.id){
	// 		sectionData = c;
	// 	}
	// });
	sectionData = state.items.find((c) => {
		return c._id == action.id;
	});

	return { ...state, modifyCheckList: action.id,  item: sectionData };
}


export const filterItem = (state = INITIAL_STATE, action) => {
    // let defectItems = action.data.filter(item => item.status_history && item.isDefect);
    let defectItems = action.data.filter(item => item.status_history);
	return { ...state, filterData: action.data, filterDataWithHistory: defectItems };
}


export const fakeUpdateItemData = (state = INITIAL_STATE, action) => {
    // let defectItems = action.data.filter(item => item.status_history && item.isDefect);
    let defectItems = action.data.filter(item => item.status_history);
	return { ...state, filterData: action.data, filterDataWithHistory: defectItems };	
}

export const updateItemImages = (state = INITIAL_STATE, action) => {
	let itemId = action.id;
    let imagePaths = action.paths;
    let items = state.items.slice();
    let filterData = state.filterData;

    // items.map((item, i)=>{
    //     if(item._id == itemId){
    //         item.images = imagePaths;
    //     }
    //     return items;
    // })
    let editItem = items.find((item) => {
        return item._id == itemId;
    });
    if (editItem) {
        editItem.images = imagePaths;
    }

    // filterData.map((item, i)=>{
    //     if(item._id == itemId){
    //         item.images = imagePaths;
    //     }
    //     return items;
    // })
    let filterItem = filterData.find((item) => {
        return item._id == itemId;
    });
    if (filterItem) {
        filterItem.images = imagePaths;
    }

	LocalDB.save('Item',items);

	return { ...state, items: items, filterData: filterData };	
}

export const updateSelectOptions = (state = INITIAL_STATE, action) => {
	
	let selectedData = action.data;
    let option = action.option;
    let items = state.items.slice();
    let filterData = state.filterData.slice();
    let filterDataWithHistory = state.filterDataWithHistory.slice();

    items.forEach((item, i)=>{
        if(selectedData.indexOf(item._id) > -1){
            item.status = option;
        }
    });

    filterData.forEach((item, i)=>{
        if(selectedData.indexOf(item._id) > -1){
            item.status = option;
        }
    });

    filterDataWithHistory.forEach((item, i)=>{
        if(selectedData.indexOf(item._id) > -1){
            item.status = option;
        }
    });

	LocalDB.save('Item',items);

	return { ...state, items: items, filterData: filterData, filterDataWithHistory: filterDataWithHistory };	
}

export const addMessage = (state = INITIAL_STATE, action) => {
    let id = action.data._id;
    let messages = action.data.messages;
    let items = state.items;
    let item = state.item;
    let filterData = state.filterData;

    // items.map((item)=>{
    //     if(item._id == id) {
    //         item.messages = messages;
    //     }
    //     return item
    // })
    let editItem = items.find((item) => {
        return item._id == id;
    });
    if (editItem) {
        editItem.messages = messages;
    }
    // filterData.map((item)=>{
    //     if(item._id == id) {
    //         item.messages = messages;
    //     }
    //     return item
    // })
    let filterItem = filterData.find((item) => {
        return item._id == id;
    });
    if (filterItem) {
        filterItem.messages = messages;
    }

    item.messages = messages;
    
    LocalDB.save('Item',item);

	return { ...state, items: items, item: item, filterData:filterData };	
}

export const addOfflineMessage = (state = INITIAL_STATE, action) => {
    let id = action._id;
    let messages = action.message;
    let items = state.items;
    let item = state.item;
    let filterData = state.filterData;
    messages.offline = true;
    let editItem = items.find((item) => {
        return item._id == id;
    });
    if (editItem) {
        editItem.messages.unshift(messages);
    }
    let filterItem = items.find((item) => {
        return item._id == id;
    });
    if (filterItem) {
        filterItem.messages = messages;
    }

    item.messages.unshift(messages); 
    
    LocalDB.save('Item',item);

	return { ...state, items: items, item: item, filterData:filterData };	
}


export const pendingItems = (state = INITIAL_STATE, action) => {
    
    action.item.saved_at = new Date(); // only used in app locally

    let newArray = state.pendingItems.slice();

    var found = false;
    found = newArray.some((item) => {
        return item._id == action.item._id;
    });

    // Add the item
    if (!found){
        newArray.push(action.item);
    }

    let newItems = [];
    let defectItems = [];
    state.items.forEach((item)=>{
        if(item._id != action.item._id) {
            newItems.push(item);
            // if (item.status_history && item.isDefect) {
            if (item.status_history) {
                defectItems.push(item);
            }
        }
    })
    
    let newFilterData = []; 
    let defectFilterItems = [];
    state.filterData.forEach((item)=>{
        if(item._id  != action.item._id) {
            newFilterData.push(item);
            // if (item.status_history && item.isDefect) {
            if (item.status_history) {
                defectFilterItems.push(item);
            }
        }
    })

    // Revised code to remove item only when PendingItem was successfully removed
    LocalDB.save('PendingItem', action.item ).then(()=> {
        LocalDB.delete('Item', action.item._id ).catch(() => {
            alert('Unable to convert Item to PendingItem in local storage.');
        });
    }).catch(() => {
        alert('Unable to remove Item in local storage.');
    });
    return { ...state, pendingItems: newArray, items: newItems, filterData: newFilterData, itemsWithHistory: defectItems, filterDataWithHistory: defectFilterItems};	
}

export const selectItemsForPending = (state = INITIAL_STATE, action) => {

    let items  = state.items.slice();
    let filterData  = state.filterData.slice();
    let itemsWithHistory  = state.itemsWithHistory.slice();
    let filterDataWithHistory  = state.filterDataWithHistory.slice();
    let pendingItems = state.pendingItems.slice();

    let selectedData = action.data;

    items.forEach((item)=> {
        if(selectedData.includes(item._id)){
            pendingItems.push(item);
        }
    })

    items = items.filter((item)=>{
        return !selectedData.includes(item._id)
    })

    itemsWithHistory = itemsWithHistory.filter((item)=>{
        return !selectedData.includes(item._id)
    })

    filterData = filterData.filter((item)=>{
        return !selectedData.includes(item._id)
    })

    filterDataWithHistory = filterDataWithHistory.filter((item)=>{
        return !selectedData.includes(item._id)
    })


	return { ...state, pendingItems: pendingItems, items: items, filterData: filterData, itemsWithHistory: itemsWithHistory, filterDataWithHistory: filterDataWithHistory};	
}

export const fetchedPendingItems = (state = INITIAL_STATE, action) => {

	return { ...state, pendingItems: action.data };
}


export const removePendingItems = (state = INITIAL_STATE, action) => {

    let pendingItems = state.pendingItems.slice();
    
    let submittedData = action.data;

    let filterSubmittedData = [];

    pendingItems.forEach((pendingItem)=> {
        submittedData.forEach((data) => {
            if(pendingItem._id  == data._id) {
                filterSubmittedData.push(data._id)
            }
        })
    })

	pendingItems = pendingItems.filter((pendingItem)=> {

        if(!filterSubmittedData.includes(pendingItem._id)){
            return pendingItem;
        }else {
            // LocalDB.delete('PendingItem', pendingItem._id );
        }
        
	});

	return { ...state, pendingItems: pendingItems  };
}

export const cleanItemData = (state = INITIAL_STATE, action) => {
    
    return { ...state, itemsWithHistory: [] };
}


export const HANDLERS = {
    [Types.FETCHED]: fetchedItems,
    [Types.FETCH_LOCAL_UNIT_ITEMS]: fetchLocalUnitItems,
	// [Types.LOADED]: loadedChecklists,
	[Types.SELECTED]: seletedItems,
	[Types.MODIFY_ITEM]: modifyItem,
	[Types.FILTER_ITEM]: filterItem,
	[Types.FAKE_UPDATE_ITEM_DATA]: fakeUpdateItemData,
    [Types.UPDATE_SELECT_OPTIONS]: updateSelectOptions,
	[Types.UPDATE_ITEM_IMAGES]: updateItemImages,
    [Types.ADD_MESSAGE]: addMessage,
    [Types.ADD_OFFLINE_MESSAGE]: addOfflineMessage,
	// [Types.UPDATE_LABEL_DATA]: updateLabelData,
	// [Types.RE_CAL_LABEL_DATA]: reCalLabelData,
	// [Types.UPDATE_OTHER_TEXT]: updateOtherText,
    [Types.PENDING_ITEMS]: pendingItems,
    [Types.SELECT_ITEMS_FOR_PENDING]: selectItemsForPending,
	[Types.FETCHED_PENDING_ITEMS]: fetchedPendingItems,
    [Types.REMOVE_PENDING_ITEMS]: removePendingItems,
    [Types.CLEAN_ITEM_DATA]: cleanItemData,
}

module.exports = createReducer(INITIAL_STATE, HANDLERS);