import { createReducer } from 'reduxsauce'
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';

const { Types } = require('../action/units');

export const INITIAL_STATE = {
	units: [],
	modifyUnit: "",
	unit: {},
	filterData: [],
	pendingUnits: [],
	qrcodeData: "",
    showKeyForm: false,
}

export const fetchedUnits = (state = INITIAL_STATE, action) => {
    const isResetNew = action.isResetNew;
	let newUnits = [...action.units];
	newUnits.forEach( unit => {
		if (typeof unit.selected == "undefined") {
			unit.selected = false;
		}
		// return unit;
	});
    let units = isResetNew? state.units.filter(unit => (unit.checklists)) : state.units;
	units = units.map( unit => {
		let newUnit = newUnits.find(c => c._id === unit._id);
		if (newUnit){
			newUnits = newUnits.filter(c => c._id !== newUnit._id);
			if (unit.checklists)
				return unit;
			else
				return newUnit;
		}else
			return unit;
	})
	return { ...state, units: units.concat(newUnits), filterData: units.concat(newUnits) };
}

export const seletedUnits = (state = INITIAL_STATE, action) => {
	
	let newItem = state.units.map(unit => {
		action.units.forEach(updateItem =>{
			if(unit._id == updateItem._id) {
				unit.selected = updateItem.selected
			}
		})
		return unit;
	})

	return { ...state, units: [...newItem] };
}

export const modifyUnit = (state = INITIAL_STATE, action) => {
	let sectionData;
	// state.units.forEach((c) =>{
	// 	if(c._id == action.id){
	// 		sectionData = c;
	// 	}
	// });
	sectionData = state.units.find((c) => {
		return c._id == action.id;
	});
	// console.log(sectionData);
	// console.log(action.id);
	return { ...state, modifyUnit: action.id,  unit: sectionData };
}


export const updateSelectOptions = (state = INITIAL_STATE, action) => {
	
	let selectedData = action.data;
    let option = action.option;
	let units = state.units.slice();
	let unit = Object.assign({}, state.unit);

    units.forEach((item, i)=>{
        if(selectedData.indexOf(item._id) > -1){
            item.status_int = option;
        }
        // return item;
    })

 
	unit.status_int = option;
	LocalDB.save('Unit',unit);

	return { ...state, units: units, unit: unit };	
}


export const updateCheckboxes = (state = INITIAL_STATE, action) => {
	
	let units = state.units.slice();
	let unit = Object.assign({}, state.unit);
	let id = action.id;

 
	unit.checkboxes[action.key] = action.value;


	units.forEach((item, i)=>{
        if(id.indexOf(item._id) > -1){
			item = unit;
        }
        // return item;
    })

	LocalDB.save('Unit',unit);

	return { ...state, units: units, unit: unit };	
}

export const filterUnit = (state = INITIAL_STATE, action) => {
	return { ...state, filterData: action.data };
}

export const fakeUpdateUnitData = (state = INITIAL_STATE, action) => {
	
	return { ...state, filterData: action.data };	
}

export const pendingUnits = (state = INITIAL_STATE, action) => {

	action.unit.saved_at = new Date(); // only used in app locally
	
	let newArray = state.pendingUnits.slice();

	var found = false;
	var i = 0;
	while (i < newArray.length) {
		if (newArray[i].id === action.unit._id) {
			// Do the logic (delete or replace)
			found = true;
			break;
		}
		i++;
	}

	// Add the unit
	if (!found){
		newArray.push(action.unit);
	}

	let newUnits = [];

	state.units.forEach((unit)=>{
		if(unit._id != action.unit._id) {
			newUnits.push(unit);
		}
	})
	
	let newFilterData = []; 
	
	state.filterData.forEach((unit)=>{
		if(unit._id  != action.unit._id) {
			newFilterData.push(unit);
		}
	})

	LocalDB.save('PendingUnit', action.unit );
	LocalDB.delete('Unit', action.unit._id );
	return { ...state, pendingUnits: newArray, units: newUnits, filterData: newFilterData};	
}


export const fetchedPendingUnits = (state = INITIAL_STATE, action) => {
	return { ...state, pendingUnits: action.data };
}

export const removePendingUnits = (state = INITIAL_STATE, action) => {

	// let ids = [];
	// if(state.pendingUnits.length > 0){
	// 	ids = state.pendingUnits.map((pendingUnit)=>{
	// 		return pendingUnit._id
	// 	});
	// }

	// let units = state.units.slice();

	// units = units.map((unit)=>{
	// 	if(!ids.includes(unit._id)){
	// 		return unit
	// 	}else {
	// 		LocalDB.delete('PendingUnit', unit._id );
	// 		LocalDB.delete('Unit', unit._id );
	// 	}
	// })

	// units = units.filter(function( element ) {
	// 	return element !== undefined;
	// });
	
	let pendingUnits = state.pendingUnits.slice();
    
    let submittedData = action.data;

    let filterSubmittedData = [];

    pendingUnits.forEach((pendingUnit)=> {
        submittedData.forEach((data) => {
            if(pendingUnit._id  == data._id) {
                filterSubmittedData.push(data._id)
            }
        })
    })

	pendingUnits = pendingUnits.filter((pendingUnit)=> {

        if(!filterSubmittedData.includes(pendingUnit._id)){
            return pendingUnit;
        }else {
           	LocalDB.delete('PendingUnit', pendingUnit._id );
        }
        
	});
	

	return { ...state, pendingUnits: pendingUnits };
}

export const updateQRcode = (state = INITIAL_STATE, action) => {
	
	return { ...state, qrcodeData: action.data, showKeyForm: action.showKeyForm };
}

export const updateUnitKey = (state = INITIAL_STATE, action) => {
	
	let units = state.units.slice();
	let unit = Object.assign({}, state.unit);

 
	unit.unit_key.key_status = action.data.key_status;
	unit.unit_key.unit_status = action.data.unit_status;
	unit.unit_key.current_owner = action.data.current_owner;
	unit.unit_key.dialing_code = action.data.dialing_code;
	unit.unit_key.mobile = action.data.mobile;
	unit.unit_key.reason = action.data.reason;
	unit.unit_key.remark = action.data.remark;
	unit.unit_key.hasUpdated = true;

	units.forEach((item, i)=>{
        if(item._id == unit._id){
			item = unit;
        }
        // return item;
    })

	LocalDB.save('Unit',unit);

	return { ...state, units: units, unit: unit };	
}

export const resetNewUnitset = (state = INITIAL_STATE, action) => {
    let resetUnits = state.units.filter(unit => (unit.checklists));
    return { ...state, filterData: resetUnits };
}

export const fetchLocalItemStatusSummary = (state = INITIAL_STATE, action) => {
    return { ...state, itemStatusSummary: action.data };
}

export const fetchLocalChecklistSummary = (state = INITIAL_STATE, action) => {
    return { ...state, checklistSummary: action.data };
}

export const HANDLERS = {
	[Types.FETCHED]: fetchedUnits,
	[Types.SELECTED]: seletedUnits,
	[Types.MODIFY_UNIT]: modifyUnit,
	[Types.UPDATE_SELECT_OPTIONS]: updateSelectOptions,
	[Types.UPDATE_CHECKBOXES]:updateCheckboxes,
	[Types.FILTER_UNIT]: filterUnit,
	[Types.FAKE_UPDATE_UNIT_DATA]: fakeUpdateUnitData,
	[Types.PENDING_UNITS]: pendingUnits,
	[Types.FETCHED_PENDING_UNITS]: fetchedPendingUnits,
	[Types.REMOVE_PENDING_UNITS]: removePendingUnits,
	[Types.UPDATE_Q_R_CODE]: updateQRcode,
	[Types.UPDATE_UNIT_KEY]: updateUnitKey,
    [Types.RESET_NEW_UNITSET]: resetNewUnitset,
    [Types.FETCH_LOCAL_ITEM_STATUS_SUMMARY]: fetchLocalItemStatusSummary,
    [Types.FETCH_LOCAL_CHECKLIST_SUMMARY]: fetchLocalChecklistSummary
}

module.exports = createReducer(INITIAL_STATE, HANDLERS);