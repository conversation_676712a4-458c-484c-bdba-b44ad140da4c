import { createReducer } from 'reduxsauce'
import { REHYDRATE } from 'redux-persist/constants'
import Server from 'Server'
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import moment from 'moment';

const { Types } = require('../action/project');

export const INITIAL_STATE = {
    projectLists: null,
    selectedProject: null,
    projectDetail: null,
    checklistsCount: 0,
    unitsCount: 0,
    itemsCount: 0,
    pendingCount: 0,
    unitsInfo: [],
    vipUnits: [],
	adhoc_options: {
		tower: [],
		floor: [],
		flat: [],
		location: [],
		level1: [],
        level2: [],
        isInspection: true
    },
    unitsTree: {},
    adhocTreeInspec: {},
    adhocTreeSpec: {},
    adhoc_current_unit: null,
    qrcodeData: {},
    showKeyForm: false,
    pendingKey: [],
    handoverForm: [],
    floor_plans: [],
    filteredOptions: {
		floor: [],
		flat: [],
    }
}

const itemPushfunction = (arr, pushItem) => {
    if(pushItem){
        let found = false;
        for (var i = 0, len = arr.length; i < len; i++) {
            if (arr[i] === pushItem) {
                found = true;
                break;
            }
        }
        
        if (!found)
            arr.push(pushItem);
    }
    return arr;
}

export const fetchProjectLists = (state = INITIAL_STATE, action) => {
	return { ...state, projectLists: [...action.projectLists] };
}

export const updateSelectedProject = (state = INITIAL_STATE, action) => {
	return { ...state, selectedProject: {...action.selectedProject} };
}

export const fetchProjectDetail = (state = INITIAL_STATE, action) => {
    let adhocOptions = action.projectDetail.adhoc_options;
    let unitsInfo = state.unitsInfo;
    // let adhoc_options = state.adhoc_options;

    /* option 2: set adhoc options separately */
    let adhocTreeInspec = state.adhocTreeInspec;
    let adhocTreeSpec = state.adhocTreeSpec;
    let inspectionOpts = {};
    let specialOpts = {};
    for (var i = 0, len = adhocOptions.length; i < len; i++) {
        let option = adhocOptions[i];
        // if (option.name && option.name.toLowerCase() == "inspection") {
            if (option.location && !(option.location in inspectionOpts)) {
                inspectionOpts[option.location] = {};
            }

            if (option.level1 && !(option.level1 in inspectionOpts[option.location])) {
                inspectionOpts[option.location][option.level1] = {};
            }

            if (option.level2 && !(option.level2 in inspectionOpts[option.location][option.level1])) {
                inspectionOpts[option.location][option.level1][option.level2] = {};
            }
        // } else {
            if (option.location && !(option.location in specialOpts)) {
                specialOpts[option.location] = {};
            }

            if (option.level1 && !(option.level1 in specialOpts[option.location])) {
                specialOpts[option.location][option.level1] = {};
            }

            if (option.level2 && !(option.level2 in specialOpts[option.location][option.level1])) {
                specialOpts[option.location][option.level1][option.level2] = {};
            }
        // }
    }
    adhocTreeSpec = specialOpts;
    adhocTreeInspec = inspectionOpts

    let unitsTree = {};
    for (var i = 0, len = unitsInfo.length; i < len; i++) {
        let info = unitsInfo[i];

        // create the tree object for units
        if (info.tower && !(info.tower in unitsTree)) {
            unitsTree[info.tower] = {};
        }
        if (info.floor && !(info.floor in unitsTree[info.tower])) {
            unitsTree[info.tower][info.floor] = {};
        }
        if (info.flat && !(info.flat in unitsTree[info.tower][info.floor])) {
            unitsTree[info.tower][info.floor][info.flat] = {};
        }

        /* option 1: set adhoc options in unit info */
        // let options = adhocOptions.filter((opt) => {
        //     return opt.tower == info.tower && opt.floor == info.floor && opt.flat == info.flat;
        // });
        // if (options.length > 0) {
        //     let inspectionOpts = {};
        //     let specialOpts = {};
        //     for (var j = 0, l = options.length; j < l; j++) {
        //         let option = options[j];
        //         if (option.name && option.name.toLowerCase() == "inspection") {
        //             // inspectionOpts.location = itemPushfunction(inspectionOpts.location, option.location);
        //             // inspectionOpts.level1 = itemPushfunction(inspectionOpts.level1, option.level1);
        //             // inspectionOpts.level2 = itemPushfunction(inspectionOpts.level2, option.level2);
        //             if (option.location && !(option.location in inspectionOpts)) {
        //                 inspectionOpts[option.location] = {};
        //             }
    
        //             if (option.level1 && !(option.level1 in inspectionOpts[option.location])) {
        //                 inspectionOpts[option.location][option.level1] = {};
        //             }
    
        //             if (option.level2 && !(option.level2 in inspectionOpts[option.location][option.level1])) {
        //                 inspectionOpts[option.location][option.level1][option.level2] = {};
        //             }
        //         } else {
        //             // specialOpts.location = itemPushfunction(specialOpts.location, option.location);
        //             // specialOpts.level1 = itemPushfunction(specialOpts.level1, option.level1);
        //             // specialOpts.level2 = itemPushfunction(specialOpts.level2, option.level2);
        //             if (option.location && !(option.location in specialOpts)) {
        //                 specialOpts[option.location] = {};
        //             }
    
        //             if (option.level1 && !(option.level1 in specialOpts[option.location])) {
        //                 specialOpts[option.location][option.level1] = {};
        //             }
    
        //             if (option.level2 && !(option.level2 in specialOpts[option.location][option.level1])) {
        //                 specialOpts[option.location][option.level1][option.level2] = {};
        //             }
        //         }
        //     }
        //     let unitOptions = { inspectionOpts, specialOpts };
        //     info.unitOptions = unitOptions;
        // }
    }
    delete action.projectDetail.adhoc_options;

	return { ...state, projectDetail: {...action.projectDetail}, unitsInfo: unitsInfo, adhocTreeSpec: specialOpts, adhocTreeInspec: inspectionOpts, unitsTree: unitsTree };
}

export const getAdHocFilter = (state = INITIAL_STATE, action) => {

	let { tower, floor, flat, location, level1, level2, isInspection } = action.adhoc_options;
	
    // let options = Object.assign({}, state.adhoc_options);
    // let filteredOptions = Object.assign({}, state.filteredOptions);

    // if (tower == "") tower = null;
    // if (floor == "") floor = null;
    // if (flat == "") flat = null;

    // better reseting the filter options when the selection is changed or no selection
    // let originalUnit = state.adhoc_current_unit;
    // if (!originalUnit || originalUnit.tower != tower) {
    //     if(tower && !floor && !flat) {
    //         filteredOptions.floor = [];
    //         filteredOptions.flat = [];
    //     }
    // }
    // if (!originalUnit || originalUnit && originalUnit.floor != floor) {
    //     if(tower && floor && !flat) {
    //         filteredOptions.flat = [];
    //     }
    // }

    let unitInfo = state.unitsInfo.find((info) => {
        // if(tower && info.tower == tower && info.floor != null) {
        //     if (filteredOptions.floor.indexOf(info.floor) == -1)
        //         filteredOptions.floor.push(info.floor);
        // }

        // if(floor && info.floor == floor && info.tower == tower && info.flat != null) {
        //     if (filteredOptions.flat.indexOf(info.flat) == -1)
        //         filteredOptions.flat.push(info.flat);
        // }

        // enhanced the check case
        let match = false;
        if (info.tower) {
            match = info.tower == tower;
        }
        if (match) 
            if (info.floor) {
                match = info.floor == floor;
            }
        if (match) 
            if (info.flat) {
                match = info.flat == flat;
            }
        return match;
    });

    // filteredOptions.floor = filteredOptions.floor.filter(function(item, pos) {
    //     return filteredOptions.floor.indexOf(item) == pos;
    // })
    // if (tower) {
    //     filteredOptions.floor = Object.keys(state.unitsTree[tower]);
    // }

    // // filteredOptions.flat = filteredOptions.flat.filter(function(item, pos) {
    // //     return filteredOptions.flat.indexOf(item) == pos;
    // // })
    // if (tower && floor) {
    //     filteredOptions.flat = Object.keys(state.unitsTree[tower][floor]);
    // }

    // if (unitInfo) {
    //     // reset the filter options if the selected unit has no floor (and flat)
    //     if (!unitInfo.floor || unitInfo.floor == "") {
    //         filteredOptions.floor = [];
    //         filteredOptions.flat = [];
    //     }

    //     if (unitInfo.unitOptions) {
    //         let source = (isInspection) ? unitInfo.unitOptions.inspectionOpts : unitInfo.unitOptions.specialOpts;
    //         if (source) {
    //             options.location = Object.keys(source);
    //             options.level1 = [];
    //             options.level2 = [];
    
    //             if (location) {
    //                 let locationObj = source[location];
    //                 if (locationObj && Object.keys(locationObj).length > 0) {
    //                     options.level1 = Object.keys(locationObj);
    //                 }
    //                 if (!(level1 in locationObj)) {
    //                     level1 = null;
    //                     level2 = null;
    //                 }
    //             }
    //             if (level1) {
    //                 let level1Obj = source[location][level1];
    //                 if (level1Obj && Object.keys(level1Obj).length > 0) {
    //                     options.level2 = Object.keys(level1Obj);
    //                 }
    //             }
    //         }
    //     } else {
    //         options.location = [];
    //         options.level1 = [];
    //         options.level2 = [];
    //     }
    // } else {
    //     options.location = [];
    //     options.level1 = [];
    //     options.level2 = [];
    // }
    if (state.adhoc_current_unit != unitInfo) {
        return {...state, adhoc_current_unit: unitInfo};
    } else {
        return state;
    }

	// return { ...state, adhoc_options: options, adhoc_current_unit: unitInfo, filteredOptions: filteredOptions };
}

export const updateChecklistsCount = (state = INITIAL_STATE, action) => {
	return { ...state, checklistsCount: action.checklistsCount };
}

export const updateUnitsCount = (state = INITIAL_STATE, action) => {
	return { ...state, unitsCount: action.unitsCount };
}

export const updateItemsCount = (state = INITIAL_STATE, action) => {
	return { ...state, itemsCount: action.itemsCount };
}

export const fetchUnitsInfo = (state = INITIAL_STATE, action) => {
    let vipUnits = action.unitsInfo.filter((info) => {
        return info.vip;
    })
    return { ...state, unitsInfo: action.unitsInfo, vipUnits: vipUnits };
}

export const updatePendingCount = (state = INITIAL_STATE, action) => {
    if (typeof action.pendingCount == 'string') {
        let newCount = state.pendingCount;
        if (action.pendingCount.indexOf('+') > -1) {
            let count = action.pendingCount.slice(1);
            newCount += parseInt(count);
        } else if (action.pendingCount.indexOf('-') > -1) {
            let count = action.pendingCount.slice(1);
            newCount -= parseInt(count);
        }
        return { ...state, pendingCount: newCount };
    } else {
        return { ...state, pendingCount: action.pendingCount };
    }
}

export const updateKeyQRCode = (state = INITIAL_STATE, action) => {
    let qrcodeData = action.data;
    let localDate = new Date();
	let randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
    let uniqId = "_Q" + randLetter + Date.now();

    qrcodeData._id = uniqId;
    qrcodeData.copyId = uniqId;

	return { ...state, qrcodeData: qrcodeData, showKeyForm: action.showKeyForm };
}

export const updateKey = (state = INITIAL_STATE, action) => {
	let pendingKey = state.pendingKey.slice();
	let qrcodeData = Object.assign({}, state.qrcodeData);
    let localDate = new Date();
    qrcodeData.unit = action.data.unit;
    qrcodeData.type = action.data.type;
	qrcodeData.key_status = action.data.key_status;
	qrcodeData.unit_status = action.data.unit_status;
	qrcodeData.current_owner = action.data.current_owner;
	qrcodeData.dialing_code = action.data.dialing_code;
	qrcodeData.mobile = action.data.mobile;
	qrcodeData.reason = action.data.reason;
	qrcodeData.remark = action.data.remark;
    qrcodeData._created_at = moment.utc( localDate ).format();

    qrcodeData.saved_at = localDate;    // only used in app locally

    pendingKey.push(qrcodeData);

    LocalDB.save('QrcodeData',qrcodeData);

	return { ...state, qrcodeData: {}, pendingKey: pendingKey };	
}

export const fetchedKey = (state = INITIAL_STATE, action) => {

	return { ...state, pendingKey: action.data };
}

export const removePendingKey = (state = INITIAL_STATE, action) => {

    let pendingKey = state.pendingKey.slice();
    console.log("pendingKey", pendingKey);
    
    let submittedData = action.data;

    let filterSubmittedData = [];

    pendingKey.forEach((key)=> {
        submittedData.forEach((data) => {
            if(key.originalId  == data.originalId) {
                filterSubmittedData.push(data.originalId)
            }
        })
    })

	pendingKey = pendingKey.filter((key)=> {

        if(!filterSubmittedData.includes(key.originalId)){
            return key;
        } else {
            // LocalDB.delete('QrcodeData', key.copyId );
        }
        
    });
    
    console.log("pendingKey", pendingKey)

	return { ...state, pendingKey: pendingKey };
}

export const fetchHandoverForm = (state = INITIAL_STATE, action) => {
    
    return { ...state, handoverForm: action.data };
}

export const getUnitFloorPlan = (state = INITIAL_STATE, action) => {
    
    let unitId = action.unitId;
    let floor_plans = [];

    if (unitId) {
        let unit = state.unitsInfo.find((u) => {
            return u._id == unitId;
        });
        if (unit) {
            floor_plans = unit.floor_plans;
        }
    }

    return { ...state, floor_plans: floor_plans };
}

export const fetchChecklistList = (state = INITIAL_STATE, action) => {
    let projectDetail = state.projectDetail;
    let dataArray = [];

    action.data.forEach((item)=> {
        dataArray.push(item.name);
    })

    dataArray = dataArray.filter(function(item, pos) {
        return dataArray.indexOf(item) == pos;
    })

    projectDetail.checklistList = dataArray;

	return { ...state, projectDetail: projectDetail };
}

export const fetchUsers = (state = INITIAL_STATE, action) => {
    let users = {};
    action.users.forEach((user) => {
        let displayName = "";
        if (user.first_name && user.last_name) {
            displayName = user.first_name + " " + user.last_name;
        } else if (user.first_name) {
            displayName = user.first_name;
        } else if (user.last_name) {
            displayName = user.last_name;
        }
        users[user._id] = displayName;
    });
	return { ...state, users: users };
}

export const HANDLERS = {
    [Types.FETCHED]: fetchedKey,
    [Types.FETCH_PROJECT_LISTS]: fetchProjectLists,
    [Types.UPDATE_SELECTED_PROJECT]: updateSelectedProject,
    [Types.FETCH_PROJECT_DETAIL]: fetchProjectDetail,
    [Types.UPDATE_CHECKLISTS_COUNT]: updateChecklistsCount,
    [Types.UPDATE_UNITS_COUNT]: updateUnitsCount,
    [Types.UPDATE_ITEMS_COUNT]: updateItemsCount,
    [Types.UPDATE_PENDING_COUNT]: updatePendingCount,
    [Types.FETCH_UNITS_INFO]: fetchUnitsInfo,
    [Types.GET_AD_HOC_FILTER]: getAdHocFilter,
    [Types.UPDATE_KEY_Q_R_CODE]: updateKeyQRCode,
    [Types.UPDATE_KEY]: updateKey,
    [Types.REMOVE_PENDING_KEY]: removePendingKey,
    [Types.FETCH_HANDOVER_FORM]: fetchHandoverForm,
    [Types.GET_UNIT_FLOOR_PLAN]: getUnitFloorPlan,
    [Types.FETCH_CHECKLIST_LIST]: fetchChecklistList,
    [Types.FETCH_USERS]: fetchUsers
}

module.exports = createReducer(INITIAL_STATE, HANDLERS);