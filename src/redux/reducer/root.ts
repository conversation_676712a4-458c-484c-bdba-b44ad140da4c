import { createReducer } from 'reduxsauce';
import { Types } from '../action/root';

// Define state interface
export interface RootState {
	someNum?: number;
}

export const INITIAL_STATE: RootState = {
};

// Define action interface
interface RootAction {
	type: string;
	[key: string]: any;
}

export const test = (state: RootState = INITIAL_STATE, _action: RootAction): RootState => {
  return { ...state, someNum: state.someNum ? state.someNum + 1 : 1 };
};

// export const onClick = (state = INITIAL_STATE, action) => {
//   console.log('test onclick :', action.username);
//   return { ...state, username: action.username }
// }

// export const testFetch = (state = INITIAL_STATE, action) => {
//   console.log('testFetch got:', action);
//   return state;
// }

// export const testFetchSuccess = (state = INITIAL_STATE, action) => {
//   return { ...state, someNum: state.someNum + 10 }
// }

// export const testFetchError = (state = INITIAL_STATE, action) => {
//   return { ...state, someNum: -10 }
// }

// export const testCustom = (state = INITIAL_STATE, action) => {
//   console.log('testCustom got:', action);
//   return state;
// }

export const HANDLERS = {
	[Types.ROOT_TEST]: test,
  // [Types.TEST_FETCH]: testFetch,
  // [Types.TEST_FETCH_SUCCESS]: testFetchSuccess,
  // [Types.TEST_FETCH_ERROR]: testFetchError,
  // [Types.TEST_CUSTOM]: testCustom,
  // [Types.ON_CLICK]: onClick,
};

const rootReducer = createReducer(INITIAL_STATE, HANDLERS);
export default rootReducer;