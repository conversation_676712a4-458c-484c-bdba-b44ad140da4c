import { createReducer } from 'reduxsauce'

const { Types } = require('../action/checklists');
// import LocalDB from 'Realm';
// import LocalDB from 'SQLite';
import LocalDB from 'LocalDB';
import moment from 'moment';

export const INITIAL_STATE = {
	checklists: [],
	downloadedChecklists: [],
	readyToDownloadChecklists: [],
	modifyCheckList: "",
	checklist: [],
	sectionData: [],
	unfillData: [],
	satisfyData: [],
	followUpData: [],
	labelData: {
		total: 0,
		Unfill: 0,
		Satisfy: 0,
		FollowUp: 0,
	},
	pendingCheckLists: [],
	imagePaths: {},
	viewUnitCheckList: [],
	formSubmission_options: {
		tower: [],
		floor: [],
		flat: [],
	},
	unitCheckList: [],
	filterData: [],
	filteredChecklist: [],
	filteredChecklistTotal: 0,
}

export const fetchedChecklists = (state = INITIAL_STATE, action) => {
    const isResetNew = action.isResetNew;
    let newChecklists = [...action.checklists];
	// newChecklists.forEach( checklist => {
	// 	if (typeof checklist.selected == "undefined") {
	// 		checklist.selected = false;
	// 	}
	// });

    let checklists = isResetNew? state.checklists.filter(checklist => (checklist.questions)) : state.checklists;
	checklists = checklists.map( checklist => {
		let newChecklist = newChecklists.find(c => c._id === checklist._id);
		if (newChecklist){
			// newChecklists = newChecklists.filter(c => c._id !== newChecklist._id);
			let checklistIndex = newChecklists.indexOf(newChecklist);
			if (checklistIndex > -1) {
				newChecklists.splice(checklistIndex, 1);
			} else {
				console.log("failed!");
			}
			if (checklist.questions && !newChecklist.downloadFromChecklist)
				return checklist;
			else
				return newChecklist;
		}else
			return checklist;
	})

	let joinedChecklists = checklists.concat(newChecklists);
	let downloadedChecklists = [];
	let readyToDownloadChecklists = [];
	joinedChecklists.forEach((c) => {
		if (c.questions && c.downloadFromChecklist) {
			downloadedChecklists.push(c);
		} else {
			readyToDownloadChecklists.push(c);
		}
	});

	return { ...state, checklists: joinedChecklists, filterData: readyToDownloadChecklists.slice()
        , downloadedChecklists: downloadedChecklists, readyToDownloadChecklists: readyToDownloadChecklists};
}

export const loadedChecklists = (state = INITIAL_STATE, action) => {
	return { ...state, checklists: [...action.checklists] };
}

export const seletedChecklists = (state = INITIAL_STATE, action) => {

	let newCheckList = state.checklists.map (checkList => {
		action.checklists.map (updateCheckList =>{
			if(checkList._id == updateCheckList._id) {
				checkList.selected = updateCheckList.selected
			}
		})
		return checkList;
	})

	return { ...state, checklists: [...newCheckList] };
}

export const modifyCheckList = (state = INITIAL_STATE, action) => {
	let sectionData;
	// state.checklists.forEach((c) =>{
	// 	if(c._id == action.id){
	// 		sectionData = c;
	// 	}
	// });
	sectionData = state.checklists.find((c) => {
		return c._id == action.id;
	});
	
	let questions = sectionData.questions;

	let unfillData = [];
	let satisfyData = [];
	let followUpData = [];
	let total = 0;

	for (var i = 0, len = questions.length; i < len; i++) {
		let section = questions[i];
		
		let newSection = {...section};
		let unfillSection = {...section, data: []};
		let satisfySection = {...section, data: []};
		let followUpSection = {...section, data: []};

		newSection.data.forEach((item, i)=>{
			total++;
			if (item.status == 'Unfill') {
				unfillSection.data.push(item);
			} else if (item.status == 'Satisfy') {
				satisfySection.data.push(item);
			} else if (item.status == 'FollowUp') {
				followUpSection.data.push(item);
			}
		});

		if (unfillSection.data.length > 0) {
			unfillData.push(unfillSection);
		}
		if (satisfySection.data.length > 0) {
			satisfyData.push(satisfySection);
		}

		if (followUpSection.data.length > 0) {
			followUpData.push(followUpSection);
		}
	}

	// questions.forEach((section) => {
	// 	let newSection = {...section};
	// 	let unfillSection = {...section, data: []};
	// 	let satisfySection = {...section, data: []};
	// 	let followUpSection = {...section, data: []};

	// 	newSection.data.forEach((item, i)=>{
	// 		if (item.status == 'Unfill') {
	// 			unfillSection.data.push(item);
	// 		} else if (item.status == 'Satisfy') {
	// 			satisfySection.data.push(item);
	// 		} else if (item.status == 'FollowUp') {
	// 			followUpSection.data.push(item);
	// 		}
	// 	});

	// 	if (unfillSection.data.length > 0) {
	// 		unfillData.push(unfillSection);
	// 	}
	// 	if (satisfySection.data.length > 0) {
	// 		satisfyData.push(satisfySection);
	// 	}

	// 	if (followUpSection.data.length > 0) {
	// 		followUpData.push(followUpSection);
	// 	}
	// });

	let checkUpdatedinDetail = action.checkUpdatedinDetail;
	
	if(action.from == 'checklistDetail') {
		checkUpdatedinDetail++;
	}

	return { ...state, modifyCheckList: action.id, checklist: sectionData, sectionData: sectionData.questions, 
		unfillData: unfillData, satisfyData: satisfyData, followUpData: followUpData, filteredChecklist:  sectionData, filteredChecklistTotal: total, checkUpdatedinDetail: checkUpdatedinDetail };
}

export const modifyUnitCheckList = (state = INITIAL_STATE, action) => {

	let sectionData;

	sectionData = action.items.find((c) => {
		return c._id == action.id;
	});

	// console.log("sectionData", sectionData)


	let questions = sectionData.questions;
	// let unfillData = questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Unfill');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	// let satisfyData = questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Satisfy');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	// let followUpData = questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'FollowUp');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	let unfillData = [];
	let satisfyData = [];
	let followUpData = [];
	let total = 0, unfill = 0, satisfy = 0, followUp = 0;;

	questions.forEach((section) => {
		let newSection = {...section};
		let unfillSection = {...section, data: []};
		let satisfySection = {...section, data: []};
		let followUpSection = {...section, data: []};

		newSection.data.forEach((item, i)=>{
			total++;
			if (item.status == 'Unfill') {
				unfill++;
				unfillSection.data.push(item);
			} else if (item.status == 'Satisfy') {
				satisfy++;
				satisfySection.data.push(item);
			} else if (item.status == 'FollowUp') {
				followUp++;
				followUpSection.data.push(item);
			}
		});
		
		if (unfillSection.data.length > 0) {
			unfillData.push(unfillSection);
		}
		if (satisfySection.data.length > 0) {
			satisfyData.push(satisfySection);
		}

		if (followUpSection.data.length > 0) {
			followUpData.push(followUpSection);
		}
	});


	let labelData =  {
		total: unfill + satisfy + followUp,
		Unfill: unfill,
		Satisfy: satisfy,
		FollowUp: followUp,
	};
	// console.log("followUpData", followUpData)
	// console.log("satisfyData", satisfyData)
	// console.log("labelData", labelData);

	let checkUpdatedinDetail = action.checkUpdatedinDetail;
	
	if(action.from == 'checklistDetail') {
		checkUpdatedinDetail++;
	}

	return { ...state, unitCheckList: action.items, modifyCheckList: action.id, checklist: sectionData, sectionData: sectionData.questions, 
		unfillData: unfillData, satisfyData: satisfyData, followUpData: followUpData, labelData: labelData, filteredChecklist:  sectionData, filteredChecklistTotal: total, checkUpdatedinDetail: checkUpdatedinDetail  };
}

export const modifyAdhocCheckList = (state = INITIAL_STATE, action) => {
	let checklist;
	state.downloadedChecklists.forEach((c) =>{
		if(c._id == action.id){
			checklist = c;
		}
	});

	return { ...state, modifyCheckList: action.id, checklist: checklist };
}

export const viewUnitCheckList = (state = INITIAL_STATE, action) => {
	let sectionData = [];
	state.checklists.forEach((c) =>{
		if (typeof c.clickExpanded == "undefined") {
			c.clickExpanded = false;
		}
		if(c.unit == action.id){
			sectionData.push(c);
		}
	});

	return { ...state, viewUnitCheckList: sectionData };
}



export const filterCheckList = (state = INITIAL_STATE, action) => {
	let currentIndex = 1;
	let filteredChecklist = action.filteredChecklist;
	filteredChecklist.questions = filteredChecklist.questions.map((section) => {
		let newSection = {...section};

		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		});

		return newSection;   
	});

	let checkUpdatedinDetail = action.checkUpdatedinDetail;
	
	if(action.from == 'checklistDetail') {
		checkUpdatedinDetail++;
	}
	
	if(action.label == 'Unfill'){
		return { ...state, unfillData: action.data, filteredChecklist: filteredChecklist, filteredChecklistTotal:  currentIndex - 1, checkUpdatedinDetail: checkUpdatedinDetail  };
	}
	if(action.label == 'Satisfy'){
		return { ...state, satisfyData: action.data, filteredChecklist: filteredChecklist, filteredChecklistTotal:  currentIndex - 1, checkUpdatedinDetail: checkUpdatedinDetail  };
	}
	if(action.label == 'FollowUp'){
		return { ...state, followUpData: action.data, filteredChecklist: filteredChecklist, filteredChecklistTotal:  currentIndex - 1, checkUpdatedinDetail: checkUpdatedinDetail  };
	}
	
}


export const fakeUpdateUnfillData = (state = INITIAL_STATE, action) => {

	return { ...state, unfillData: action.data };	
}

export const fakeUpdateAdhocData = (state = INITIAL_STATE, action) => {

	let checklist = state.checklist;
	
	checklist.questions = action.data
	
	return { ...state, checklist: Object.assign({}, checklist) };	
}

export const updateSelectOptions = (state = INITIAL_STATE, action) => {
	
	let selectedData = action.data;
	let label = action.label;

	let sectionData;

	let checklist = state.checklist;
	let filteredChecklist = state.filteredChecklist;

	let currentIndex = 1;

	let unfillData = [];
	let satisfyData = [];
	let followUpData = [];

	checklist.questions = checklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(selectedData.indexOf(newItem._id) > -1){
				newItem.status = label;
				newItem.selectOptions = action.option.slice(0);
				// console.log(newItem);
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
	
			return newItem;
		});
		return newSection;   
	});


	let checklists = state.checklists.map((c) =>{
		if(c._id == action.id){
			c = checklist;
		}
		return c;
	});

	downloadedChecklists = state.downloadedChecklists.map((c) =>{
		if(c._id == action.id){
			c = checklist;
		}
		return c;
	});

	currentIndex = 1;
	filteredChecklist.questions = filteredChecklist.questions.map((section) => {
		let newSection = {...section};
		let unfillSection = {...section, data: []};
		let satisfySection = {...section, data: []};
		let followUpSection = {...section, data: []};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(selectedData.indexOf(newItem._id) > -1){
				newItem.status = label;
				newItem.selectOptions = action.option.slice(0);
				// console.log(newItem);
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;

			if (newItem.status == 'Unfill') {
				unfillSection.data.push(newItem);
			} else if (newItem.status == 'Satisfy') {
				satisfySection.data.push(newItem);
			} else if (newItem.status == 'FollowUp') {
				followUpSection.data.push(newItem);
			}
	
			return newItem;
		});
		if (unfillSection.data.length > 0) {
			unfillData.push(unfillSection);
		}
		if (satisfySection.data.length > 0) {
			satisfyData.push(satisfySection);
		}

		if (followUpSection.data.length > 0) {
			followUpData.push(followUpSection);
		}
		return newSection;    
	});

	// let unfillData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Unfill');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	// let satisfyData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Satisfy');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	// let followUpData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'FollowUp');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);
	
	//console.log(checklists, checklist, checklist.questions, satisfyData, followUpData );

	LocalDB.save('Checklist',checklist);

	let checkUpdatedinDetail = action.checkUpdatedinDetail;

	if(action.from == 'checklistDetail') {
		checkUpdatedinDetail++;
	}
	

	// console.log(checklist.questions)

	return { ...state, checklists: checklists, checklist: checklist, sectionData: checklist.questions, unfillData: unfillData,
		 satisfyData: satisfyData, followUpData: followUpData, filteredChecklist: filteredChecklist, 
		 filteredChecklistTotal:  currentIndex - 1, checkUpdatedinDetail: checkUpdatedinDetail, downloadedChecklists: downloadedChecklists };	
}

export const updateLabelData = (state = INITIAL_STATE, action) => {
	
	return { ...state, labelData: action.data };	
}

export const updateFloorPlanAnnotations = (state = INITIAL_STATE, action) => {
	let annotations = action.annotations;
	let id = action.id;

	let checklist = state.checklist;
	let currentIndex = 1;

	checklist.questions = checklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(newItem._id == id){
				newItem.annotations = annotations;
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		})
		return newSection;   
	});


	let checklists = state.checklists.map((c) =>{
		if(c._id == checklist.id){
			c = checklist;
		}
		return c;
	});

	let filteredChecklist = state.filteredChecklist;
	currentIndex = 1;
	filteredChecklist.questions = filteredChecklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(newItem._id == id){
				newItem.annotations = annotations;
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		})
		return newSection;   
	});

	LocalDB.save('Checklist',checklist);

	let checkUpdatedinDetail = action.checkUpdatedinDetail;
	
	if(action.from == 'checklistDetail') {
		checkUpdatedinDetail++;
	}

	return { ...state, checklists: checklists, checklist: checklist, sectionData: checklist.questions, filteredChecklist: filteredChecklist, filteredChecklistTotal:  currentIndex - 1, checkUpdatedinDetail: checkUpdatedinDetail   };	
}

export const updateItemImages = (state = INITIAL_STATE, action) => {
	
	let imagePaths = action.paths;
	let id = action.id;

	let checklist = state.checklist;
	let currentIndex = 1;

	checklist.questions = checklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(newItem._id == id){
				newItem.images = imagePaths;
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		})
		return newSection;   
	});


	let checklists = state.checklists.map((c) =>{
		if(c._id == checklist.id){
			c = checklist;
		}
		return c;
	});

	let filteredChecklist = state.filteredChecklist;
	currentIndex = 1;
	filteredChecklist.questions = filteredChecklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(newItem._id == id){
				newItem.images = imagePaths;
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		})
		return newSection;   
	});

	LocalDB.save('Checklist',checklist);

	let checkUpdatedinDetail = action.checkUpdatedinDetail;
	
	if(action.from == 'checklistDetail') {
		checkUpdatedinDetail++;
	}

	return { ...state, checklists: checklists, checklist: checklist, sectionData: checklist.questions, filteredChecklist: filteredChecklist, filteredChecklistTotal:  currentIndex - 1, checkUpdatedinDetail: checkUpdatedinDetail   };	
}

export const reCalLabelData = (state = INITIAL_STATE, action) => {

	let labelData = {};
	let lengthForEachSection = 0;
	let checklist = state.checklist;
	let total = 0;
	let lengthForUnfillSection = 0;
	let lengthForSatisfySection = 0;
	let lengthForFollowUpSection = 0;
	
	// checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	total += newSection.data.length;
	// 	newSection.data = newSection.data.filter(item => item.status == 'Unfill');
	// 	lengthForUnfillSection += newSection.data.length;
	// 	return newSection;
	// });

	// checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Satisfy');
	// 	lengthForSatisfySection += newSection.data.length;
	// 	return newSection;
	// });

	// checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'FollowUp');
	// 	lengthForFollowUpSection += newSection.data.length;
	// 	return newSection;
	// });

	checklist.questions.forEach((section) => {
		total += section.data.length;
		section.data.forEach((item) => {
			if (item.status == 'Unfill') {
				lengthForUnfillSection += 1;
			} else if (item.status == 'Satisfy') {
				lengthForSatisfySection += 1;
			} else if (item.status == 'FollowUp') {
				lengthForFollowUpSection += 1;
			}
		});
	});

	labelData.Unfill = lengthForUnfillSection;

	labelData.Satisfy = lengthForSatisfySection;

	labelData.FollowUp = lengthForFollowUpSection;

	labelData.total = total;
	
	return { ...state, labelData: labelData };	
}


export const updateOtherText = (state = INITIAL_STATE, action) => {
	
	let otherText = action.text;
	let id = action.id;

	let checklist = state.checklist;
	let currentIndex = 1;

	checklist.questions = checklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(newItem._id == id){
				newItem.otherText = otherText;
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		})
		return newSection;   
	});


	let checklists = state.checklists.map((c) =>{
		if(c._id == checklist.id){
			c = checklist;
		}
		return c;
	});


	let filteredChecklist = state.filteredChecklist;
	currentIndex = 1;
	filteredChecklist.questions = filteredChecklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(newItem._id == id){
				newItem.otherText = otherText;
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		})
		return newSection;   
	});


	// let unfillData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Unfill');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	// let satisfyData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Satisfy');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	// let followUpData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'FollowUp');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	let unfillData = [];
	let satisfyData = [];
	let followUpData = [];

	filteredChecklist.questions.forEach((section) => {
		let newSection = {...section};
		let unfillSection = {...section, data: []};
		let satisfySection = {...section, data: []};
		let followUpSection = {...section, data: []};

		newSection.data.forEach((item, i)=>{
			if (item.status == 'Unfill') {
				unfillSection.data.push(item);
			} else if (item.status == 'Satisfy') {
				satisfySection.data.push(item);
			} else if (item.status == 'FollowUp') {
				followUpSection.data.push(item);
			}
		});
		
		if (unfillSection.data.length > 0) {
			unfillData.push(unfillSection);
		}
		if (satisfySection.data.length > 0) {
			satisfyData.push(satisfySection);
		}

		if (followUpSection.data.length > 0) {
			followUpData.push(followUpSection);
		}
	});
	
	let checkUpdatedinDetail = action.checkUpdatedinDetail;
	
	if(action.from == 'checklistDetail') {
		checkUpdatedinDetail++;
	}

	// console.log(checklists, checklist, sectionData, checklist.questions, satisfyData, followUpData );

	LocalDB.save('Checklist',checklist);

	return { ...state, checklists: checklists, checklist: checklist, sectionData: checklist.questions, unfillData: unfillData, satisfyData: satisfyData, followUpData: followUpData, filteredChecklist: filteredChecklist, filteredChecklistTotal:  currentIndex - 1, checkUpdatedinDetail: checkUpdatedinDetail   };	
}

export const updateComments = (state = INITIAL_STATE, action) => {
	
	let comments = action.text;
	let id = action.id;

	let checklist = state.checklist;
	let currentIndex = 1;

	checklist.questions = checklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(newItem._id == id){
				newItem.comments = comments;
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		})
		return newSection;   
	});


	let filteredChecklist = state.filteredChecklist;
	currentIndex = 1;
	filteredChecklist.questions = filteredChecklist.questions.map((section) => {
		let newSection = {...section};
		newSection.data = newSection.data.map((item, i)=>{
			let newItem = {...item};
			if(newItem._id == id){
				newItem.comments = comments;
			}

			newItem.currentIndex = currentIndex;
			currentIndex++;
			return newItem;
		})
		return newSection;   
	});


	let checklists = state.checklists.map((c) =>{
		if(c._id == checklist.id){
			c = checklist;
		}
		return c;
	});

	// let unfillData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Unfill');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	// let satisfyData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'Satisfy');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	// let followUpData = checklist.questions.map( section => {
	// 	let newSection = {...section};
	// 	newSection.data = newSection.data.filter(item => item.status == 'FollowUp');
	// 	return newSection;
	// }).filter( section => section.data.length > 0);

	let unfillData = [];
	let satisfyData = [];
	let followUpData = [];

	filteredChecklist.questions.forEach((section) => {
		let newSection = {...section};
		let unfillSection = {...section, data: []};
		let satisfySection = {...section, data: []};
		let followUpSection = {...section, data: []};

		newSection.data.forEach((item, i)=>{
			if (item.status == 'Unfill') {
				unfillSection.data.push(item);
			} else if (item.status == 'Satisfy') {
				satisfySection.data.push(item);
			} else if (item.status == 'FollowUp') {
				followUpSection.data.push(item);
			}
		});
		
		if (unfillSection.data.length > 0) {
			unfillData.push(unfillSection);
		}
		if (satisfySection.data.length > 0) {
			satisfyData.push(satisfySection);
		}

		if (followUpSection.data.length > 0) {
			followUpData.push(followUpSection);
		}
	});

	let checkUpdatedinDetail = action.checkUpdatedinDetail;
	
	if(action.from == 'checklistDetail') {
		checkUpdatedinDetail++;
	}
	
	// console.log(checklists, checklist, sectionData, checklist.questions, satisfyData, followUpData );

	LocalDB.save('Checklist',checklist);

	return { ...state, checklists: checklists, checklist: checklist, sectionData: checklist.questions, unfillData: unfillData, satisfyData: satisfyData, followUpData: followUpData, filteredChecklist: filteredChecklist, filteredChecklistTotal:  currentIndex - 1, checkUpdatedinDetail: checkUpdatedinDetail   };	
}


export const pendingCheckLists = (state = INITIAL_STATE, action) => {

	action.checklist.saved_at = new Date(); // only used in app locally

	let newChecklists = []; 

	newChecklists = state.checklists.filter((item, i)=>{
		return (item._id  != action.checklist._id);
	});

	newDownloadChecklists = state.downloadedChecklists.filter((item, i)=>{
		return (item._id  != action.checklist._id);
	});

	let pendingCheckLists = state.pendingCheckLists.slice();
	let newUnit = pendingCheckLists.find(c => c._id === action.checklist._id);
	if (!newUnit){
		action.checklist.labelData = state.labelData;
		pendingCheckLists.push(action.checklist);
	}

	// console.log("newChecklists", newChecklists);

	// console.log("save to DB", action.checklist);
	// console.log("save to newArray", newArray);
	LocalDB.save('PendingCheckList', action.checklist);
	LocalDB.delete('Checklist', action.checklist._id );

	return { ...state, pendingCheckLists: pendingCheckLists, checklists: newChecklists, downloadedChecklists: newDownloadChecklists};	
}

export const fetchedPendingCheckLists = (state = INITIAL_STATE, action) => {
	// console.log("action.data", action.data);

	return { ...state, pendingCheckLists: action.data };
}

export const removePendingCheckLists = (state = INITIAL_STATE, action) => {

	let pendingCheckLists = state.pendingCheckLists.slice();
	let submittedData = action.data;
	let filterAdhocData = [];
	let fitlerNormalData =  [];

	pendingCheckLists.forEach((pendingChecklist)=> {
		pendingChecklist.questions.forEach((q)=> {
			if(pendingChecklist.source) {
				submittedData.forEach((data) => {
					if(pendingChecklist._id  == data.checklistId && q._id == data.originalId) {
						filterAdhocData.push(q._id)
					}
				})
			} else {
				q.data.forEach((eachData)=> {
					submittedData.forEach((data) => {
						if(pendingChecklist._id  == data.checklistId && eachData._id == data.originalId) {
							fitlerNormalData.push(eachData._id)
						}
					})
				});
			}
		})
	})

	pendingCheckLists = pendingCheckLists.filter((pendingChecklist)=> {
		if (pendingChecklist.source) {
			pendingChecklist.questions = pendingChecklist.questions.filter((q)=> {
				return !filterAdhocData.includes(q._id)
			})
			if (pendingChecklist.questions.length > 0){
				LocalDB.save('PendingCheckList', pendingChecklist);
				return pendingChecklist
			} else {
				// LocalDB.delete('PendingCheckList', pendingChecklist._id );
			}
		} else {
			pendingChecklist.questions = pendingChecklist.questions.filter((q)=> {
				q.data = q.data.filter((eachData)=> {
					return (!fitlerNormalData.includes(eachData._id) && eachData.status != "Unfill")
				})

				if(q.data.length > 0){
					return q
				}
			})

			// console.log(pendingChecklist);
			if (pendingChecklist.questions.length > 0){
				LocalDB.save('PendingCheckList', pendingChecklist);
				return pendingChecklist
			} else {
				// LocalDB.delete('PendingCheckList', pendingChecklist._id );
			}
		}
	});

	// console.log("final", pendingCheckLists);

	return { ...state,  pendingCheckLists: pendingCheckLists};
}


export const filterTask = (state = INITIAL_STATE, action) => {
	// filter for TaskDownload
	let filterData = action.data.filter((c) => !c.questions);
	return { ...state, filterData: filterData };
}

export const fakeUpdateTaskData = (state = INITIAL_STATE, action) => {
	
	return { ...state, filterData: action.data };	
}

export const getFormSubmissionFilter = (state = INITIAL_STATE, action) => {
	
	return { ...state, formSubmission_options: action.formSubmission_options };
}

export const resetChecklist = (state = INITIAL_STATE, action) => {
	
	return { ...state, checklist: {} };
}

export const modifyAddHocCheckList = (state = INITIAL_STATE, action) => {
	let checklist = {};
	let checklists = state.checklists.slice();

	checklists.find((item)=>{
		if(item._id == action.data.id) {
			item.questions.find((question)=>{
				if(question._id == action.data.questionId) {
					question.description = action.data.remarks;
					question.options = [];
					question.options.push(action.data.remarks);
					question.selectOptions = [];
					question.selectOptions.push(action.data.remarks);
					question.location = action.data.location;
					question.level1 = action.data.level1;
					question.level2 = action.data.level2;
					question.critical = action.data.isImportant;
					question.selected = false;
					question.images = action.data.images;
					question.unit = action.data.unit;
					question.annotations = action.data.annotations;
					return true;
				}
				return false;
			});
			checklist = item;
			return true;
		}
		return false;
	})

	
	LocalDB.save('Checklist',checklist);
	return { ...state, checklists: checklists, checklist: Object.assign({}, checklist )};
}

export const createAddHocCheckList = (state = INITIAL_STATE, action) => {
	let checklist = {};
	let question = {};

	let localDate = new Date();
	let randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
	let uniqQuestionId = "_Q" + randLetter + Date.now();

	question._id = uniqQuestionId;
	question.description = action.data.remarks;
	question.options = [];
	question.options.push(action.data.remarks);
	question.selectOptions = [];
	question.selectOptions.push(action.data.remarks);
	question.location = action.data.location;
	question.level1 = action.data.level1;
	question.level2 = action.data.level2;
	question.critical = action.data.isImportant;
	question.selected = false;
	question.images = action.data.images;
	question.unit = action.data.unit;
	question.annotations = action.data.annotations;

	let checklists = state.checklists.slice();
	let downloadedChecklists = state.downloadedChecklists.slice();
	
	if(!action.data.id) {
		let uniqChecklistId = "_C" + randLetter + Date.now();

		checklist._id = uniqChecklistId;
		checklist.flat =  action.data.flat;
		checklist.floor = action.data.floor;
		checklist.name =  action.data.firstOption;
		checklist.tower = action.data.tower;
		checklist._created_at = moment.utc( localDate ).format();
		checklist._updated_at = moment.utc( localDate ).format();
		checklist.source = action.data.secondOption;
		checklist.label = action.data.label;
		checklist.questions = [];
		checklist.questions.push(question);
		checklist.type = action.data.type;
		checklist.unit = action.data.unit;

		downloadedChecklists.push(checklist);
	}else {
		// checklists.find((item)=>{
		// 	if(item._id == action.data.id) {
		// 		item.questions.push(question);
		// 		checklist = item;
		// 		return true;
		// 	}
		// 	return false;
		// })
		downloadedChecklists.find((item)=>{
			if(item._id == action.data.id) {
				item.questions.push(question);
				checklist = item;
				return true;
			}
			return false;
		})
	}

	LocalDB.save('Checklist',checklist);

	return { ...state, checklists:  checklists, checklist: Object.assign({}, checklist), downloadedChecklists: downloadedChecklists };
}

export const deleteAdhocData = (state = INITIAL_STATE, action) => {
	let checklist = {};
	let checklists = state.checklists.slice();
	checklists.find((item)=>{
		if(item._id == action.id) {
			item.questions = item.questions.filter((question)=>{
				return !action.data.includes(question._id)
			})
			return true;
		}
		return false;
	})

	checklists = checklists.filter(item => item.questions.length > 0);	

	let downloadedChecklists = state.downloadedChecklists.slice();
	downloadedChecklists.find((item)=>{
		if(item._id == action.id) {
			item.questions = item.questions.filter((question)=>{
				return !action.data.includes(question._id)
			})
			checklist = item;
			return true;
		}
		return false;
	})

	downloadedChecklists = downloadedChecklists.filter(item => item.questions.length > 0);	
	if(checklist.questions.length > 0) {
		LocalDB.save('Checklist',checklist);
	}else {
		LocalDB.delete('Checklist', checklist._id );
		checklist = {};
	}
	
	return { ...state, checklists: checklists, checklist: Object.assign({}, checklist), downloadedChecklists: downloadedChecklists  };
}


export const clearChecklists = (state = INITIAL_STATE, action) => {

	return { ...state,  downloadedChecklists: []  };
}

export const updateExpandedChecklist = (state = INITIAL_STATE, action) => {
	
	let viewUnitCheckList = state.viewUnitCheckList.slice();
	viewUnitCheckList.map((item)=>{
		if(item._id == action.id) {
			item.clickExpanded = !item.clickExpanded;
		}
		return item;
	})

	return { ...state, viewUnitCheckList: viewUnitCheckList };	
}

export const HANDLERS = {
	[Types.FETCHED]: fetchedChecklists,
	[Types.LOADED]: loadedChecklists,
	[Types.SELECTED]: seletedChecklists,
	[Types.MODIFY_CHECK_LIST]: modifyCheckList,
	[Types.MODIFY_UNIT_CHECK_LIST]: modifyUnitCheckList,
	[Types.MODIFY_ADHOC_CHECK_LIST]: modifyAdhocCheckList,
	[Types.VIEW_UNIT_CHECK_LIST]: viewUnitCheckList,
	[Types.FILTER_CHECK_LIST]: filterCheckList,
	[Types.FAKE_UPDATE_UNFILL_DATA]: fakeUpdateUnfillData,
	[Types.FAKE_UPDATE_ADHOC_DATA]: fakeUpdateAdhocData,
	[Types.UPDATE_SELECT_OPTIONS]: updateSelectOptions,
	[Types.UPDATE_LABEL_DATA]: updateLabelData,
	[Types.UPDATE_FLOOR_PLAN_ANNOTATIONS]: updateFloorPlanAnnotations,
	[Types.UPDATE_ITEM_IMAGES]: updateItemImages,
	[Types.RE_CAL_LABEL_DATA]: reCalLabelData,
	[Types.UPDATE_OTHER_TEXT]: updateOtherText,
	[Types.UPDATE_COMMENTS]: updateComments,
	[Types.PENDING_CHECK_LISTS]: pendingCheckLists,
	[Types.FETCHED_PENDING_CHECK_LISTS]: fetchedPendingCheckLists,
	[Types.REMOVE_PENDING_CHECK_LISTS]: removePendingCheckLists,
	[Types.FILTER_TASK]: filterTask,
	[Types.FAKE_UPDATE_TASK_DATA]: fakeUpdateTaskData,
	[Types.GET_FORM_SUBMISSION_FILTER]: getFormSubmissionFilter,
	[Types.CREATE_ADD_HOC_CHECK_LIST]: createAddHocCheckList,
	[Types.MODIFY_ADD_HOC_CHECK_LIST]: modifyAddHocCheckList,
	[Types.RESET_CHECKLIST]: resetChecklist,
	[Types.DELETE_ADHOC_DATA]: deleteAdhocData,
	[Types.CLEAR_CHECKLISTS]: clearChecklists,
	[Types.UPDATE_EXPANDED_CHECKLIST]: updateExpandedChecklist,
}

module.exports = createReducer(INITIAL_STATE, HANDLERS);